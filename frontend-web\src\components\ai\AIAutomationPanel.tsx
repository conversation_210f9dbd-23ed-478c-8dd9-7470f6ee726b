import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Grid,
  Chip,
  LinearProgress,
  Alert,
  IconButton,
  <PERSON>lt<PERSON>,
  useTheme,
  alpha
} from '@mui/material';
import {
  Psychology,
  AutoAwesome,
  SmartToy,
  Analytics,
  PlayArrow,
  Stop,
  Refresh,
  Settings,
  TrendingUp,
  Warning,
  CheckCircle
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';

interface AITask {
  id: string;
  name: string;
  description: string;
  status: 'idle' | 'running' | 'completed' | 'error';
  progress: number;
  icon: React.ReactNode;
  category: 'analysis' | 'automation' | 'prediction' | 'optimization';
  estimatedTime: string;
  lastRun?: string;
}

const AIAutomationPanel: React.FC = () => {
  const theme = useTheme();
  const [runningTasks, setRunningTasks] = useState<Set<string>>(new Set());
  const [completedTasks, setCompletedTasks] = useState<Set<string>>(new Set());

  const aiTasks: AITask[] = [
    {
      id: 'health-analysis',
      name: 'Health Analysis',
      description: 'Analyze all animal health data and predict potential issues',
      status: 'idle',
      progress: 0,
      icon: <Psychology sx={{ color: '#2196F3' }} />,
      category: 'analysis',
      estimatedTime: '2-3 minutes',
      lastRun: '2 hours ago'
    },
    {
      id: 'feed-optimization',
      name: 'Feed Optimization',
      description: 'Optimize feeding schedules and automatically order supplies',
      status: 'idle',
      progress: 0,
      icon: <AutoAwesome sx={{ color: '#FF9800' }} />,
      category: 'automation',
      estimatedTime: '1-2 minutes',
      lastRun: '4 hours ago'
    },
    {
      id: 'breeding-prediction',
      name: 'Breeding Predictions',
      description: 'Predict optimal breeding times and genetic outcomes',
      status: 'idle',
      progress: 0,
      icon: <TrendingUp sx={{ color: '#4CAF50' }} />,
      category: 'prediction',
      estimatedTime: '3-5 minutes',
      lastRun: '1 day ago'
    },
    {
      id: 'vet-scheduling',
      name: 'Vet Appointment Automation',
      description: 'Automatically schedule veterinary appointments based on health data',
      status: 'idle',
      progress: 0,
      icon: <SmartToy sx={{ color: '#9C27B0' }} />,
      category: 'automation',
      estimatedTime: '30 seconds',
      lastRun: '6 hours ago'
    },
    {
      id: 'financial-forecast',
      name: 'Financial Forecasting',
      description: 'Generate financial projections and cost optimization recommendations',
      status: 'idle',
      progress: 0,
      icon: <Analytics sx={{ color: '#F44336' }} />,
      category: 'analysis',
      estimatedTime: '2-4 minutes',
      lastRun: 'Never'
    },
    {
      id: 'inventory-management',
      name: 'Smart Inventory Management',
      description: 'Automatically manage inventory levels and reorder supplies',
      status: 'idle',
      progress: 0,
      icon: <AutoAwesome sx={{ color: '#607D8B' }} />,
      category: 'optimization',
      estimatedTime: '1 minute',
      lastRun: '3 hours ago'
    }
  ];

  const runTask = async (taskId: string) => {
    setRunningTasks(prev => new Set(Array.from(prev).concat(taskId)));

    // Simulate AI task execution
    const task = aiTasks.find(t => t.id === taskId);
    if (!task) return;

    // Simulate progress
    for (let i = 0; i <= 100; i += 10) {
      await new Promise(resolve => setTimeout(resolve, 200));
      // Update progress would go here in real implementation
    }

    setRunningTasks(prev => {
      const newSet = new Set(Array.from(prev));
      newSet.delete(taskId);
      return newSet;
    });

    setCompletedTasks(prev => new Set(Array.from(prev).concat(taskId)));

    // Reset completed status after 3 seconds
    setTimeout(() => {
      setCompletedTasks(prev => {
        const newSet = new Set(Array.from(prev));
        newSet.delete(taskId);
        return newSet;
      });
    }, 3000);
  };

  const runAllTasks = async () => {
    for (const task of aiTasks) {
      if (!runningTasks.has(task.id)) {
        await runTask(task.id);
        await new Promise(resolve => setTimeout(resolve, 500)); // Delay between tasks
      }
    }
  };

  const getTaskStatus = (taskId: string) => {
    if (runningTasks.has(taskId)) return 'running';
    if (completedTasks.has(taskId)) return 'completed';
    return 'idle';
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'analysis': return '#2196F3';
      case 'automation': return '#FF9800';
      case 'prediction': return '#4CAF50';
      case 'optimization': return '#9C27B0';
      default: return '#757575';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" fontWeight="bold" sx={{ mb: 1 }}>
            🤖 AI Automation Center
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Enterprise Pro AI-powered automation and analysis tools
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="contained"
            startIcon={<PlayArrow />}
            onClick={runAllTasks}
            disabled={runningTasks.size > 0}
            sx={{
              background: 'linear-gradient(45deg, #9C27B0, #7B1FA2)',
              '&:hover': {
                background: 'linear-gradient(45deg, #7B1FA2, #6A1B9A)',
              }
            }}
          >
            Run All Tasks
          </Button>
          <IconButton>
            <Settings />
          </IconButton>
        </Box>
      </Box>

      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>Enterprise Pro Features:</strong> One-click AI automation, predictive analytics, and intelligent task management. 
          These tools analyze your farm data and automatically perform optimizations.
        </Typography>
      </Alert>

      <Grid container spacing={3}>
        {aiTasks.map((task, index) => {
          const status = getTaskStatus(task.id);
          const isRunning = status === 'running';
          const isCompleted = status === 'completed';
          
          return (
            <Grid item xs={12} md={6} lg={4} key={task.id}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card
                  sx={{
                    height: '100%',
                    background: alpha(theme.palette.background.paper, 0.8),
                    backdropFilter: 'blur(10px)',
                    border: `1px solid ${alpha(getCategoryColor(task.category), 0.3)}`,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: `0 8px 25px ${alpha(getCategoryColor(task.category), 0.2)}`,
                    }
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {task.icon}
                        <Chip
                          label={task.category}
                          size="small"
                          sx={{
                            backgroundColor: alpha(getCategoryColor(task.category), 0.1),
                            color: getCategoryColor(task.category),
                            fontWeight: 'bold'
                          }}
                        />
                      </Box>
                      <Box>
                        {isCompleted && (
                          <CheckCircle sx={{ color: '#4CAF50', fontSize: 20 }} />
                        )}
                        {isRunning && (
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                          >
                            <Refresh sx={{ color: '#2196F3', fontSize: 20 }} />
                          </motion.div>
                        )}
                      </Box>
                    </Box>

                    <Typography variant="h6" fontWeight="bold" sx={{ mb: 1 }}>
                      {task.name}
                    </Typography>
                    
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2, minHeight: 40 }}>
                      {task.description}
                    </Typography>

                    <Box sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="caption" color="text.secondary">
                          Estimated time: {task.estimatedTime}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Last run: {task.lastRun}
                        </Typography>
                      </Box>
                      
                      <AnimatePresence>
                        {isRunning && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                          >
                            <LinearProgress
                              sx={{
                                height: 6,
                                borderRadius: 3,
                                backgroundColor: alpha(getCategoryColor(task.category), 0.2),
                                '& .MuiLinearProgress-bar': {
                                  backgroundColor: getCategoryColor(task.category),
                                }
                              }}
                            />
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </Box>

                    <Button
                      fullWidth
                      variant={isCompleted ? "outlined" : "contained"}
                      startIcon={isRunning ? <Stop /> : <PlayArrow />}
                      onClick={() => runTask(task.id)}
                      disabled={isRunning}
                      sx={{
                        backgroundColor: isCompleted ? 'transparent' : getCategoryColor(task.category),
                        borderColor: getCategoryColor(task.category),
                        color: isCompleted ? getCategoryColor(task.category) : 'white',
                        '&:hover': {
                          backgroundColor: isCompleted 
                            ? alpha(getCategoryColor(task.category), 0.1)
                            : alpha(getCategoryColor(task.category), 0.8),
                        }
                      }}
                    >
                      {isRunning ? 'Running...' : isCompleted ? 'Completed' : 'Run Task'}
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          );
        })}
      </Grid>

      <Box sx={{ mt: 4, p: 3, backgroundColor: alpha('#9C27B0', 0.1), borderRadius: 2 }}>
        <Typography variant="h6" fontWeight="bold" sx={{ mb: 2, color: '#9C27B0' }}>
          🚀 Pro AI Features
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="body2" fontWeight="bold">Predictive Analysis</Typography>
            <Typography variant="caption" color="text.secondary">
              AI predicts health issues, breeding outcomes, and market trends
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="body2" fontWeight="bold">One-Click Automation</Typography>
            <Typography variant="caption" color="text.secondary">
              Automate feed ordering, vet appointments, and routine tasks
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="body2" fontWeight="bold">Smart Optimization</Typography>
            <Typography variant="caption" color="text.secondary">
              AI optimizes costs, schedules, and resource allocation
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="body2" fontWeight="bold">Intelligent Agents</Typography>
            <Typography variant="caption" color="text.secondary">
              AI agents handle complex multi-step operations automatically
            </Typography>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
};

export default AIAutomationPanel;
