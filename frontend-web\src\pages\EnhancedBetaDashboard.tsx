import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Button,
  Card,
  CardContent,
  Grid,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useTheme,
  alpha,
  Paper,
  AppBar,
  Toolbar,
  IconButton,
  Drawer,
  ListItemButton
} from '@mui/material';
import {
  Pets,
  LocalHospital,
  MenuBook,
  Lock,
  Star,
  Menu,
  Close,
  Dashboard,
  Upgrade,
  PlayArrow,
  Favorite,
  Restaurant,
  AccountBalance,
  Inventory,
  Business,
  Assessment,
  Gavel,
  Analytics,
  Settings
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import EnhancedGradientBackground from '../components/common/EnhancedGradientBackground';
import AgriIntelBrand from '../components/branding/AgriIntelBrand';
import { getModulesByPlan, getLockedModules, subscriptionPlans } from '../config/subscriptionConfig';

const EnhancedBetaDashboard: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [upgradeDialog, setUpgradeDialog] = useState(false);

  // Get modules based on subscription plan
  const currentPlan = 'beta';
  const availableModules = getModulesByPlan(currentPlan);
  const lockedModules = getLockedModules(currentPlan);
  const betaPlan = subscriptionPlans.find(p => p.id === 'beta');
  const professionalPlan = subscriptionPlans.find(p => p.id === 'professional');
  const enterprisePlan = subscriptionPlans.find(p => p.id === 'enterprise');

  // Icon mapping
  const iconMap: Record<string, any> = {
    Dashboard,
    Pets,
    LocalHospital,
    MenuBook,
    Favorite,
    Restaurant,
    AccountBalance,
    Inventory,
    Business,
    Assessment,
    Gavel,
    Analytics,
    Settings
  };

  // Color mapping for modules
  const colorMap: Record<string, string> = {
    dashboard: theme.palette.primary.main,
    animals: '#4caf50',
    health: '#f44336',
    resources: '#ff9800',
    breeding: '#e91e63',
    feeding: '#ff5722',
    financial: '#2196f3',
    inventory: '#9c27b0',
    commercial: '#607d8b',
    reports: '#795548',
    compliance: '#3f51b5',
    analytics: '#009688',
    settings: '#424242'
  };

  const handleModuleClick = (module: any) => {
    navigate(module.path);
  };

  const handleLockedModuleClick = () => {
    setUpgradeDialog(true);
  };

  const sidebarContent = (
    <Box sx={{ width: 280, p: 2 }}>
      <Box sx={{ mb: 3 }}>
        <AgriIntelBrand variant="compact" size="medium" />
        <Chip
          label="BETA VERSION"
          color="primary"
          size="small"
          sx={{ mt: 1, fontSize: '0.7rem' }}
        />
      </Box>

      <Typography variant="h6" sx={{ mb: 2, color: 'success.main' }}>
        Available Modules
      </Typography>

      <List>
        {availableModules.map((module) => {
          const IconComponent = iconMap[module.icon];
          const moduleColor = colorMap[module.id];

          return (
            <ListItemButton
              key={module.id}
              onClick={() => handleModuleClick(module)}
              sx={{
                borderRadius: 2,
                mb: 1,
                '&:hover': {
                  backgroundColor: alpha(moduleColor, 0.1)
                }
              }}
            >
              <ListItemIcon>
                <IconComponent sx={{ color: moduleColor }} />
              </ListItemIcon>
              <ListItemText
                primary={module.title}
                secondary={module.limitations?.beta || module.description}
              />
            </ListItemButton>
          );
        })}
      </List>

      <Typography variant="h6" sx={{ mb: 2, mt: 3, color: 'warning.main' }}>
        Locked Modules
      </Typography>

      <List>
        {lockedModules.map((module) => {
          const requiredPlan = module.requiredPlan === 'professional' ? professionalPlan : enterprisePlan;

          return (
            <ListItemButton
              key={module.id}
              onClick={handleLockedModuleClick}
              sx={{
                borderRadius: 2,
                mb: 1,
                opacity: 0.6,
                '&:hover': {
                  backgroundColor: alpha(theme.palette.warning.main, 0.1),
                  opacity: 0.8
                }
              }}
            >
              <ListItemIcon>
                <Lock sx={{ color: 'warning.main' }} />
              </ListItemIcon>
              <ListItemText
                primary={module.title}
                secondary={`🔒 ${requiredPlan?.name} - ${requiredPlan?.price}/month`}
              />
            </ListItemButton>
          );
        })}
      </List>

      <Button
        variant="contained"
        fullWidth
        onClick={() => setUpgradeDialog(true)}
        sx={{ mt: 3 }}
        startIcon={<Upgrade />}
      >
        Upgrade to Premium
      </Button>
    </Box>
  );

  return (
    <EnhancedGradientBackground
      module="beta"
      variant="mesh"
      enableAnimation={true}
      enableParticles={true}
      opacity={0.7}
    >
      {/* App Bar */}
      <AppBar position="fixed" sx={{ zIndex: theme.zIndex.drawer + 1 }}>
        <Toolbar>
          <IconButton
            color="inherit"
            onClick={() => setSidebarOpen(true)}
            sx={{ mr: 2 }}
          >
            <Menu />
          </IconButton>
          
          <AgriIntelBrand variant="compact" size="small" color="white" />
          
          <Chip 
            label="BETA" 
            color="secondary" 
            size="small" 
            sx={{ ml: 2 }}
          />
          
          <Box sx={{ flexGrow: 1 }} />
          
          <Button
            color="inherit"
            onClick={() => setUpgradeDialog(true)}
            startIcon={<Star />}
          >
            Upgrade
          </Button>
          
          <Button
            color="inherit"
            onClick={() => navigate('/')}
          >
            Exit Beta
          </Button>
        </Toolbar>
      </AppBar>

      {/* Sidebar */}
      <Drawer
        anchor="left"
        open={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
      >
        {sidebarContent}
      </Drawer>

      {/* Main Content */}
      <Container maxWidth="xl" sx={{ mt: 10, py: 4 }}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Welcome Section */}
          <Paper sx={{ p: 4, mb: 4, borderRadius: 3 }}>
            <Typography variant="h3" fontWeight="bold" sx={{ mb: 2 }}>
              Welcome to AgriIntel Beta! 🌾
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ mb: 3 }}>
              You're experiencing the future of livestock management. 
              Explore our beta features and see how AgriIntel can transform your farm.
            </Typography>
            
            <Alert severity="info" sx={{ mb: 3 }}>
              <Typography variant="body1">
                <strong>Beta Access Includes:</strong> {betaPlan?.features.join(', ')}
              </Typography>
            </Alert>

            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Button
                variant="contained"
                size="large"
                onClick={() => navigate('/dashboard/animals')}
                startIcon={<PlayArrow />}
              >
                Start Managing Animals
              </Button>
              <Button
                variant="outlined"
                size="large"
                onClick={() => setUpgradeDialog(true)}
                startIcon={<Upgrade />}
              >
                See Premium Features
              </Button>
            </Box>
          </Paper>

          {/* Available Modules Grid */}
          <Typography variant="h4" fontWeight="bold" sx={{ mb: 3 }}>
            Available Beta Modules
          </Typography>
          
          <Grid container spacing={3} sx={{ mb: 4 }}>
            {availableModules.map((module, index) => {
              const IconComponent = iconMap[module.icon];
              const moduleColor = colorMap[module.id];

              return (
                <Grid item xs={12} sm={6} md={3} key={module.id}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card
                      sx={{
                        height: '100%',
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          transform: 'translateY(-8px)',
                          boxShadow: `0 12px 24px ${alpha(moduleColor, 0.2)}`
                        }
                      }}
                      onClick={() => handleModuleClick(module)}
                    >
                      <CardContent sx={{ p: 3, textAlign: 'center' }}>
                        <Box
                          sx={{
                            p: 2,
                            borderRadius: 3,
                            backgroundColor: alpha(moduleColor, 0.1),
                            display: 'inline-block',
                            mb: 2
                          }}
                        >
                          <IconComponent sx={{ fontSize: 40, color: moduleColor }} />
                        </Box>
                        <Typography variant="h6" fontWeight="bold" sx={{ mb: 1 }}>
                          {module.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                          {module.description}
                        </Typography>
                        {module.limitations?.beta && (
                          <Typography variant="caption" color="warning.main" sx={{ display: 'block', mb: 1 }}>
                            {module.limitations.beta}
                          </Typography>
                        )}
                        <Chip
                          label="Available"
                          color="success"
                          size="small"
                          sx={{ mt: 1 }}
                        />
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              );
            })}
          </Grid>
        </motion.div>
      </Container>

      {/* Upgrade Dialog */}
      <Dialog 
        open={upgradeDialog} 
        onClose={() => setUpgradeDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Typography variant="h5" fontWeight="bold">
            🚀 Upgrade to Premium
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" sx={{ mb: 3 }}>
            Choose the perfect plan for your farm. Unlock advanced features and save thousands
            with professional livestock management.
          </Typography>

          <Grid container spacing={2}>
            {[professionalPlan, enterprisePlan].filter(Boolean).map((plan) => (
              <Grid item xs={12} sm={6} key={plan!.id}>
                <Card
                  sx={{
                    p: 3,
                    height: '100%',
                    border: plan!.popular ? '2px solid' : '1px solid',
                    borderColor: plan!.popular ? 'primary.main' : 'divider'
                  }}
                >
                  {plan!.popular && (
                    <Chip
                      label="Most Popular"
                      color="primary"
                      size="small"
                      sx={{ mb: 2 }}
                    />
                  )}
                  <Typography variant="h5" fontWeight="bold" sx={{ mb: 1 }}>
                    {plan!.name}
                  </Typography>
                  <Typography variant="h4" color="primary" fontWeight="bold" sx={{ mb: 1 }}>
                    {plan!.price}
                    <Typography component="span" variant="body2" color="text.secondary">
                      /{plan!.duration.split(' ')[1]}
                    </Typography>
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {plan!.description}
                  </Typography>
                  <List dense>
                    {plan!.features.slice(0, 4).map((feature, idx) => (
                      <ListItem key={idx} sx={{ px: 0, py: 0.5 }}>
                        <ListItemText
                          primary={feature}
                          primaryTypographyProps={{ variant: 'body2' }}
                        />
                      </ListItem>
                    ))}
                    {plan!.features.length > 4 && (
                      <ListItem sx={{ px: 0, py: 0.5 }}>
                        <ListItemText
                          primary={`+${plan!.features.length - 4} more features...`}
                          primaryTypographyProps={{
                            variant: 'body2',
                            fontStyle: 'italic',
                            color: 'text.secondary'
                          }}
                        />
                      </ListItem>
                    )}
                  </List>
                  <Button
                    variant={plan!.popular ? "contained" : "outlined"}
                    fullWidth
                    sx={{ mt: 2 }}
                    onClick={() => navigate('/register')}
                  >
                    Choose {plan!.name}
                  </Button>
                </Card>
              </Grid>
            ))}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUpgradeDialog(false)}>
            Maybe Later
          </Button>
          <Button
            variant="contained"
            onClick={() => navigate('/register')}
            startIcon={<Star />}
          >
            Start Free Trial
          </Button>
        </DialogActions>
      </Dialog>
    </EnhancedGradientBackground>
  );
};

export default EnhancedBetaDashboard;
