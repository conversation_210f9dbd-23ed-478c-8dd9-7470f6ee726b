import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Container,
  Alert,
  Chip,
  Paper,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  CheckCircle,
  Star,
  Agriculture,
  HealthAndSafety,
  Restaurant,
  Assessment,
  Support,
  PhoneAndroid
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import AgriIntelBrand from '../../components/branding/AgriIntelBrand';
import '../../styles/auth-pages.css';

const BetaLogin: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { login } = useAuth();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!username.trim() || !password.trim()) {
      setError('Please enter both username and password');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      await login(username, password);
      // Navigate to beta dashboard
      navigate('/dashboard', { replace: true });
    } catch (err: any) {
      setError(err.message || 'Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const betaFeatures = [
    { icon: <Agriculture />, title: 'Animal Management', description: 'Track up to 50 animals with basic profiles' },
    { icon: <HealthAndSafety />, title: 'Health Monitoring', description: 'Basic health records and vaccination tracking' },
    { icon: <Restaurant />, title: 'Feed Management', description: 'Simple feed consumption recording' },
    { icon: <Assessment />, title: 'Excel Reports', description: 'Export basic reports to Excel format' },
    { icon: <Support />, title: 'Community Support', description: 'Access to community forums and resources' },
    { icon: <PhoneAndroid />, title: 'Mobile Access', description: 'Mobile app access (coming soon)' }
  ];

  return (
    <Box className="auth-page beta-auth" sx={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%)',
      display: 'flex',
      alignItems: 'center',
      py: 4
    }}>
      <Container maxWidth="lg">
        <Grid container spacing={4} alignItems="center">
          {/* Login Form */}
          <Grid item xs={12} md={6}>
            <Card sx={{
              maxWidth: 500,
              mx: 'auto',
              boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
              borderRadius: 3,
              overflow: 'visible'
            }}>
              <CardContent sx={{ p: 4 }}>
                <Box sx={{ textAlign: 'center', mb: 4 }}>
                  <AgriIntelBrand variant="compact" size="large" color="primary" />
                  <Chip
                    label="BETA ACCESS"
                    color="success"
                    sx={{
                      mt: 2,
                      fontWeight: 600,
                      background: 'linear-gradient(45deg, #22C55E, #16A34A)'
                    }}
                  />
                </Box>

                <Typography variant="h4" align="center" sx={{ mb: 1, fontWeight: 600 }}>
                  Welcome to Beta
                </Typography>
                <Typography variant="body1" align="center" color="text.secondary" sx={{ mb: 4 }}>
                  Experience the future of livestock management with our free beta program
                </Typography>

                {error && (
                  <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                  </Alert>
                )}

                <Box component="form" onSubmit={handleLogin}>
                  <TextField
                    fullWidth
                    label="Username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    margin="normal"
                    required
                    autoFocus
                    placeholder="Enter Demo for beta access"
                  />
                  <TextField
                    fullWidth
                    label="Password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    margin="normal"
                    required
                    placeholder="Enter 123 for beta access"
                  />

                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    size="large"
                    disabled={isLoading}
                    sx={{
                      mt: 3,
                      mb: 2,
                      py: 1.5,
                      background: 'linear-gradient(45deg, #22C55E, #16A34A)',
                      fontWeight: 600,
                      fontSize: '1.1rem'
                    }}
                  >
                    {isLoading ? 'Signing In...' : 'Access Beta Dashboard'}
                  </Button>
                </Box>

                <Box sx={{ textAlign: 'center', mt: 3 }}>
                  <Typography variant="body2" color="text.secondary">
                    Demo Credentials: Demo / 123
                  </Typography>
                  <Button
                    variant="text"
                    onClick={() => navigate('/')}
                    sx={{ mt: 1 }}
                  >
                    Back to Home
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Features Overview */}
          <Grid item xs={12} md={6}>
            <Paper sx={{
              p: 4,
              background: 'linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7))',
              backdropFilter: 'blur(10px)',
              borderRadius: 3
            }}>
              <Typography variant="h4" sx={{ mb: 3, fontWeight: 600, color: '#22C55E' }}>
                Beta Features
              </Typography>
              <Typography variant="body1" sx={{ mb: 4, color: 'text.secondary' }}>
                Get started with essential livestock management tools at no cost
              </Typography>

              <List>
                {betaFeatures.map((feature, index) => (
                  <ListItem key={index} sx={{ px: 0, py: 1 }}>
                    <ListItemIcon sx={{ color: '#22C55E' }}>
                      {feature.icon}
                    </ListItemIcon>
                    <ListItemText
                      primary={feature.title}
                      secondary={feature.description}
                      primaryTypographyProps={{ fontWeight: 600 }}
                    />
                  </ListItem>
                ))}
              </List>

              <Box sx={{ mt: 4, p: 3, background: 'rgba(34, 197, 94, 0.1)', borderRadius: 2 }}>
                <Typography variant="h6" sx={{ mb: 1, fontWeight: 600 }}>
                  Ready to Upgrade?
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Unlock unlimited animals, advanced analytics, and marketplace features
                </Typography>
                <Button
                  variant="outlined"
                  onClick={() => navigate('/?tab=pricing')}
                  sx={{ borderColor: '#22C55E', color: '#22C55E' }}
                >
                  View Professional Plans
                </Button>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default BetaLogin;