import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Button,
  Card,
  CardContent,
  Grid,
  Chip,
  Alert,
  useTheme,
  alpha,
  Paper,
  AppBar,
  Toolbar,
  IconButton,
  Drawer,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  Dashboard,
  Pets,
  LocalHospital,
  Favorite,
  Restaurant,
  AccountBalance,
  Inventory,
  Business,
  Assessment,
  MenuBook,
  Gavel,
  Settings,
  Menu,
  Star,
  TrendingUp,
  Analytics,
  Security
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import EnhancedGradientBackground from '../components/common/EnhancedGradientBackground';
import AgriIntelBrand from '../components/branding/AgriIntelBrand';
import { moduleAccess, subscriptionPlans } from '../config/subscriptionConfig';

const EnhancedLiveDashboard: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Get all modules for enterprise plan (full access)
  const currentPlan = 'enterprise';
  const enterprisePlan = subscriptionPlans.find(p => p.id === 'enterprise');
  const allModules = moduleAccess;

  // Icon mapping
  const iconMap: Record<string, any> = {
    Dashboard,
    Pets,
    LocalHospital,
    Favorite,
    Restaurant,
    AccountBalance,
    Inventory,
    Business,
    Assessment,
    MenuBook,
    Gavel,
    Analytics,
    Settings
  };

  // Color mapping for modules
  const colorMap: Record<string, string> = {
    dashboard: theme.palette.primary.main,
    animals: '#4caf50',
    health: '#f44336',
    resources: '#ff9800',
    breeding: '#e91e63',
    feeding: '#ff5722',
    financial: '#2196f3',
    inventory: '#9c27b0',
    commercial: '#607d8b',
    reports: '#795548',
    compliance: '#3f51b5',
    analytics: '#009688',
    settings: '#424242'
  };

  const handleModuleClick = (module: any) => {
    navigate(module.path);
  };

  const coreModules = allModules.filter(m => m.category === 'core');
  const advancedModules = allModules.filter(m => m.category === 'advanced');
  const analyticsModules = allModules.filter(m => m.category === 'analytics');
  const premiumModules = allModules.filter(m => m.category === 'premium');

  const sidebarContent = (
    <Box sx={{ width: 280, p: 2 }}>
      <Box sx={{ mb: 3 }}>
        <AgriIntelBrand variant="compact" size="medium" />
        <Chip 
          label="LIVE VERSION" 
          color="success" 
          size="small" 
          sx={{ mt: 1, fontSize: '0.7rem' }}
        />
      </Box>

      <Typography variant="h6" sx={{ mb: 2, color: 'primary.main' }}>
        Core Modules
      </Typography>
      
      <List>
        {coreModules.map((module) => {
          const IconComponent = iconMap[module.icon];
          const moduleColor = colorMap[module.id];

          return (
            <ListItemButton
              key={module.id}
              onClick={() => handleModuleClick(module)}
              sx={{
                borderRadius: 2,
                mb: 1,
                '&:hover': {
                  backgroundColor: alpha(moduleColor, 0.1)
                }
              }}
            >
              <ListItemIcon>
                <IconComponent sx={{ color: moduleColor }} />
              </ListItemIcon>
              <ListItemText
                primary={module.title}
                secondary={module.description}
              />
            </ListItemButton>
          );
        })}
      </List>

      <Typography variant="h6" sx={{ mb: 2, mt: 3, color: 'secondary.main' }}>
        Advanced Features
      </Typography>

      <List>
        {advancedModules.map((module) => {
          const IconComponent = iconMap[module.icon];
          const moduleColor = colorMap[module.id];

          return (
            <ListItemButton
              key={module.id}
              onClick={() => handleModuleClick(module)}
              sx={{
                borderRadius: 2,
                mb: 1,
                '&:hover': {
                  backgroundColor: alpha(moduleColor, 0.1)
                }
              }}
            >
              <ListItemIcon>
                <IconComponent sx={{ color: moduleColor }} />
              </ListItemIcon>
              <ListItemText
                primary={module.title}
                secondary={module.description}
              />
            </ListItemButton>
          );
        })}
      </List>

      <Typography variant="h6" sx={{ mb: 2, mt: 3, color: 'info.main' }}>
        Analytics & Premium
      </Typography>

      <List>
        {[...analyticsModules, ...premiumModules].map((module) => {
          const IconComponent = iconMap[module.icon];
          const moduleColor = colorMap[module.id];

          return (
            <ListItemButton
              key={module.id}
              onClick={() => handleModuleClick(module)}
              sx={{
                borderRadius: 2,
                mb: 1,
                '&:hover': {
                  backgroundColor: alpha(moduleColor, 0.1)
                }
              }}
            >
              <ListItemIcon>
                <IconComponent sx={{ color: moduleColor }} />
              </ListItemIcon>
              <ListItemText
                primary={module.title}
                secondary={module.description}
              />
            </ListItemButton>
          );
        })}
      </List>
    </Box>
  );

  return (
    <EnhancedGradientBackground
      module="dashboard"
      variant="mesh"
      enableAnimation={true}
      enableParticles={true}
      opacity={0.7}
    >
      {/* App Bar */}
      <AppBar position="fixed" sx={{ zIndex: theme.zIndex.drawer + 1 }}>
        <Toolbar>
          <IconButton
            color="inherit"
            onClick={() => setSidebarOpen(true)}
            sx={{ mr: 2 }}
          >
            <Menu />
          </IconButton>
          
          <AgriIntelBrand variant="compact" size="small" color="white" />
          
          <Chip 
            label="LIVE" 
            color="success" 
            size="small" 
            sx={{ ml: 2 }}
          />
          
          <Box sx={{ flexGrow: 1 }} />
          
          <Button
            color="inherit"
            onClick={() => navigate('/dashboard/settings')}
            startIcon={<Settings />}
          >
            Settings
          </Button>
          
          <Button
            color="inherit"
            onClick={() => navigate('/')}
          >
            Home
          </Button>
        </Toolbar>
      </AppBar>

      {/* Sidebar */}
      <Drawer
        anchor="left"
        open={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
      >
        {sidebarContent}
      </Drawer>

      {/* Main Content */}
      <Container maxWidth="xl" sx={{ mt: 10, py: 4 }}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Welcome Section */}
          <Paper sx={{ p: 4, mb: 4, borderRadius: 3 }}>
            <Typography variant="h3" fontWeight="bold" sx={{ mb: 2 }}>
              Welcome to AgriIntel Live! 🚀
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ mb: 3 }}>
              You have full access to all premium features. Manage unlimited animals, 
              access AI insights, and optimize your entire livestock operation.
            </Typography>
            
            <Alert severity="success" sx={{ mb: 3 }}>
              <Typography variant="body1">
                <strong>Live Access Includes:</strong> {enterprisePlan?.features.join(', ')}
              </Typography>
            </Alert>

            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Button
                variant="contained"
                size="large"
                onClick={() => navigate('/dashboard/animals')}
                startIcon={<Pets />}
              >
                Manage Animals
              </Button>
              <Button
                variant="outlined"
                size="large"
                onClick={() => navigate('/dashboard/reports')}
                startIcon={<Analytics />}
              >
                View Analytics
              </Button>
            </Box>
          </Paper>

          {/* Core Modules */}
          <Typography variant="h4" fontWeight="bold" sx={{ mb: 3 }}>
            Core Management Modules
          </Typography>
          
          <Grid container spacing={3} sx={{ mb: 4 }}>
            {coreModules.map((module, index) => {
              const IconComponent = iconMap[module.icon];
              const moduleColor = colorMap[module.id];

              return (
                <Grid item xs={12} sm={6} md={3} key={module.id}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card
                      sx={{
                        height: '100%',
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          transform: 'translateY(-8px)',
                          boxShadow: `0 12px 24px ${alpha(moduleColor, 0.2)}`
                        }
                      }}
                      onClick={() => handleModuleClick(module)}
                    >
                      <CardContent sx={{ p: 3, textAlign: 'center' }}>
                        <Box
                          sx={{
                            p: 2,
                            borderRadius: 3,
                            backgroundColor: alpha(moduleColor, 0.1),
                            display: 'inline-block',
                            mb: 2
                          }}
                        >
                          <IconComponent sx={{ fontSize: 40, color: moduleColor }} />
                        </Box>
                        <Typography variant="h6" fontWeight="bold" sx={{ mb: 1 }}>
                          {module.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {module.description}
                        </Typography>
                        <Chip
                          label="Full Access"
                          color="success"
                          size="small"
                          sx={{ mt: 2 }}
                        />
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              );
            })}
          </Grid>

          {/* Advanced Modules */}
          <Typography variant="h4" fontWeight="bold" sx={{ mb: 3 }}>
            Advanced Features
          </Typography>
          
          <Grid container spacing={3} sx={{ mb: 4 }}>
            {advancedModules.map((module, index) => (
              <Grid item xs={12} sm={6} md={4} key={module.id}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: (index + 4) * 0.1 }}
                >
                  <Card 
                    sx={{ 
                      height: '100%', 
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: `0 12px 24px ${alpha(module.color, 0.2)}`
                      }
                    }}
                    onClick={() => handleModuleClick(module)}
                  >
                    <CardContent sx={{ p: 3, textAlign: 'center' }}>
                      <Box
                        sx={{
                          p: 2,
                          borderRadius: 3,
                          backgroundColor: alpha(module.color, 0.1),
                          display: 'inline-block',
                          mb: 2
                        }}
                      >
                        <module.icon sx={{ fontSize: 40, color: module.color }} />
                      </Box>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 1 }}>
                        {module.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {module.description}
                      </Typography>
                      <Chip 
                        label="Premium" 
                        color="secondary" 
                        size="small" 
                        sx={{ mt: 2 }}
                      />
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>

          {/* Analytics Module */}
          <Typography variant="h4" fontWeight="bold" sx={{ mb: 3 }}>
            Analytics & Intelligence
          </Typography>
          
          <Grid container spacing={3}>
            {analyticsModules.map((module, index) => (
              <Grid item xs={12} key={module.id}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: (index + 9) * 0.1 }}
                >
                  <Card 
                    sx={{ 
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: `0 8px 16px ${alpha(module.color, 0.2)}`
                      }
                    }}
                    onClick={() => handleModuleClick(module)}
                  >
                    <CardContent sx={{ p: 4, display: 'flex', alignItems: 'center' }}>
                      <Box
                        sx={{
                          p: 3,
                          borderRadius: 3,
                          backgroundColor: alpha(module.color, 0.1),
                          mr: 3
                        }}
                      >
                        <module.icon sx={{ fontSize: 48, color: module.color }} />
                      </Box>
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="h5" fontWeight="bold" sx={{ mb: 1 }}>
                          {module.title}
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          {module.description}
                        </Typography>
                      </Box>
                      <Chip 
                        label="AI Powered" 
                        color="info" 
                        sx={{ ml: 2 }}
                      />
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </motion.div>
      </Container>
    </EnhancedGradientBackground>
  );
};

export default EnhancedLiveDashboard;
