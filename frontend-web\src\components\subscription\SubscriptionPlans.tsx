import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  useTheme,
  alpha,
  Divider
} from '@mui/material';
import {
  Check,
  Star,
  Business,
  Pets,
  Restaurant,
  LocalHospital,
  Assessment,
  MonetizationOn,
  Security,
  Support,
  Cloud,
  Speed,
  AutoAwesome,
  TrendingUp,
  Close
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../../contexts/LanguageContext';

interface PlanFeature {
  text: string;
  included: boolean;
  premium?: boolean;
}

interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  period: string;
  description: string;
  popular?: boolean;
  features: PlanFeature[];
  limitations?: string[];
  color: string;
  icon: React.ReactNode;
}

const SubscriptionPlans: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { translate } = useLanguage();
  const [showComparisonDialog, setShowComparisonDialog] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);

  const plans: SubscriptionPlan[] = [
    {
      id: 'beta',
      name: 'BETA (Free)',
      price: 0,
      period: 'forever',
      description: 'Perfect for small farms getting started',
      color: '#4CAF50',
      icon: <Pets />,
      features: [
        { text: 'Up to 50 animals', included: true },
        { text: 'Basic animal records', included: true },
        { text: 'Simple feed tracking', included: true },
        { text: 'Excel reports only', included: true },
        { text: 'Basic health monitoring', included: true },
        { text: 'Email support', included: true },
        { text: 'AI health analytics', included: false, premium: true },
        { text: 'Financial management', included: false, premium: true },
        { text: 'Breeding optimization', included: false, premium: true },
        { text: 'Service marketplace', included: false, premium: true }
      ],
      limitations: [
        '50 animal limit',
        'Basic reporting only',
        'No advanced analytics',
        'Limited support'
      ]
    },
    {
      id: 'professional',
      name: 'Professional',
      price: 299,
      period: 'month',
      description: 'For growing farms with advanced needs',
      popular: true,
      color: '#2196F3',
      icon: <TrendingUp />,
      features: [
        { text: 'Up to 500 animals', included: true },
        { text: 'Advanced animal management', included: true },
        { text: 'AI health analytics', included: true },
        { text: 'Financial management', included: true },
        { text: 'Breeding optimization', included: true },
        { text: 'Feed scheduling & nutrition', included: true },
        { text: 'Advanced reporting & analytics', included: true },
        { text: 'Service marketplace access', included: true },
        { text: 'Priority support', included: true },
        { text: 'Mobile app access', included: true },
        { text: 'Multi-farm management', included: false, premium: true },
        { text: 'Custom integrations', included: false, premium: true }
      ]
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      price: 599,
      period: 'month',
      description: 'For large operations and commercial farms',
      color: '#9C27B0',
      icon: <Business />,
      features: [
        { text: 'Unlimited animals', included: true },
        { text: 'Everything in Professional', included: true },
        { text: 'Multi-farm management', included: true },
        { text: 'Custom integrations', included: true },
        { text: 'Advanced AI insights', included: true },
        { text: 'Dedicated account manager', included: true },
        { text: '24/7 phone support', included: true },
        { text: 'Custom reporting', included: true },
        { text: 'API access', included: true },
        { text: 'White-label options', included: true },
        { text: 'On-premise deployment', included: true },
        { text: 'Training & onboarding', included: true }
      ]
    }
  ];

  const handleSelectPlan = (planId: string) => {
    setSelectedPlan(planId);
    if (planId === 'beta') {
      // Already on BETA, show features
      setShowComparisonDialog(true);
    } else {
      // Navigate to payment/upgrade flow
      navigate(`/subscription/checkout?plan=${planId}`);
    }
  };

  const handleCompareFeatures = () => {
    setShowComparisonDialog(true);
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ textAlign: 'center', mb: 6 }}>
        <Typography variant="h3" fontWeight="bold" gutterBottom>
          Choose Your AgriIntel Plan
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ mb: 3 }}>
          Unlock the full potential of your livestock management
        </Typography>
        <Button
          variant="outlined"
          onClick={handleCompareFeatures}
          sx={{ borderRadius: 3 }}
        >
          Compare All Features
        </Button>
      </Box>

      {/* Pricing Cards */}
      <Grid container spacing={4} justifyContent="center">
        {plans.map((plan, index) => (
          <Grid item xs={12} md={4} key={plan.id}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ y: -8 }}
            >
              <Card
                sx={{
                  height: '100%',
                  borderRadius: 4,
                  border: plan.popular ? `3px solid ${plan.color}` : '1px solid rgba(255,255,255,0.1)',
                  background: plan.popular 
                    ? `linear-gradient(135deg, ${alpha(plan.color, 0.1)}, ${alpha(plan.color, 0.05)})`
                    : 'rgba(255,255,255,0.05)',
                  backdropFilter: 'blur(10px)',
                  position: 'relative',
                  overflow: 'visible'
                }}
              >
                {plan.popular && (
                  <Chip
                    label="MOST POPULAR"
                    sx={{
                      position: 'absolute',
                      top: -12,
                      left: '50%',
                      transform: 'translateX(-50%)',
                      backgroundColor: plan.color,
                      color: 'white',
                      fontWeight: 'bold',
                      zIndex: 1
                    }}
                  />
                )}

                <CardContent sx={{ p: 4, height: '100%', display: 'flex', flexDirection: 'column' }}>
                  {/* Plan Header */}
                  <Box sx={{ textAlign: 'center', mb: 3 }}>
                    <Box sx={{ 
                      display: 'inline-flex', 
                      p: 2, 
                      borderRadius: '50%', 
                      backgroundColor: alpha(plan.color, 0.1),
                      color: plan.color,
                      mb: 2
                    }}>
                      {plan.icon}
                    </Box>
                    <Typography variant="h5" fontWeight="bold" gutterBottom>
                      {plan.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {plan.description}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'baseline', justifyContent: 'center', mt: 2 }}>
                      <Typography variant="h3" fontWeight="bold" sx={{ color: plan.color }}>
                        R{plan.price}
                      </Typography>
                      <Typography variant="body1" color="text.secondary" sx={{ ml: 1 }}>
                        /{plan.period}
                      </Typography>
                    </Box>
                  </Box>

                  {/* Features List */}
                  <List sx={{ flexGrow: 1, py: 0 }}>
                    {plan.features.slice(0, 6).map((feature, idx) => (
                      <ListItem key={idx} sx={{ px: 0, py: 0.5 }}>
                        <ListItemIcon sx={{ minWidth: 32 }}>
                          {feature.included ? (
                            <Check sx={{ color: plan.color, fontSize: 20 }} />
                          ) : (
                            <Close sx={{ color: 'text.disabled', fontSize: 20 }} />
                          )}
                        </ListItemIcon>
                        <ListItemText
                          primary={feature.text}
                          primaryTypographyProps={{
                            variant: 'body2',
                            color: feature.included ? 'text.primary' : 'text.disabled',
                            sx: { textDecoration: feature.included ? 'none' : 'line-through' }
                          }}
                        />
                        {feature.premium && (
                          <Star sx={{ color: '#FFD700', fontSize: 16, ml: 1 }} />
                        )}
                      </ListItem>
                    ))}
                    {plan.features.length > 6 && (
                      <ListItem sx={{ px: 0, py: 0.5 }}>
                        <ListItemText
                          primary={`+ ${plan.features.length - 6} more features`}
                          primaryTypographyProps={{
                            variant: 'body2',
                            color: 'text.secondary',
                            fontStyle: 'italic'
                          }}
                        />
                      </ListItem>
                    )}
                  </List>

                  {/* Action Button */}
                  <Button
                    variant={plan.popular ? 'contained' : 'outlined'}
                    fullWidth
                    size="large"
                    onClick={() => handleSelectPlan(plan.id)}
                    sx={{
                      mt: 3,
                      borderRadius: 3,
                      py: 1.5,
                      backgroundColor: plan.popular ? plan.color : 'transparent',
                      borderColor: plan.color,
                      color: plan.popular ? 'white' : plan.color,
                      '&:hover': {
                        backgroundColor: plan.popular ? alpha(plan.color, 0.8) : alpha(plan.color, 0.1),
                        borderColor: plan.color
                      }
                    }}
                  >
                    {plan.id === 'beta' ? 'Current Plan' : `Upgrade to ${plan.name}`}
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* Feature Comparison Dialog */}
      <Dialog 
        open={showComparisonDialog} 
        onClose={() => setShowComparisonDialog(false)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          <Typography variant="h5" fontWeight="bold">
            Complete Feature Comparison
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ overflowX: 'auto' }}>
            {/* Comparison table would go here */}
            <Typography variant="body1" sx={{ mb: 2 }}>
              Detailed feature comparison coming soon. Contact support for more information.
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowComparisonDialog(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Trust Indicators */}
      <Box sx={{ mt: 8, textAlign: 'center' }}>
        <Typography variant="h6" gutterBottom>
          Trusted by South African Farmers
        </Typography>
        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 4, mt: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Security sx={{ color: theme.palette.success.main }} />
            <Typography variant="body2">Secure & Compliant</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Support sx={{ color: theme.palette.info.main }} />
            <Typography variant="body2">Local Support</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Cloud sx={{ color: theme.palette.primary.main }} />
            <Typography variant="body2">Cloud-Based</Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default SubscriptionPlans;
