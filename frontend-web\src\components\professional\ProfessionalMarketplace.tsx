import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Chip,
  Avatar,
  <PERSON>ing,
  Tab,
  Tabs,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  IconButton,
  Badge,
  Alert
} from '@mui/material';
import {
  LocalHospital,
  Store,
  Gavel,
  Security,
  Phone,
  Message,
  Star,
  LocationOn,
  Schedule,
  AttachMoney
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';

interface ServiceProvider {
  id: string;
  name: string;
  type: 'veterinarian' | 'supplier' | 'auctioneer' | 'security';
  rating: number;
  reviews: number;
  location: string;
  distance: string;
  availability: 'available' | 'busy' | 'offline';
  specialties: string[];
  priceRange: string;
  responseTime: string;
  avatar: string;
}

const ProfessionalMarketplace: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState(0);
  const [selectedProvider, setSelectedProvider] = useState<ServiceProvider | null>(null);

  // Mock service providers
  const serviceProviders: ServiceProvider[] = [
    {
      id: '1',
      name: 'Dr. <PERSON>',
      type: 'veterinarian',
      rating: 4.8,
      reviews: 127,
      location: 'Johannesburg, GP',
      distance: '12 km',
      availability: 'available',
      specialties: ['Cattle Health', 'Emergency Care', 'Vaccinations'],
      priceRange: 'R800 - R1,500',
      responseTime: '< 30 min',
      avatar: '/images/providers/vet1.jpg'
    },
    {
      id: '2',
      name: 'AgriSupply Co.',
      type: 'supplier',
      rating: 4.6,
      reviews: 89,
      location: 'Pretoria, GP',
      distance: '25 km',
      availability: 'available',
      specialties: ['Feed', 'Equipment', 'Medications'],
      priceRange: 'Competitive',
      responseTime: '< 1 hour',
      avatar: '/images/providers/supplier1.jpg'
    },
    {
      id: '3',
      name: 'BKB Auctions',
      type: 'auctioneer',
      rating: 4.9,
      reviews: 203,
      location: 'Bloemfontein, FS',
      distance: '180 km',
      availability: 'available',
      specialties: ['Cattle Auctions', 'Online Bidding', 'Market Analysis'],
      priceRange: '5% commission',
      responseTime: '< 2 hours',
      avatar: '/images/providers/auction1.jpg'
    },
    {
      id: '4',
      name: 'FarmGuard Security',
      type: 'security',
      rating: 4.7,
      reviews: 156,
      location: 'Potchefstroom, NW',
      distance: '45 km',
      availability: 'available',
      specialties: ['Farm Security', '24/7 Monitoring', 'Livestock Protection'],
      priceRange: 'R2,500/month',
      responseTime: '< 15 min',
      avatar: '/images/providers/security1.jpg'
    }
  ];

  const getProviderIcon = (type: string) => {
    switch (type) {
      case 'veterinarian': return <LocalHospital color="primary" />;
      case 'supplier': return <Store color="secondary" />;
      case 'auctioneer': return <Gavel color="warning" />;
      case 'security': return <Security color="error" />;
      default: return <Store />;
    }
  };

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'available': return 'success';
      case 'busy': return 'warning';
      case 'offline': return 'error';
      default: return 'default';
    }
  };

  const filteredProviders = activeTab === 0 
    ? serviceProviders 
    : serviceProviders.filter(provider => {
        const types = ['veterinarian', 'supplier', 'auctioneer', 'security'];
        return provider.type === types[activeTab - 1];
      });

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleContactProvider = (provider: ServiceProvider) => {
    // In a real app, this would open a communication interface
    console.log('Contacting provider:', provider.name);
  };

  if (user?.role !== 'professional' && user?.role !== 'admin') {
    return (
      <Alert severity="warning">
        Marketplace features are available for Professional tier users only.
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
          Professional Marketplace
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Connect with trusted veterinarians, suppliers, auctioneers, and security services
        </Typography>
      </Box>

      {/* Service Categories */}
      <Card sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab label="All Services" />
          <Tab 
            label="Veterinarians" 
            icon={<LocalHospital />} 
            iconPosition="start"
          />
          <Tab 
            label="Suppliers" 
            icon={<Store />} 
            iconPosition="start"
          />
          <Tab 
            label="Auctioneers" 
            icon={<Gavel />} 
            iconPosition="start"
          />
          <Tab 
            label="Security" 
            icon={<Security />} 
            iconPosition="start"
          />
        </Tabs>
      </Card>

      {/* Service Providers Grid */}
      <Grid container spacing={3}>
        {filteredProviders.map((provider, index) => (
          <Grid item xs={12} md={6} lg={4} key={provider.id}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card 
                sx={{ 
                  height: '100%',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4
                  }
                }}
                onClick={() => setSelectedProvider(provider)}
              >
                <CardContent>
                  {/* Provider Header */}
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar
                      src={provider.avatar}
                      sx={{ width: 56, height: 56, mr: 2 }}
                    >
                      {getProviderIcon(provider.type)}
                    </Avatar>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        {provider.name}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Rating value={provider.rating} precision={0.1} size="small" readOnly />
                        <Typography variant="body2" color="text.secondary">
                          ({provider.reviews})
                        </Typography>
                      </Box>
                    </Box>
                    <Badge
                      color={getAvailabilityColor(provider.availability) as any}
                      variant="dot"
                      sx={{ mr: 1 }}
                    />
                  </Box>

                  {/* Location & Distance */}
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <LocationOn sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                    <Typography variant="body2" color="text.secondary">
                      {provider.location} • {provider.distance}
                    </Typography>
                  </Box>

                  {/* Response Time */}
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Schedule sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                    <Typography variant="body2" color="text.secondary">
                      Response time: {provider.responseTime}
                    </Typography>
                  </Box>

                  {/* Specialties */}
                  <Box sx={{ mb: 2 }}>
                    {provider.specialties.slice(0, 2).map((specialty) => (
                      <Chip
                        key={specialty}
                        label={specialty}
                        size="small"
                        sx={{ mr: 0.5, mb: 0.5 }}
                      />
                    ))}
                    {provider.specialties.length > 2 && (
                      <Chip
                        label={`+${provider.specialties.length - 2} more`}
                        size="small"
                        variant="outlined"
                      />
                    )}
                  </Box>

                  {/* Price Range */}
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <AttachMoney sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                    <Typography variant="body2" color="text.secondary">
                      {provider.priceRange}
                    </Typography>
                  </Box>

                  {/* Action Buttons */}
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      variant="contained"
                      size="small"
                      startIcon={<Phone />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleContactProvider(provider);
                      }}
                      sx={{ flexGrow: 1 }}
                    >
                      Contact
                    </Button>
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        // Handle message
                      }}
                    >
                      <Message />
                    </IconButton>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* Quick Stats */}
      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
            Marketplace Statistics
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={6} md={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="primary" sx={{ fontWeight: 700 }}>
                  {serviceProviders.filter(p => p.type === 'veterinarian').length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Veterinarians
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} md={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="secondary" sx={{ fontWeight: 700 }}>
                  {serviceProviders.filter(p => p.type === 'supplier').length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Suppliers
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} md={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="warning.main" sx={{ fontWeight: 700 }}>
                  {serviceProviders.filter(p => p.type === 'auctioneer').length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Auctioneers
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} md={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="error.main" sx={{ fontWeight: 700 }}>
                  {serviceProviders.filter(p => p.type === 'security').length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Security Services
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
};

export default ProfessionalMarketplace;
