import React from 'react';
import UpgradePrompt from './UpgradePrompt';

const FinancialUpgrade: React.FC = () => {
  const features = [
    'Complete financial management system',
    'Income and expense tracking',
    'Profit & loss statements',
    'Cash flow analysis',
    'Budget planning and forecasting',
    'Tax preparation assistance',
    'Financial performance dashboards',
    'Multi-currency support',
    'Invoice and payment management'
  ];

  return (
    <UpgradePrompt
      moduleName="Financial Management"
      features={features}
      tier="Professional"
      price="R299/month"
    />
  );
};

export default FinancialUpgrade;
