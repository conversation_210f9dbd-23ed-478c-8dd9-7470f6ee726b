import React from 'react';
import UpgradePrompt from './UpgradePrompt';

const AnalyticsUpgrade: React.FC = () => {
  const features = [
    'Advanced business analytics',
    'Predictive modeling and forecasting',
    'Performance benchmarking',
    'Custom dashboard creation',
    'Data visualization tools',
    'Automated reporting',
    'KPI tracking and alerts',
    'Machine learning insights',
    'Export to Excel/PDF'
  ];

  return (
    <UpgradePrompt
      moduleName="Business Analytics"
      features={features}
      tier="Enterprise"
      price="R599/month"
    />
  );
};

export default AnalyticsUpgrade;
