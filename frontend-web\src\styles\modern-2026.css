/**
 * AgriIntel 2026 - Next-Generation Design System
 * Futuristic Agricultural Intelligence Platform
 * Features: Quantum Gradients, Neural Networks, Glassmorphism, AI-Enhanced UX
 */

/* ===== QUANTUM DESIGN TOKENS ===== */
:root {
  /* Quantum Color Palette */
  --quantum-void: #000000;
  --quantum-dark: #0a0a0a;
  --quantum-surface: #1a1a1a;
  --quantum-elevated: #2a2a2a;
  --quantum-border: rgba(255, 255, 255, 0.1);
  --quantum-text: #ffffff;
  --quantum-text-secondary: rgba(255, 255, 255, 0.7);
  --quantum-text-muted: rgba(255, 255, 255, 0.5);

  /* Neural Network Gradients */
  --neural-primary: linear-gradient(135deg, #00f5ff 0%, #0066ff 25%, #6600ff 50%, #ff0066 75%, #ff6600 100%);
  --neural-secondary: linear-gradient(135deg, #ff6b35 0%, #f7931e 25%, #ffd23f 50%, #06ffa5 75%, #00d4ff 100%);
  --neural-success: linear-gradient(135deg, #00ff88 0%, #00cc6a 50%, #009951 100%);
  --neural-warning: linear-gradient(135deg, #ffb347 0%, #ff8c42 50%, #ff6b35 100%);
  --neural-danger: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 50%, #d63031 100%);
  --neural-info: linear-gradient(135deg, #00d4ff 0%, #0099cc 50%, #006699 100%);

  /* Glassmorphism System */
  --glass-ultra-light: rgba(255, 255, 255, 0.03);
  --glass-light: rgba(255, 255, 255, 0.05);
  --glass-medium: rgba(255, 255, 255, 0.08);
  --glass-strong: rgba(255, 255, 255, 0.12);
  --glass-border-light: rgba(255, 255, 255, 0.1);
  --glass-border-medium: rgba(255, 255, 255, 0.2);
  --glass-backdrop: blur(20px) saturate(180%);
  --glass-backdrop-strong: blur(40px) saturate(200%);

  /* Neural Shadows */
  --shadow-neural: 0 8px 32px rgba(0, 245, 255, 0.15);
  --shadow-quantum: 0 12px 48px rgba(0, 0, 0, 0.3);
  --shadow-elevated: 0 20px 80px rgba(0, 0, 0, 0.4);
  --shadow-floating: 0 32px 120px rgba(0, 0, 0, 0.5);

  /* Quantum Typography */
  --font-quantum: 'Inter Variable', 'SF Pro Display', -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
  --font-mono: 'JetBrains Mono Variable', 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
  
  /* Micro-interaction Timings */
  --timing-instant: 0.1s cubic-bezier(0.4, 0, 0.2, 1);
  --timing-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --timing-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --timing-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  --timing-bounce: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Quantum Spacing */
  --space-quantum: 4px;
  --space-xs: 8px;
  --space-sm: 12px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  --space-2xl: 48px;
  --space-3xl: 64px;
  --space-4xl: 96px;

  /* Neural Border Radius */
  --radius-quantum: 4px;
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 24px;
  --radius-2xl: 32px;
  --radius-full: 9999px;
}

/* ===== QUANTUM RESET & BASE ===== */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-quantum);
  background: var(--quantum-dark);
  color: var(--quantum-text);
  min-height: 100vh;
  overflow-x: hidden;
  position: relative;
}

/* Neural Network Background */
body::before {
  content: '';
  position: fixed;
  inset: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(0, 245, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(102, 0, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: -2;
}

/* Quantum Grid Pattern */
body::after {
  content: '';
  position: fixed;
  inset: 0;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 50px 50px;
  pointer-events: none;
  z-index: -1;
}

/* ===== QUANTUM SCROLLBAR ===== */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--quantum-surface);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--neural-primary);
  border-radius: var(--radius-full);
  border: 2px solid var(--quantum-surface);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neural-secondary);
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 245, 255, 0.5) var(--quantum-surface);
}

/* ===== QUANTUM SELECTION ===== */
::selection {
  background: rgba(0, 245, 255, 0.3);
  color: var(--quantum-text);
}

::-moz-selection {
  background: rgba(0, 245, 255, 0.3);
  color: var(--quantum-text);
}

/* ===== NEURAL FOCUS SYSTEM ===== */
:focus-visible {
  outline: 2px solid rgba(0, 245, 255, 0.8);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* ===== QUANTUM ANIMATIONS ===== */
@keyframes quantum-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes neural-pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(0, 245, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 245, 255, 0);
  }
}

@keyframes holographic-rotate {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* ===== QUANTUM UTILITY CLASSES ===== */
.quantum-fade-in {
  animation: quantum-fade-in 0.6s ease-out;
}

.neural-pulse {
  animation: neural-pulse 2s infinite;
}

.glass-surface {
  background: var(--glass-medium);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border-light);
}

.glass-elevated {
  background: var(--glass-strong);
  -webkit-backdrop-filter: var(--glass-backdrop-strong);
  backdrop-filter: var(--glass-backdrop-strong);
  border: 1px solid var(--glass-border-medium);
  box-shadow: var(--shadow-elevated);
}

.neural-gradient {
  background: var(--neural-primary);
  background-size: 400% 400%;
  animation: holographic-rotate 3s ease infinite;
}

.quantum-text {
  background: var(--neural-primary);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 400% 400%;
  animation: holographic-rotate 3s ease infinite;
}

/* ===== RESPONSIVE QUANTUM DESIGN ===== */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
  
  body::after {
    background-size: 30px 30px;
  }
}

@media (max-width: 480px) {
  html {
    font-size: 13px;
  }
  
  body::after {
    background-size: 20px 20px;
  }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  html {
    scroll-behavior: auto;
  }
}

@media (prefers-contrast: high) {
  :root {
    --quantum-text: #ffffff;
    --quantum-border: #ffffff;
    --glass-border-light: #ffffff;
    --glass-border-medium: #ffffff;
  }
}
