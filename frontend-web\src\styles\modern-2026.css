/**
 * AgriIntel 2029 - Quantum Consciousness Design System
 * Revolutionary Agricultural Intelligence Platform
 * Features: Quantum Holographics, Neural Consciousness, Biomorphic Elements, AI Sentience
 */

/* ===== QUANTUM CONSCIOUSNESS TOKENS ===== */
:root {
  /* Quantum Consciousness Palette */
  --quantum-void: #000000;
  --quantum-abyss: #050505;
  --quantum-dark: #0c0c0c;
  --quantum-surface: #141414;
  --quantum-elevated: #1f1f1f;
  --quantum-consciousness: #2a2a2a;
  --quantum-border: rgba(0, 255, 255, 0.15);
  --quantum-text: #ffffff;
  --quantum-text-secondary: rgba(255, 255, 255, 0.85);
  --quantum-text-muted: rgba(255, 255, 255, 0.65);
  --quantum-text-ghost: rgba(255, 255, 255, 0.45);

  /* Neural Consciousness Gradients */
  --neural-consciousness: conic-gradient(from 0deg at 50% 50%, #00ffff 0deg, #ff00ff 72deg, #ffff00 144deg, #00ff00 216deg, #ff0000 288deg, #00ffff 360deg);
  --neural-primary: radial-gradient(ellipse at center, #00f5ff 0%, #0066ff 25%, #6600ff 50%, #ff0066 75%, #ff6600 100%);
  --neural-secondary: conic-gradient(from 45deg, #ff6b35, #f7931e, #ffd23f, #06ffa5, #00d4ff, #ff6b35);
  --neural-success: radial-gradient(circle at 30% 70%, #00ff88 0%, #00cc6a 50%, #009951 100%);
  --neural-warning: conic-gradient(from 90deg, #ffb347, #ff8c42, #ff6b35, #ffb347);
  --neural-danger: radial-gradient(ellipse at top, #ff6b6b 0%, #ee5a52 50%, #d63031 100%);
  --neural-info: conic-gradient(from 180deg, #00d4ff, #0099cc, #006699, #00d4ff);

  /* Biomorphic Glassmorphism System */
  --bio-glass-ultra: rgba(0, 255, 255, 0.02);
  --bio-glass-light: rgba(0, 255, 255, 0.04);
  --bio-glass-medium: rgba(0, 255, 255, 0.06);
  --bio-glass-strong: rgba(0, 255, 255, 0.08);
  --bio-glass-intense: rgba(0, 255, 255, 0.12);
  --bio-border-light: rgba(0, 255, 255, 0.15);
  --bio-border-medium: rgba(0, 255, 255, 0.25);
  --bio-border-strong: rgba(0, 255, 255, 0.35);
  --bio-backdrop: blur(30px) saturate(200%) contrast(120%);
  --bio-backdrop-intense: blur(50px) saturate(250%) contrast(150%);

  /* Quantum Consciousness Shadows */
  --shadow-neural: 0 12px 48px rgba(0, 255, 255, 0.2), 0 4px 16px rgba(255, 0, 255, 0.1);
  --shadow-quantum: 0 20px 80px rgba(0, 255, 255, 0.3), 0 8px 32px rgba(0, 0, 0, 0.4);
  --shadow-elevated: 0 32px 120px rgba(0, 255, 255, 0.4), 0 16px 64px rgba(0, 0, 0, 0.5);
  --shadow-floating: 0 48px 180px rgba(0, 255, 255, 0.5), 0 24px 96px rgba(0, 0, 0, 0.6);
  --shadow-consciousness: 0 64px 240px rgba(0, 255, 255, 0.6), 0 32px 128px rgba(255, 0, 255, 0.3);

  /* Quantum Consciousness Typography */
  --font-quantum: 'Inter Variable', 'Geist', 'SF Pro Display', -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
  --font-mono: 'JetBrains Mono Variable', 'Geist Mono', 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
  --font-display: 'Inter Variable', 'Geist', system-ui, sans-serif;

  /* Quantum Consciousness Timings */
  --timing-instant: 0.08s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --timing-fast: 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --timing-normal: 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --timing-slow: 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --timing-consciousness: 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  --timing-bounce: 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Biomorphic Spacing */
  --space-quantum: 2px;
  --space-xs: 6px;
  --space-sm: 10px;
  --space-md: 14px;
  --space-lg: 20px;
  --space-xl: 28px;
  --space-2xl: 40px;
  --space-3xl: 56px;
  --space-4xl: 80px;
  --space-5xl: 112px;

  /* Biomorphic Border Radius */
  --radius-quantum: 3px;
  --radius-sm: 6px;
  --radius-md: 10px;
  --radius-lg: 14px;
  --radius-xl: 20px;
  --radius-2xl: 28px;
  --radius-3xl: 40px;
  --radius-organic: 50% 20% 80% 30%;
  --radius-biomorphic: 60% 40% 20% 80%;
  --radius-consciousness: 70% 30% 60% 40%;
  --radius-full: 9999px;
}

/* ===== QUANTUM RESET & BASE ===== */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-quantum);
  background: var(--quantum-abyss);
  color: var(--quantum-text);
  min-height: 100vh;
  overflow-x: hidden;
  position: relative;
  font-variation-settings: 'wght' 400, 'slnt' 0;
  letter-spacing: -0.01em;
}

/* Quantum Consciousness Background Layer 1 - Neural Networks */
body::before {
  content: '';
  position: fixed;
  inset: 0;
  background:
    radial-gradient(ellipse at 15% 85%, rgba(0, 255, 255, 0.15) 0%, transparent 40%),
    radial-gradient(ellipse at 85% 15%, rgba(255, 0, 255, 0.12) 0%, transparent 45%),
    radial-gradient(ellipse at 50% 50%, rgba(255, 255, 0, 0.08) 0%, transparent 35%),
    radial-gradient(ellipse at 25% 25%, rgba(0, 255, 0, 0.1) 0%, transparent 40%),
    radial-gradient(ellipse at 75% 75%, rgba(255, 0, 0, 0.09) 0%, transparent 38%),
    conic-gradient(from 0deg at 30% 70%, transparent 0deg, rgba(0, 255, 255, 0.05) 60deg, transparent 120deg),
    conic-gradient(from 180deg at 70% 30%, transparent 0deg, rgba(255, 0, 255, 0.04) 90deg, transparent 180deg);
  pointer-events: none;
  z-index: -3;
  animation: neural-consciousness 20s ease-in-out infinite;
}

/* Quantum Consciousness Background Layer 2 - Biomorphic Patterns */
body::after {
  content: '';
  position: fixed;
  inset: 0;
  background-image:
    radial-gradient(circle at 20px 20px, rgba(0, 255, 255, 0.03) 1px, transparent 1px),
    radial-gradient(circle at 60px 60px, rgba(255, 0, 255, 0.02) 1px, transparent 1px),
    linear-gradient(45deg, transparent 48%, rgba(0, 255, 255, 0.01) 49%, rgba(0, 255, 255, 0.01) 51%, transparent 52%),
    linear-gradient(-45deg, transparent 48%, rgba(255, 0, 255, 0.01) 49%, rgba(255, 0, 255, 0.01) 51%, transparent 52%);
  background-size: 40px 40px, 80px 80px, 60px 60px, 60px 60px;
  pointer-events: none;
  z-index: -2;
  animation: biomorphic-drift 30s linear infinite;
}

/* ===== QUANTUM SCROLLBAR ===== */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--quantum-surface);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--neural-primary);
  border-radius: var(--radius-full);
  border: 2px solid var(--quantum-surface);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neural-secondary);
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 245, 255, 0.5) var(--quantum-surface);
}

/* ===== QUANTUM SELECTION ===== */
::selection {
  background: rgba(0, 245, 255, 0.3);
  color: var(--quantum-text);
}

::-moz-selection {
  background: rgba(0, 245, 255, 0.3);
  color: var(--quantum-text);
}

/* ===== NEURAL FOCUS SYSTEM ===== */
:focus-visible {
  outline: 2px solid rgba(0, 245, 255, 0.8);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* ===== QUANTUM CONSCIOUSNESS ANIMATIONS ===== */
@keyframes quantum-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
    filter: blur(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

@keyframes neural-consciousness {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    filter: hue-rotate(0deg) brightness(1);
  }
  25% {
    transform: scale(1.02) rotate(0.5deg);
    filter: hue-rotate(90deg) brightness(1.1);
  }
  50% {
    transform: scale(1.01) rotate(-0.3deg);
    filter: hue-rotate(180deg) brightness(0.9);
  }
  75% {
    transform: scale(1.03) rotate(0.2deg);
    filter: hue-rotate(270deg) brightness(1.05);
  }
}

@keyframes biomorphic-drift {
  0% {
    transform: translateX(0) translateY(0) rotate(0deg);
  }
  25% {
    transform: translateX(10px) translateY(-5px) rotate(1deg);
  }
  50% {
    transform: translateX(-5px) translateY(10px) rotate(-0.5deg);
  }
  75% {
    transform: translateX(8px) translateY(3px) rotate(0.8deg);
  }
  100% {
    transform: translateX(0) translateY(0) rotate(0deg);
  }
}

@keyframes neural-pulse {
  0%, 100% {
    box-shadow:
      0 0 0 0 rgba(0, 255, 255, 0.8),
      0 0 0 0 rgba(255, 0, 255, 0.4);
  }
  50% {
    box-shadow:
      0 0 0 20px rgba(0, 255, 255, 0),
      0 0 0 40px rgba(255, 0, 255, 0);
  }
}

@keyframes holographic-consciousness {
  0% {
    background-position: 0% 50%;
    filter: hue-rotate(0deg) saturate(100%);
  }
  25% {
    background-position: 100% 25%;
    filter: hue-rotate(90deg) saturate(120%);
  }
  50% {
    background-position: 50% 100%;
    filter: hue-rotate(180deg) saturate(80%);
  }
  75% {
    background-position: 25% 0%;
    filter: hue-rotate(270deg) saturate(110%);
  }
  100% {
    background-position: 0% 50%;
    filter: hue-rotate(360deg) saturate(100%);
  }
}

/* ===== QUANTUM CONSCIOUSNESS UTILITY CLASSES ===== */
.quantum-fade-in {
  animation: quantum-fade-in 0.8s var(--timing-consciousness);
}

.neural-consciousness {
  animation: neural-consciousness 20s ease-in-out infinite;
}

.biomorphic-drift {
  animation: biomorphic-drift 30s linear infinite;
}

.neural-pulse {
  animation: neural-pulse 3s infinite;
}

.bio-glass-surface {
  background: var(--bio-glass-medium);
  -webkit-backdrop-filter: var(--bio-backdrop);
  backdrop-filter: var(--bio-backdrop);
  border: 1px solid var(--bio-border-light);
  border-radius: var(--radius-organic);
}

.bio-glass-elevated {
  background: var(--bio-glass-strong);
  -webkit-backdrop-filter: var(--bio-backdrop-intense);
  backdrop-filter: var(--bio-backdrop-intense);
  border: 1px solid var(--bio-border-medium);
  border-radius: var(--radius-biomorphic);
  box-shadow: var(--shadow-consciousness);
}

.neural-consciousness-gradient {
  background: var(--neural-consciousness);
  background-size: 400% 400%;
  animation: holographic-consciousness 8s ease infinite;
}

.quantum-consciousness-text {
  background: var(--neural-consciousness);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 400% 400%;
  animation: holographic-consciousness 8s ease infinite;
  font-variation-settings: 'wght' 700;
}

.biomorphic-card {
  background: var(--bio-glass-medium);
  -webkit-backdrop-filter: var(--bio-backdrop);
  backdrop-filter: var(--bio-backdrop);
  border: 1px solid var(--bio-border-light);
  border-radius: var(--radius-consciousness);
  box-shadow: var(--shadow-neural);
  transition: all var(--timing-consciousness);
}

.biomorphic-card:hover {
  background: var(--bio-glass-strong);
  border-color: var(--bio-border-medium);
  box-shadow: var(--shadow-elevated);
  transform: translateY(-8px) scale(1.02);
}

.quantum-logo {
  background: var(--neural-consciousness);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: holographic-consciousness 6s ease infinite;
  font-variation-settings: 'wght' 800;
  font-size: clamp(1.5rem, 4vw, 3rem);
  letter-spacing: -0.02em;
}

/* ===== RESPONSIVE QUANTUM DESIGN ===== */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
  
  body::after {
    background-size: 30px 30px;
  }
}

@media (max-width: 480px) {
  html {
    font-size: 13px;
  }
  
  body::after {
    background-size: 20px 20px;
  }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  html {
    scroll-behavior: auto;
  }
}

@media (prefers-contrast: high) {
  :root {
    --quantum-text: #ffffff;
    --quantum-border: #ffffff;
    --glass-border-light: #ffffff;
    --glass-border-medium: #ffffff;
  }
}
