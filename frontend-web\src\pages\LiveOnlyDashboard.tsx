import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Box, CircularProgress, Typography } from '@mui/material';

const LiveOnlyDashboard: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  // Redirect to the main dashboard with sidebar navigation
  // This ensures Live users get the proper dashboard with full sidebar navigation
  useEffect(() => {
    // Redirect to the main dashboard route which will use UnifiedDashboardLayout with proper sidebar
    navigate('/dashboard', { replace: true });
  }, [navigate]);

  // Show loading while redirecting
  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)'
      }}
    >
      <Box sx={{ textAlign: 'center', color: 'white' }}>
        <CircularProgress sx={{ color: 'white', mb: 2 }} />
        <Typography variant="h6">
          Loading AgriIntel Dashboard...
        </Typography>
        <Typography variant="body2" sx={{ opacity: 0.8, mt: 1 }}>
          Welcome, {user?.username || 'User'}
        </Typography>
      </Box>
    </Box>
  );
};

export default LiveOnlyDashboard;
