/**
 * AgriIntel 2026 - Quantum Form System
 * Next-Generation Form Components with Neural Networks & Glassmorphism
 */

/* ===== QUANTUM FORM BASE ===== */
.agri-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.agri-form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  position: relative;
}

.agri-form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-lg);
}

/* ===== QUANTUM LABELS ===== */
.agri-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--quantum-text);
  margin-bottom: var(--space-xs);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.agri-label-required::after {
  content: '*';
  color: #ff6b6b;
  font-weight: bold;
}

.agri-label-optional {
  color: var(--quantum-text-secondary);
  font-weight: 400;
  font-size: 0.75rem;
}

/* ===== QUANTUM INPUTS ===== */
.agri-input {
  width: 100%;
  padding: var(--space-md) var(--space-lg);
  background: var(--glass-light);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border-light);
  border-radius: var(--radius-lg);
  color: var(--quantum-text);
  font-size: 1rem;
  font-family: var(--font-quantum);
  transition: all var(--timing-normal);
  position: relative;
}

.agri-input::placeholder {
  color: var(--quantum-text-muted);
  font-weight: 400;
}

.agri-input:focus {
  outline: none;
  border-color: rgba(0, 245, 255, 0.5);
  background: var(--glass-medium);
  box-shadow: 
    0 0 0 3px rgba(0, 245, 255, 0.1),
    0 8px 32px rgba(0, 245, 255, 0.15);
  transform: translateY(-2px);
}

.agri-input:hover:not(:focus) {
  border-color: var(--glass-border-medium);
  background: var(--glass-medium);
}

/* ===== QUANTUM INPUT VARIANTS ===== */
.agri-input-sm {
  padding: var(--space-sm) var(--space-md);
  font-size: 0.875rem;
  border-radius: var(--radius-md);
}

.agri-input-lg {
  padding: var(--space-lg) var(--space-xl);
  font-size: 1.125rem;
  border-radius: var(--radius-xl);
}

.agri-input-neural {
  background: var(--glass-ultra-light);
  border: 2px solid transparent;
  background-clip: padding-box;
  position: relative;
}

.agri-input-neural::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 2px;
  background: var(--neural-primary);
  border-radius: inherit;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0;
  transition: opacity var(--timing-normal);
}

.agri-input-neural:focus::before {
  opacity: 1;
}

/* ===== QUANTUM TEXTAREA ===== */
.agri-textarea {
  min-height: 120px;
  resize: vertical;
  font-family: var(--font-quantum);
  line-height: 1.6;
}

.agri-textarea-lg {
  min-height: 200px;
}

/* ===== QUANTUM SELECT ===== */
.agri-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%2300f5ff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--space-md) center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: var(--space-2xl);
  cursor: pointer;
}

.agri-select:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
}

/* ===== QUANTUM CHECKBOX & RADIO ===== */
.agri-checkbox,
.agri-radio {
  appearance: none;
  width: 20px;
  height: 20px;
  background: var(--glass-light);
  border: 2px solid var(--glass-border-light);
  border-radius: var(--radius-sm);
  position: relative;
  cursor: pointer;
  transition: all var(--timing-fast);
}

.agri-radio {
  border-radius: var(--radius-full);
}

.agri-checkbox:checked,
.agri-radio:checked {
  background: var(--neural-primary);
  border-color: rgba(0, 245, 255, 0.5);
  transform: scale(1.1);
}

.agri-checkbox:checked::before {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.agri-radio:checked::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8px;
  height: 8px;
  background: white;
  border-radius: var(--radius-full);
  transform: translate(-50%, -50%);
}

.agri-checkbox:hover,
.agri-radio:hover {
  border-color: var(--glass-border-medium);
  transform: scale(1.05);
}

/* ===== QUANTUM FORM CONTROLS ===== */
.agri-form-control {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  cursor: pointer;
  padding: var(--space-sm);
  border-radius: var(--radius-lg);
  transition: all var(--timing-fast);
}

.agri-form-control:hover {
  background: var(--glass-ultra-light);
}

.agri-form-control-label {
  font-size: 0.875rem;
  color: var(--quantum-text);
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;
}

/* ===== QUANTUM INPUT GROUPS ===== */
.agri-input-group {
  display: flex;
  position: relative;
}

.agri-input-group .agri-input {
  border-radius: 0;
}

.agri-input-group .agri-input:first-child {
  border-radius: var(--radius-lg) 0 0 var(--radius-lg);
}

.agri-input-group .agri-input:last-child {
  border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
}

.agri-input-group-addon {
  display: flex;
  align-items: center;
  padding: var(--space-md) var(--space-lg);
  background: var(--glass-medium);
  border: 1px solid var(--glass-border-light);
  color: var(--quantum-text-secondary);
  font-size: 0.875rem;
  white-space: nowrap;
}

.agri-input-group-addon:first-child {
  border-radius: var(--radius-lg) 0 0 var(--radius-lg);
  border-right: none;
}

.agri-input-group-addon:last-child {
  border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
  border-left: none;
}

/* ===== QUANTUM FORM VALIDATION ===== */
.agri-input-error {
  border-color: rgba(255, 107, 107, 0.5);
  background: rgba(255, 107, 107, 0.05);
}

.agri-input-error:focus {
  border-color: rgba(255, 107, 107, 0.7);
  box-shadow: 
    0 0 0 3px rgba(255, 107, 107, 0.1),
    0 8px 32px rgba(255, 107, 107, 0.15);
}

.agri-input-success {
  border-color: rgba(0, 255, 136, 0.5);
  background: rgba(0, 255, 136, 0.05);
}

.agri-input-success:focus {
  border-color: rgba(0, 255, 136, 0.7);
  box-shadow: 
    0 0 0 3px rgba(0, 255, 136, 0.1),
    0 8px 32px rgba(0, 255, 136, 0.15);
}

.agri-form-error {
  color: #ff6b6b;
  font-size: 0.75rem;
  margin-top: var(--space-xs);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.agri-form-success {
  color: #00ff88;
  font-size: 0.75rem;
  margin-top: var(--space-xs);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.agri-form-help {
  color: var(--quantum-text-muted);
  font-size: 0.75rem;
  margin-top: var(--space-xs);
}

/* ===== QUANTUM FORM ACTIONS ===== */
.agri-form-actions {
  display: flex;
  gap: var(--space-md);
  align-items: center;
  justify-content: flex-end;
  padding-top: var(--space-lg);
  border-top: 1px solid var(--glass-border-light);
  margin-top: var(--space-lg);
}

.agri-form-actions-left {
  justify-content: flex-start;
}

.agri-form-actions-center {
  justify-content: center;
}

.agri-form-actions-between {
  justify-content: space-between;
}

/* ===== RESPONSIVE QUANTUM FORMS ===== */
@media (max-width: 768px) {
  .agri-form-row {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }
  
  .agri-form-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .agri-input-group {
    flex-direction: column;
  }
  
  .agri-input-group .agri-input,
  .agri-input-group-addon {
    border-radius: var(--radius-lg);
    border: 1px solid var(--glass-border-light);
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  .agri-input,
  .agri-textarea,
  .agri-select,
  .agri-checkbox,
  .agri-radio {
    transition: none !important;
    transform: none !important;
  }
  
  .agri-input:focus,
  .agri-checkbox:checked,
  .agri-radio:checked {
    transform: none !important;
  }
}
