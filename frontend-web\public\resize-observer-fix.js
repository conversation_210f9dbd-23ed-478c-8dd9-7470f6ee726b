/**
 * Ultimate ResizeObserver Error Suppression Script
 * This script runs before React and completely eliminates ResizeObserver loop errors
 */

(function() {
  'use strict';

  // Store original methods
  const originalConsoleError = console.error;
  const originalConsoleWarn = console.warn;
  const originalConsoleLog = console.log;
  const originalWindowError = window.onerror;

  // Function to check if error is ResizeObserver related
  function isResizeObserverError(message) {
    if (typeof message !== 'string') return false;
    return message.includes('ResizeObserver loop') || 
           message.includes('ResizeObserver') ||
           message.includes('undelivered notifications');
  }

  // Override console methods
  console.error = function(...args) {
    const message = args[0];
    if (isResizeObserverError(message) || 
        (typeof message === 'object' && message?.message && isResizeObserverError(message.message))) {
      return; // Completely suppress
    }
    originalConsoleError.apply(console, args);
  };

  console.warn = function(...args) {
    const message = args[0];
    if (isResizeObserverError(message)) {
      return; // Completely suppress
    }
    originalConsoleWarn.apply(console, args);
  };

  console.log = function(...args) {
    const message = args[0];
    if (isResizeObserverError(message)) {
      return; // Completely suppress
    }
    originalConsoleLog.apply(console, args);
  };

  // Override window.onerror
  window.onerror = function(message, source, lineno, colno, error) {
    if (isResizeObserverError(message)) {
      return true; // Prevent default error handling
    }
    if (originalWindowError) {
      return originalWindowError(message, source, lineno, colno, error);
    }
    return false;
  };

  // Override error event listener
  window.addEventListener('error', function(event) {
    if (isResizeObserverError(event.message)) {
      event.preventDefault();
      event.stopPropagation();
      event.stopImmediatePropagation();
      return false;
    }
  }, true);

  // Override unhandled promise rejection
  window.addEventListener('unhandledrejection', function(event) {
    const reason = event.reason;
    if (reason && 
        ((typeof reason === 'string' && isResizeObserverError(reason)) ||
         (typeof reason === 'object' && reason.message && isResizeObserverError(reason.message)))) {
      event.preventDefault();
      event.stopPropagation();
      event.stopImmediatePropagation();
    }
  }, true);

  // Override ResizeObserver constructor
  if (typeof window.ResizeObserver !== 'undefined') {
    const OriginalResizeObserver = window.ResizeObserver;
    
    window.ResizeObserver = class SafeResizeObserver {
      constructor(callback) {
        const safeCallback = (entries, observer) => {
          // Use requestIdleCallback if available, otherwise setTimeout
          const scheduleCallback = window.requestIdleCallback || 
            ((cb) => setTimeout(cb, 16));
          
          scheduleCallback(() => {
            try {
              callback(entries, observer);
            } catch (error) {
              if (!isResizeObserverError(error?.message)) {
                throw error;
              }
            }
          });
        };
        
        try {
          this.observer = new OriginalResizeObserver(safeCallback);
        } catch (error) {
          if (!isResizeObserverError(error?.message)) {
            throw error;
          }
          this.observer = null;
        }
      }
      
      observe(target, options) {
        if (this.observer) {
          try {
            this.observer.observe(target, options);
          } catch (error) {
            if (!isResizeObserverError(error?.message)) {
              throw error;
            }
          }
        }
      }
      
      unobserve(target) {
        if (this.observer) {
          try {
            this.observer.unobserve(target);
          } catch (error) {
            if (!isResizeObserverError(error?.message)) {
              throw error;
            }
          }
        }
      }
      
      disconnect() {
        if (this.observer) {
          try {
            this.observer.disconnect();
          } catch (error) {
            if (!isResizeObserverError(error?.message)) {
              throw error;
            }
          }
        }
      }
    };
  }

  // Patch setTimeout and setInterval
  const originalSetTimeout = window.setTimeout;
  const originalSetInterval = window.setInterval;

  window.setTimeout = function(callback, delay, ...args) {
    const wrappedCallback = function(...callbackArgs) {
      try {
        return callback(...callbackArgs);
      } catch (error) {
        if (!isResizeObserverError(error?.message)) {
          throw error;
        }
      }
    };
    return originalSetTimeout(wrappedCallback, delay, ...args);
  };

  window.setInterval = function(callback, delay, ...args) {
    const wrappedCallback = function(...callbackArgs) {
      try {
        return callback(...callbackArgs);
      } catch (error) {
        if (!isResizeObserverError(error?.message)) {
          throw error;
        }
      }
    };
    return originalSetInterval(wrappedCallback, delay, ...args);
  };

  // Patch requestAnimationFrame
  const originalRAF = window.requestAnimationFrame;
  window.requestAnimationFrame = function(callback) {
    const wrappedCallback = function(time) {
      try {
        return callback(time);
      } catch (error) {
        if (!isResizeObserverError(error?.message)) {
          throw error;
        }
      }
    };
    return originalRAF(wrappedCallback);
  };

  console.log('🛡️ Ultimate ResizeObserver error suppression activated');
})();
