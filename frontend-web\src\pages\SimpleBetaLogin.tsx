import React, { useEffect } from 'react';
import '../styles/simple-beta-login.css';

const SimpleBetaLogin: React.FC = () => {
  useEffect(() => {
    // Auto redirect to beta dashboard after 2 seconds
    const timer = setTimeout(() => {
      window.location.href = '/beta-dashboard';
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="simple-beta-login">
      <div className="simple-beta-login-container">
        <h1 className="simple-beta-login-title">🚀 AgriIntel BETA</h1>

        <div className="simple-beta-login-welcome">
          <div className="simple-beta-login-welcome-icon">🎉</div>
          <h2>Welcome to BETA!</h2>
          <p>Accessing limited features...</p>
        </div>

        <div className="simple-beta-login-loading">
          <div className="simple-beta-login-dot"></div>
          <div className="simple-beta-login-dot"></div>
          <div className="simple-beta-login-dot"></div>
        </div>

        <a
          href="/beta-dashboard"
          className="simple-beta-login-button"
        >
          Go to BETA Dashboard
        </a>

        <div className="simple-beta-login-features">
          <p>🎯 BETA Features Available:</p>
          <ul className="simple-beta-login-features-list">
            <li>✅ Up to 50 animals</li>
            <li>✅ Basic health monitoring</li>
            <li>✅ Simple reports</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default SimpleBetaLogin;
