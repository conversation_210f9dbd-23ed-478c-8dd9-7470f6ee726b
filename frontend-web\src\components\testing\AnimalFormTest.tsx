import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Al<PERSON>,
  <PERSON>,
  <PERSON>ack,
  Divider,
  useTheme,
  alpha
} from '@mui/material';
import {
  Pets,
  Add,
  CheckCircle,
  Error,
  Warning,
  Info
} from '@mui/icons-material';
import AddAnimalForm from '../animals/AddAnimalForm';
import { useSnackbar } from '../../hooks/useSnackbar';

interface TestResult {
  test: string;
  status: 'pass' | 'fail' | 'warning' | 'info';
  message: string;
  timestamp: Date;
}

const AnimalFormTest: React.FC = () => {
  const theme = useTheme();
  const { showSnackbar } = useSnackbar();
  const [formOpen, setFormOpen] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);

  const addTestResult = (test: string, status: TestResult['status'], message: string) => {
    setTestResults(prev => [...prev, {
      test,
      status,
      message,
      timestamp: new Date()
    }]);
  };

  const runFormTests = async () => {
    setIsRunningTests(true);
    setTestResults([]);

    // Test 1: Form Component Loading
    try {
      addTestResult('Form Component', 'pass', 'AddAnimalForm component loaded successfully');
    } catch (error) {
      addTestResult('Form Component', 'fail', 'Failed to load AddAnimalForm component');
    }

    // Test 2: Form Validation
    addTestResult('Form Validation', 'info', 'Form validation rules are configured');

    // Test 3: API Endpoint Check
    try {
      const response = await fetch('/api/animals', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      if (response.ok) {
        addTestResult('API Endpoint', 'pass', 'Animals API endpoint is accessible');
      } else {
        addTestResult('API Endpoint', 'warning', `API returned status: ${response.status}`);
      }
    } catch (error) {
      addTestResult('API Endpoint', 'fail', 'Failed to connect to animals API');
    }

    // Test 4: Form Fields
    const requiredFields = ['tagNumber', 'species', 'gender', 'acquisitionDate'];
    addTestResult('Required Fields', 'pass', `Required fields configured: ${requiredFields.join(', ')}`);

    // Test 5: Species Options
    const speciesOptions = ['cattle', 'sheep', 'goat', 'pig', 'chicken', 'other'];
    addTestResult('Species Options', 'pass', `Species options available: ${speciesOptions.join(', ')}`);

    // Test 6: Gender Options
    const genderOptions = ['male', 'female', 'unknown'];
    addTestResult('Gender Options', 'pass', `Gender options available: ${genderOptions.join(', ')}`);

    setIsRunningTests(false);
    showSnackbar('Animal form tests completed', 'success');
  };

  const handleFormSuccess = () => {
    addTestResult('Form Submission', 'pass', 'Animal form submitted successfully');
    showSnackbar('Animal added successfully!', 'success');
    setFormOpen(false);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return <CheckCircle sx={{ color: theme.palette.success.main }} />;
      case 'fail':
        return <Error sx={{ color: theme.palette.error.main }} />;
      case 'warning':
        return <Warning sx={{ color: theme.palette.warning.main }} />;
      case 'info':
        return <Info sx={{ color: theme.palette.info.main }} />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return theme.palette.success.main;
      case 'fail':
        return theme.palette.error.main;
      case 'warning':
        return theme.palette.warning.main;
      case 'info':
        return theme.palette.info.main;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Card
        sx={{
          background: alpha(theme.palette.background.paper, 0.9),
          backdropFilter: 'blur(20px)',
          border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
        }}
      >
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
            <Pets sx={{ fontSize: 40, color: theme.palette.primary.main }} />
            <Box>
              <Typography variant="h4" fontWeight="bold">
                Animal Form Testing
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                Test animal adding functionality and API endpoints
              </Typography>
            </Box>
          </Box>

          <Stack direction="row" spacing={2} sx={{ mb: 3 }}>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setFormOpen(true)}
              sx={{
                background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
                '&:hover': {
                  background: `linear-gradient(135deg, ${theme.palette.primary.dark}, ${theme.palette.primary.main})`
                }
              }}
            >
              Test Add Animal Form
            </Button>
            
            <Button
              variant="outlined"
              onClick={runFormTests}
              disabled={isRunningTests}
              sx={{
                borderColor: theme.palette.secondary.main,
                color: theme.palette.secondary.main,
                '&:hover': {
                  borderColor: theme.palette.secondary.dark,
                  background: alpha(theme.palette.secondary.main, 0.1)
                }
              }}
            >
              {isRunningTests ? 'Running Tests...' : 'Run API Tests'}
            </Button>
          </Stack>

          {testResults.length > 0 && (
            <>
              <Divider sx={{ my: 3 }} />
              <Typography variant="h6" gutterBottom>
                Test Results
              </Typography>
              
              <Stack spacing={2}>
                {testResults.map((result, index) => (
                  <Alert
                    key={index}
                    severity={result.status === 'pass' ? 'success' : 
                             result.status === 'fail' ? 'error' :
                             result.status === 'warning' ? 'warning' : 'info'}
                    icon={getStatusIcon(result.status)}
                    sx={{
                      '& .MuiAlert-message': {
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        width: '100%'
                      }
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="subtitle2" fontWeight="bold">
                          {result.test}
                        </Typography>
                        <Typography variant="body2">
                          {result.message}
                        </Typography>
                      </Box>
                      <Chip
                        label={result.status.toUpperCase()}
                        size="small"
                        sx={{
                          backgroundColor: getStatusColor(result.status),
                          color: 'white',
                          fontWeight: 'bold'
                        }}
                      />
                    </Box>
                  </Alert>
                ))}
              </Stack>
            </>
          )}
        </CardContent>
      </Card>

      {/* Add Animal Form Dialog */}
      <AddAnimalForm
        open={formOpen}
        onClose={() => setFormOpen(false)}
        onSuccess={handleFormSuccess}
      />
    </Box>
  );
};

export default AnimalFormTest;
