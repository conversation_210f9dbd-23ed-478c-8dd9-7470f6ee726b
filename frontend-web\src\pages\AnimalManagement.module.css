.pageContainer {
  display: flex;
  height: 100vh;
  background-color: #ecf0f1;
}

.sidebar {
  width: 250px;
  background-color: #34495e;
  color: #ecf0f1;
  padding: 20px;
}

.sidebarTitle {
  color: #ecf0f1;
  margin-bottom: 30px;
}

.navList {
  list-style: none;
  padding: 0;
}

.navItem {
  margin-bottom: 15px;
}

.navLink {
  color: #ecf0f1;
  text-decoration: none;
  background: none;
  border: none;
  padding: 0;
  font: inherit;
  cursor: pointer;
  text-align: left;
}

.mainContent {
  flex: 1;
  padding: 40px;
}

.mainTitle {
  color: #2c3e50;
  border-bottom: 2px solid #2c3e50;
  padding-bottom: 10px;
}

.actionButton {
  margin-top: 20px;
  padding: 10px 20px;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.addButton {
  background-color: #27ae60;
}

.updateButton {
  background-color: #3498db;
  margin-left: 10px;
}

.tableContainer {
  margin-top: 30px;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.tableHeader {
  background-color: #34495e;
  color: white;
}

.tableHeaderCell {
  padding: 10px;
}

.tableRow {
  border-bottom: 1px solid #bdc3c7;
}

.tableCell {
  padding: 10px;
  text-align: center;
}

.actionCell {
  padding: 10px;
  text-align: center;
}

.editButton {
  margin-right: 10px;
  padding: 5px 10px;
  background-color: #2980b9;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}

.deleteButton {
  padding: 5px 10px;
  background-color: #c0392b;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.modalContent {
  background-color: white;
  padding: 20px;
  border-radius: 5px;
  width: 400px;
}

.formGroup {
  margin-bottom: 15px;
}

.formInput {
  width: 100%;
  padding: 8px;
  box-sizing: border-box;
}

.modalActions {
  text-align: right;
}

.cancelButton {
  margin-right: 10px;
  padding: 10px 20px;
  background-color: #7f8c8d;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.submitButton {
  padding: 10px 20px;
  background-color: #27ae60;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}