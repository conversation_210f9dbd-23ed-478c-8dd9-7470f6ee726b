import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Tabs,
  Tab,
  Card,
  CardContent,
  Grid,
  Chip,
  useTheme,
  alpha
} from '@mui/material';
import {
  BugReport,
  Pets,
  Language,
  Api,
  Security,
  Speed
} from '@mui/icons-material';
import AnimalFormTest from '../../components/testing/AnimalFormTest';
import LanguageSelectorTest from '../../components/testing/LanguageSelectorTest';
import AccessControlTest from '../../components/testing/AccessControlTest';
import RoutingTest from '../../components/testing/RoutingTest';
import PerformanceTest from '../../components/testing/PerformanceTest';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`testing-tabpanel-${index}`}
      aria-labelledby={`testing-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `testing-tab-${index}`,
    'aria-controls': `testing-tabpanel-${index}`,
  };
}

const TestingDashboard: React.FC = () => {
  const theme = useTheme();
  const [value, setValue] = useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  const testCategories = [
    {
      title: 'Animal Forms',
      description: 'Test animal adding forms and validation',
      icon: <Pets />,
      status: 'ready',
      component: <AnimalFormTest />
    },
    {
      title: 'Language Selector',
      description: 'Test language switching functionality',
      icon: <Language />,
      status: 'ready',
      component: <LanguageSelectorTest />
    },
    {
      title: 'Routing & Navigation',
      description: 'Test route resolution and navigation guards',
      icon: <Api />,
      status: 'ready',
      component: <RoutingTest />
    },
    {
      title: 'Access Control',
      description: 'Test user permissions and module access',
      icon: <Security />,
      status: 'ready',
      component: <AccessControlTest />
    },
    {
      title: 'Performance & Recovery',
      description: 'Test app performance and error recovery',
      icon: <Speed />,
      status: 'ready',
      component: <PerformanceTest />
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ready':
        return theme.palette.success.main;
      case 'pending':
        return theme.palette.warning.main;
      case 'failed':
        return theme.palette.error.main;
      default:
        return theme.palette.grey[500];
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
        py: 4
      }}
    >
      <Container maxWidth="xl">
        <Box sx={{ mb: 4 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <BugReport sx={{ fontSize: 48, color: theme.palette.primary.main }} />
            <Box>
              <Typography variant="h3" fontWeight="bold" color="primary">
                AgriIntel Testing Dashboard
              </Typography>
              <Typography variant="h6" color="text.secondary">
                Comprehensive testing suite for all application features
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Test Categories Overview */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          {testCategories.map((category, index) => (
            <Grid item xs={12} sm={6} md={4} lg={2.4} key={index}>
              <Card
                sx={{
                  height: '100%',
                  background: alpha(theme.palette.background.paper, 0.9),
                  backdropFilter: 'blur(20px)',
                  border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: theme.shadows[8]
                  }
                }}
                onClick={() => setValue(index)}
              >
                <CardContent sx={{ textAlign: 'center', p: 2 }}>
                  <Box
                    sx={{
                      width: 60,
                      height: 60,
                      borderRadius: '50%',
                      background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mx: 'auto',
                      mb: 2,
                      color: 'white'
                    }}
                  >
                    {React.cloneElement(category.icon, { fontSize: 'large' })}
                  </Box>
                  
                  <Typography variant="h6" fontWeight="bold" gutterBottom>
                    {category.title}
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {category.description}
                  </Typography>
                  
                  <Chip
                    label={category.status.toUpperCase()}
                    size="small"
                    sx={{
                      backgroundColor: getStatusColor(category.status),
                      color: 'white',
                      fontWeight: 'bold'
                    }}
                  />
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* Test Tabs */}
        <Card
          sx={{
            background: alpha(theme.palette.background.paper, 0.9),
            backdropFilter: 'blur(20px)',
            border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
          }}
        >
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs
              value={value}
              onChange={handleChange}
              aria-label="testing tabs"
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                '& .MuiTab-root': {
                  minHeight: 72,
                  textTransform: 'none',
                  fontSize: '1rem',
                  fontWeight: 500
                }
              }}
            >
              {testCategories.map((category, index) => (
                <Tab
                  key={index}
                  label={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {category.icon}
                      <Box sx={{ textAlign: 'left' }}>
                        <Typography variant="body1" fontWeight="bold">
                          {category.title}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {category.description}
                        </Typography>
                      </Box>
                    </Box>
                  }
                  {...a11yProps(index)}
                />
              ))}
            </Tabs>
          </Box>

          {testCategories.map((category, index) => (
            <TabPanel key={index} value={value} index={index}>
              {category.component}
            </TabPanel>
          ))}
        </Card>
      </Container>
    </Box>
  );
};

export default TestingDashboard;
