/**
 * API Endpoint Testing Script
 * Tests all major API endpoints to ensure they're working correctly
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';
let authToken = null;

// Test credentials
const testCredentials = [
  { username: 'Demo', password: '123' },
  { username: 'admin', password: 'Admin@123' },
  { username: 'Pro', password: '123' }
];

// Color codes for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Test authentication
async function testAuth() {
  log('\n=== Testing Authentication ===', 'blue');
  
  for (const creds of testCredentials) {
    try {
      log(`Testing login with ${creds.username}/${creds.password}...`, 'yellow');
      
      const response = await axios.post(`${BASE_URL}/auth/login`, creds);
      
      if (response.data.success) {
        log(`✅ Login successful for ${creds.username}`, 'green');
        if (creds.username === 'admin') {
          authToken = response.data.token;
          log(`🔑 Auth token saved for admin user`, 'green');
        }
      } else {
        log(`❌ Login failed for ${creds.username}`, 'red');
      }
    } catch (error) {
      log(`❌ Login error for ${creds.username}: ${error.message}`, 'red');
      if (error.response) {
        log(`   Status: ${error.response.status}`, 'red');
        log(`   Data: ${JSON.stringify(error.response.data)}`, 'red');
      }
      if (error.code) {
        log(`   Code: ${error.code}`, 'red');
      }
    }
  }
}

// Test API status
async function testApiStatus() {
  log('\n=== Testing API Status ===', 'blue');
  
  try {
    const response = await axios.get(`${BASE_URL}/status`);
    if (response.data.success) {
      log('✅ API Status endpoint working', 'green');
      log(`   Version: ${response.data.version}`, 'yellow');
    } else {
      log('❌ API Status endpoint failed', 'red');
    }
  } catch (error) {
    log(`❌ API Status error: ${error.message}`, 'red');
    if (error.response) {
      log(`   Status: ${error.response.status}`, 'red');
    }
    if (error.code) {
      log(`   Code: ${error.code}`, 'red');
    }
  }
}

// Test Animals endpoints
async function testAnimals() {
  log('\n=== Testing Animals Endpoints ===', 'blue');
  
  const endpoints = [
    { method: 'GET', path: '/animals', desc: 'Get all animals' },
    { method: 'GET', path: '/animals/statistics', desc: 'Get animal statistics' }
  ];
  
  for (const endpoint of endpoints) {
    try {
      log(`Testing ${endpoint.method} ${endpoint.path}...`, 'yellow');
      
      const config = authToken ? {
        headers: { Authorization: `Bearer ${authToken}` }
      } : {};
      
      const response = await axios({
        method: endpoint.method.toLowerCase(),
        url: `${BASE_URL}${endpoint.path}`,
        ...config
      });
      
      if (response.status === 200) {
        log(`✅ ${endpoint.desc} - Success`, 'green');
        if (response.data.data && Array.isArray(response.data.data)) {
          log(`   Found ${response.data.data.length} records`, 'yellow');
        }
      } else {
        log(`❌ ${endpoint.desc} - Failed`, 'red');
      }
    } catch (error) {
      log(`❌ ${endpoint.desc} - Error: ${error.response?.status || error.message}`, 'red');
    }
  }
}

// Test Health endpoints
async function testHealth() {
  log('\n=== Testing Health Endpoints ===', 'blue');
  
  const endpoints = [
    { method: 'GET', path: '/health', desc: 'Get health records' },
    { method: 'GET', path: '/health/statistics', desc: 'Get health statistics' }
  ];
  
  for (const endpoint of endpoints) {
    try {
      log(`Testing ${endpoint.method} ${endpoint.path}...`, 'yellow');
      
      const config = authToken ? {
        headers: { Authorization: `Bearer ${authToken}` }
      } : {};
      
      const response = await axios({
        method: endpoint.method.toLowerCase(),
        url: `${BASE_URL}${endpoint.path}`,
        ...config
      });
      
      if (response.status === 200) {
        log(`✅ ${endpoint.desc} - Success`, 'green');
      } else {
        log(`❌ ${endpoint.desc} - Failed`, 'red');
      }
    } catch (error) {
      log(`❌ ${endpoint.desc} - Error: ${error.response?.status || error.message}`, 'red');
    }
  }
}

// Test Financial endpoints
async function testFinancial() {
  log('\n=== Testing Financial Endpoints ===', 'blue');
  
  const endpoints = [
    { method: 'GET', path: '/financial', desc: 'Get financial records' },
    { method: 'GET', path: '/financial/statistics', desc: 'Get financial statistics' }
  ];
  
  for (const endpoint of endpoints) {
    try {
      log(`Testing ${endpoint.method} ${endpoint.path}...`, 'yellow');
      
      const config = authToken ? {
        headers: { Authorization: `Bearer ${authToken}` }
      } : {};
      
      const response = await axios({
        method: endpoint.method.toLowerCase(),
        url: `${BASE_URL}${endpoint.path}`,
        ...config
      });
      
      if (response.status === 200) {
        log(`✅ ${endpoint.desc} - Success`, 'green');
      } else {
        log(`❌ ${endpoint.desc} - Failed`, 'red');
      }
    } catch (error) {
      log(`❌ ${endpoint.desc} - Error: ${error.response?.status || error.message}`, 'red');
    }
  }
}

// Test Feeding endpoints
async function testFeeding() {
  log('\n=== Testing Feeding Endpoints ===', 'blue');
  
  const endpoints = [
    { method: 'GET', path: '/feeding', desc: 'Get feeding records' },
    { method: 'GET', path: '/feeding/records', desc: 'Get feeding records list' }
  ];
  
  for (const endpoint of endpoints) {
    try {
      log(`Testing ${endpoint.method} ${endpoint.path}...`, 'yellow');
      
      const config = authToken ? {
        headers: { Authorization: `Bearer ${authToken}` }
      } : {};
      
      const response = await axios({
        method: endpoint.method.toLowerCase(),
        url: `${BASE_URL}${endpoint.path}`,
        ...config
      });
      
      if (response.status === 200) {
        log(`✅ ${endpoint.desc} - Success`, 'green');
      } else {
        log(`❌ ${endpoint.desc} - Failed`, 'red');
      }
    } catch (error) {
      log(`❌ ${endpoint.desc} - Error: ${error.response?.status || error.message}`, 'red');
    }
  }
}

// Main test function
async function runTests() {
  log('🚀 Starting API Endpoint Tests...', 'blue');
  log('=====================================', 'blue');
  
  await testApiStatus();
  await testAuth();
  await testAnimals();
  await testHealth();
  await testFinancial();
  await testFeeding();
  
  log('\n=====================================', 'blue');
  log('✅ API Endpoint Tests Complete!', 'green');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(error => {
    log(`❌ Test execution failed: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = { runTests };
