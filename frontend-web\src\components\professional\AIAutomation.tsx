import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Chip,
  LinearProgress,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Switch,
  FormControlLabel,
  Divider
} from '@mui/material';
import {
  AutoAwesome,
  TrendingUp,
  Schedule,
  Notifications,
  Analytics,
  SmartToy,
  CheckCircle,
  Warning,
  Info
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';

interface AITask {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  estimatedCompletion: string;
  priority: 'low' | 'medium' | 'high';
}

interface AIInsight {
  id: string;
  type: 'health' | 'feeding' | 'breeding' | 'financial';
  title: string;
  description: string;
  confidence: number;
  actionRequired: boolean;
}

const AIAutomation: React.FC = () => {
  const { user } = useAuth();
  const [aiTasks, setAiTasks] = useState<AITask[]>([]);
  const [insights, setInsights] = useState<AIInsight[]>([]);
  const [automationEnabled, setAutomationEnabled] = useState(true);
  const [loading, setLoading] = useState(true);

  // Mock AI tasks and insights
  useEffect(() => {
    const mockTasks: AITask[] = [
      {
        id: '1',
        title: 'Health Risk Assessment',
        description: 'Analyzing animal health patterns and predicting potential issues',
        status: 'running',
        progress: 75,
        estimatedCompletion: '5 minutes',
        priority: 'high'
      },
      {
        id: '2',
        title: 'Feed Optimization',
        description: 'Calculating optimal feed schedules based on animal performance',
        status: 'completed',
        progress: 100,
        estimatedCompletion: 'Completed',
        priority: 'medium'
      },
      {
        id: '3',
        title: 'Breeding Recommendations',
        description: 'Generating breeding pairs based on genetic analysis',
        status: 'pending',
        progress: 0,
        estimatedCompletion: '15 minutes',
        priority: 'low'
      }
    ];

    const mockInsights: AIInsight[] = [
      {
        id: '1',
        type: 'health',
        title: 'Vaccination Schedule Alert',
        description: '12 animals require vaccination within the next 7 days',
        confidence: 95,
        actionRequired: true
      },
      {
        id: '2',
        type: 'feeding',
        title: 'Feed Efficiency Improvement',
        description: 'Switching to Feed Mix B could improve weight gain by 15%',
        confidence: 87,
        actionRequired: false
      },
      {
        id: '3',
        type: 'financial',
        title: 'Market Opportunity',
        description: 'Current market prices are 8% above average - consider selling',
        confidence: 92,
        actionRequired: false
      }
    ];

    setAiTasks(mockTasks);
    setInsights(mockInsights);
    setLoading(false);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'running': return 'primary';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      default: return 'default';
    }
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'health': return <CheckCircle color="success" />;
      case 'feeding': return <TrendingUp color="primary" />;
      case 'breeding': return <Analytics color="secondary" />;
      case 'financial': return <Warning color="warning" />;
      default: return <Info color="info" />;
    }
  };

  if (user?.role !== 'professional' && user?.role !== 'admin') {
    return (
      <Alert severity="warning">
        AI Automation features are available for Professional tier users only.
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
          AI Automation Dashboard
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Intelligent automation and predictive insights for your livestock operation
        </Typography>
      </Box>

      {/* Automation Toggle */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                AI Automation Status
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Enable or disable automated AI tasks and insights
              </Typography>
            </Box>
            <FormControlLabel
              control={
                <Switch
                  checked={automationEnabled}
                  onChange={(e) => setAutomationEnabled(e.target.checked)}
                  color="primary"
                />
              }
              label={automationEnabled ? 'Enabled' : 'Disabled'}
            />
          </Box>
        </CardContent>
      </Card>

      <Grid container spacing={3}>
        {/* Active AI Tasks */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <SmartToy sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Active AI Tasks
                </Typography>
              </Box>
              
              <List>
                {aiTasks.map((task, index) => (
                  <motion.div
                    key={task.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <ListItem sx={{ px: 0 }}>
                      <Box sx={{ width: '100%' }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                            {task.title}
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Chip
                              label={task.status}
                              size="small"
                              color={getStatusColor(task.status) as any}
                            />
                            <Chip
                              label={task.priority}
                              size="small"
                              color={getPriorityColor(task.priority) as any}
                            />
                          </Box>
                        </Box>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                          {task.description}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <LinearProgress
                            variant="determinate"
                            value={task.progress}
                            sx={{ flexGrow: 1, height: 6, borderRadius: 3 }}
                          />
                          <Typography variant="caption" color="text.secondary">
                            {task.estimatedCompletion}
                          </Typography>
                        </Box>
                      </Box>
                    </ListItem>
                    {index < aiTasks.length - 1 && <Divider />}
                  </motion.div>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* AI Insights */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <AutoAwesome sx={{ mr: 1, color: 'secondary.main' }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  AI Insights
                </Typography>
              </Box>
              
              <List>
                {insights.map((insight, index) => (
                  <motion.div
                    key={insight.id}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <ListItem sx={{ px: 0 }}>
                      <ListItemIcon>
                        {getInsightIcon(insight.type)}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                              {insight.title}
                            </Typography>
                            <Chip
                              label={`${insight.confidence}% confidence`}
                              size="small"
                              color="info"
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              {insight.description}
                            </Typography>
                            {insight.actionRequired && (
                              <Button
                                size="small"
                                variant="outlined"
                                sx={{ mt: 1 }}
                                startIcon={<Schedule />}
                              >
                                Take Action
                              </Button>
                            )}
                          </Box>
                        }
                      />
                    </ListItem>
                    {index < insights.length - 1 && <Divider />}
                  </motion.div>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AIAutomation;

// Export types for use in other components
export type { AITask, AIInsight };
