/**
 * Glassmorphism Effects for AgriIntel
 * Modern glass-like UI components with backdrop blur and transparency
 */

/* ===== BASE GLASSMORPHISM CLASSES ===== */

.glass-base {
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-light {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.25);
}

.glass-medium {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.35);
}

.glass-heavy {
  background: rgba(255, 255, 255, 0.35);
  border-color: rgba(255, 255, 255, 0.45);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.25);
  border-color: rgba(255, 255, 255, 0.15);
}

/* ===== GLASSMORPHISM CARDS ===== */

.glass-card {
  @extend .glass-base;
  @extend .glass-light;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
}

.glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  z-index: 1;
}

.glass-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 255, 255, 0.35);
  background: rgba(255, 255, 255, 0.2);
}

.glass-card-interactive {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-card-interactive:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
}

.glass-card-interactive:active {
  transform: translateY(-2px) scale(1.01);
}

/* ===== GLASSMORPHISM NAVIGATION ===== */

.glass-nav {
  @extend .glass-base;
  @extend .glass-light;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  border-left: none;
  border-right: none;
  border-top: none;
}

.glass-nav::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

/* ===== GLASSMORPHISM BUTTONS ===== */

.glass-btn {
  @extend .glass-base;
  @extend .glass-light;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.glass-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.glass-btn:hover::before {
  left: 100%;
}

.glass-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.25);
}

.glass-btn:active {
  transform: translateY(0);
}

/* ===== GLASSMORPHISM FORMS ===== */

.glass-form {
  @extend .glass-base;
  @extend .glass-light;
  border-radius: 16px;
  padding: 32px;
}

.glass-input {
  @extend .glass-base;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 12px 16px;
  color: white;
  font-size: 16px;
  width: 100%;
  transition: all 0.3s ease;
}

.glass-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.glass-input:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

.glass-input:hover {
  border-color: rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.12);
}

/* ===== GLASSMORPHISM MODALS ===== */

.glass-modal {
  @extend .glass-base;
  @extend .glass-medium;
  border-radius: 20px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
}

.glass-modal-backdrop {
  background: rgba(0, 0, 0, 0.5);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
}

/* ===== GLASSMORPHISM SIDEBAR ===== */

.glass-sidebar {
  @extend .glass-base;
  @extend .glass-light;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  border-left: none;
  border-top: none;
  border-bottom: none;
}

/* ===== TIER-SPECIFIC GLASSMORPHISM ===== */

/* BETA V1 Glass Effects */
.glass-beta {
  background: rgba(245, 124, 0, 0.15);
  border-color: rgba(245, 124, 0, 0.25);
  box-shadow: 0 8px 32px rgba(245, 124, 0, 0.12);
}

.glass-beta:hover {
  background: rgba(245, 124, 0, 0.2);
  border-color: rgba(245, 124, 0, 0.35);
  box-shadow: 0 12px 40px rgba(245, 124, 0, 0.15);
}

/* Professional V1 Glass Effects */
.glass-professional {
  background: rgba(46, 125, 50, 0.15);
  border-color: rgba(46, 125, 50, 0.25);
  box-shadow: 0 8px 32px rgba(46, 125, 50, 0.12);
}

.glass-professional:hover {
  background: rgba(46, 125, 50, 0.2);
  border-color: rgba(46, 125, 50, 0.35);
  box-shadow: 0 12px 40px rgba(46, 125, 50, 0.15);
}

/* ===== GLASSMORPHISM ANIMATIONS ===== */

@keyframes glassShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.glass-shimmer {
  position: relative;
  overflow: hidden;
}

.glass-shimmer::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  background-size: 200% 100%;
  animation: glassShimmer 2s infinite;
}

@keyframes glassFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.glass-float {
  animation: glassFloat 3s ease-in-out infinite;
}

@keyframes glassPulse {
  0%, 100% {
    box-shadow: 0 8px 32px rgba(255, 255, 255, 0.1);
  }
  50% {
    box-shadow: 0 8px 32px rgba(255, 255, 255, 0.2);
  }
}

.glass-pulse {
  animation: glassPulse 2s ease-in-out infinite;
}

/* ===== RESPONSIVE GLASSMORPHISM ===== */

@media (max-width: 768px) {
  .glass-card {
    border-radius: 12px;
  }
  
  .glass-form {
    padding: 24px;
    border-radius: 12px;
  }
  
  .glass-modal {
    border-radius: 16px;
    max-width: 95vw;
  }
  
  .glass-btn {
    padding: 10px 20px;
    border-radius: 10px;
  }
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  .glass-card,
  .glass-btn,
  .glass-input {
    transition: none;
  }
  
  .glass-shimmer::after,
  .glass-float,
  .glass-pulse {
    animation: none;
  }
}

@media (prefers-contrast: high) {
  .glass-base {
    background: rgba(255, 255, 255, 0.9);
    border-color: #000000;
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
  }
  
  .glass-dark {
    background: rgba(0, 0, 0, 0.9);
    border-color: #ffffff;
  }
}

/* ===== BROWSER FALLBACKS ===== */

@supports not (backdrop-filter: blur(20px)) {
  .glass-base {
    background: rgba(255, 255, 255, 0.9);
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
  }
  
  .glass-dark {
    background: rgba(0, 0, 0, 0.9);
  }
}
