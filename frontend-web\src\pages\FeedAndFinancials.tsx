import React, { useState } from 'react';
import { Box, Paper, Typography, TextField, Button, Grid, Select, MenuItem, FormControl, InputLabel, SelectChangeEvent, List, ListItem, ListItemText } from '@mui/material';
import { <PERSON><PERSON>hart, Pie, Cell, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import * as XLSX from 'xlsx';

// Simplified Animal interface based on AnimalManagement.tsx for mock data
interface Animal {
  id: number;
  name: string;
  species: string;
}

// Interface for our feed records
interface FeedRecord {
  id: number;
  animalId: number;
  animalName: string;
  feedType: string;
  quantity: number;
  date: string;
}

// Mock animal data similar to AnimalManagement.tsx
const mockAnimals: Animal[] = [
    { id: 1, name: 'Be<PERSON>', species: 'Cattle' },
    { id: 2, name: '<PERSON>', species: 'She<PERSON>' },
    { id: 3, name: '<PERSON>', species: 'Goat' },
    { id: 4, name: '<PERSON><PERSON><PERSON>', species: 'Cattle' },
];

const FeedAndFinancials: React.FC = () => {
  const [animals] = useState<Animal[]>(mockAnimals);
  const [selectedAnimal, setSelectedAnimal] = useState<string>('');
  const [feedType, setFeedType] = useState<string>('');
  const [quantity, setQuantity] = useState<string>('');
  const [date, setDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [feedRecords, setFeedRecords] = useState<FeedRecord[]>([]);

  const handleAnimalChange = (event: SelectChangeEvent<string>) => {
    setSelectedAnimal(event.target.value);
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    if (!selectedAnimal || !feedType || !quantity || !date) {
      alert('Please fill out all fields.');
      return;
    }

    const animal = animals.find(a => a.id === parseInt(selectedAnimal, 10));
    if (!animal) return;

    const newRecord: FeedRecord = {
      id: Date.now(),
      animalId: animal.id,
      animalName: animal.name,
      feedType,
      quantity: parseFloat(quantity),
      date,
    };

    setFeedRecords(prevRecords => [...prevRecords, newRecord]);

    // Reset form
    setSelectedAnimal('');
    setFeedType('');
    setQuantity('');
    setDate(new Date().toISOString().split('T')[0]);
  };

  // --- Data Aggregation and Charting ---
  const feedCostPerKg: { [key: string]: number } = {
    'Hay': 0.5,
    'Grain': 0.8,
    'Silage': 0.3,
    'Supplement': 1.2,
    // Add other feed types and their costs here
  };

  const aggregatedData = feedRecords.reduce((acc, record) => {
    const cost = (record.quantity * (feedCostPerKg[record.feedType] || 0.75)); // Default cost if not specified
    if (!acc[record.feedType]) {
      acc[record.feedType] = { totalCost: 0, name: record.feedType };
    }
    acc[record.feedType].totalCost += cost;
    return acc;
  }, {} as { [key: string]: { totalCost: number, name: string } });

  const chartData = Object.values(aggregatedData).map(item => ({...item, totalCost: parseFloat(item.totalCost.toFixed(2))}));
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

  // --- Excel Export ---
  const handleExport = () => {
    const worksheet = XLSX.utils.json_to_sheet(feedRecords);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Feed Records');
    XLSX.writeFile(workbook, 'FeedRecords.xlsx');
  };

  return (
    <Box sx={{ p: 4, backgroundColor: '#ecf0f1', minHeight: '100vh' }}>
      <Typography variant="h4" component="h1" sx={{ color: '#2c3e50', borderBottom: '2px solid #2c3e50', paddingBottom: '10px', marginBottom: '2rem' }}>
        Feed & Financials
      </Typography>

      <Grid container spacing={4}>
        {/* Feed Consumption Section */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, backgroundColor: 'white', borderRadius: '8px' }}>
            <Typography variant="h5" component="h2" sx={{ color: '#2c3e50', mb: 3 }}>
              Feed Consumption
            </Typography>
            <Box component="form" noValidate autoComplete="off" onSubmit={handleSubmit}>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel id="animal-select-label">Select Animal</InputLabel>
                <Select
                  labelId="animal-select-label"
                  id="animal-select"
                  value={selectedAnimal}
                  label="Select Animal"
                  onChange={handleAnimalChange}
                >
                  {animals.map((animal) => (
                    <MenuItem key={animal.id} value={animal.id}>
                      {animal.name} ({animal.species})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <TextField
                fullWidth
                label="Feed Type"
                placeholder="e.g., Hay, Grain"
                value={feedType}
                onChange={(e) => setFeedType(e.target.value)}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="Quantity (kg)"
                type="number"
                placeholder="e.g., 10"
                value={quantity}
                onChange={(e) => setQuantity(e.target.value)}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="Date"
                type="date"
                value={date}
                onChange={(e) => setDate(e.target.value)}
                InputLabelProps={{ shrink: true }}
                sx={{ mb: 3 }}
              />
              <Button
                type="submit"
                variant="contained"
                sx={{
                  backgroundColor: '#1abc9c',
                  color: '#ecf0f1',
                  '&:hover': { backgroundColor: '#16a085' }
                }}
              >
                Record Feed Data
              </Button>
            </Box>
          </Paper>
        </Grid>

        {/* Financial Analysis Section */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, backgroundColor: 'white', borderRadius: '8px', height: '100%' }}>
            <Typography variant="h5" component="h2" sx={{ color: '#2c3e50', mb: 3 }}>
              Financial Analysis
            </Typography>
            {chartData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={chartData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="totalCost"
                    nameKey="name"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {chartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value: number) => `R${value.toFixed(2)}`} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <Typography sx={{ color: '#7f8c8d', textAlign: 'center', mt: 4 }}>
                No financial data to display. Record feed entries to see analysis.
              </Typography>
            )}
            <Button
              variant="contained"
              onClick={handleExport}
              disabled={feedRecords.length === 0}
              sx={{ mt: 3, backgroundColor: '#34495e', '&:hover': { backgroundColor: '#2c3e50' } }}
            >
              Export to Excel
            </Button>
          </Paper>
        </Grid>
      </Grid>

      {/* Recorded Entries List */}
      <Grid container spacing={4} sx={{ mt: 2 }}>
        <Grid item xs={12}>
          <Paper sx={{ p: 3, backgroundColor: 'white', borderRadius: '8px' }}>
            <Typography variant="h5" component="h2" sx={{ color: '#2c3e50', mb: 3 }}>
              Recorded Entries
            </Typography>
            {feedRecords.length > 0 ? (
              <List>
                {feedRecords.map((record) => (
                  <ListItem key={record.id} divider>
                    <ListItemText
                      primary={`${record.animalName} - ${record.feedType}`}
                      secondary={`Quantity: ${record.quantity}kg on ${record.date}`}
                    />
                  </ListItem>
                ))}
              </List>
            ) : (
              <Typography sx={{ color: '#7f8c8d', textAlign: 'center', mt: 4 }}>
                No feed entries recorded yet.
              </Typography>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default FeedAndFinancials;