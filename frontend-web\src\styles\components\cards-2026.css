/**
 * AgriIntel 2026 - Quantum Card System
 * Next-Generation Card Components with Neural Networks & Glassmorphism
 */

/* ===== QUANTUM CARD BASE ===== */
.agri-card {
  position: relative;
  background: var(--glass-medium);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border-light);
  border-radius: var(--radius-xl);
  padding: var(--space-lg);
  transition: all var(--timing-normal);
  overflow: hidden;
  box-shadow: var(--shadow-neural);
}

.agri-card::before {
  content: '';
  position: absolute;
  inset: 0;
  background: var(--neural-primary);
  opacity: 0;
  transition: opacity var(--timing-normal);
  border-radius: inherit;
}

.agri-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-elevated);
  border-color: var(--glass-border-medium);
}

.agri-card:hover::before {
  opacity: 0.05;
}

/* ===== QUANTUM CARD VARIANTS ===== */

/* Neural Enhanced Card */
.agri-card-neural {
  background: var(--glass-strong);
  border: 2px solid transparent;
  background-clip: padding-box;
  position: relative;
}

.agri-card-neural::after {
  content: '';
  position: absolute;
  inset: 0;
  padding: 2px;
  background: var(--neural-primary);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}

.agri-card-neural:hover {
  transform: translateY(-12px) scale(1.03);
  box-shadow: 
    var(--shadow-floating),
    0 0 40px rgba(0, 245, 255, 0.2);
}

/* Holographic Card */
.agri-card-holographic {
  background: var(--glass-light);
  position: relative;
  overflow: hidden;
}

.agri-card-holographic::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, #ff0080, #ff8c00, #40e0d0, #ff0080);
  background-size: 400% 400%;
  animation: holographic-rotate 3s ease infinite;
  border-radius: inherit;
  z-index: -1;
  filter: blur(1px);
}

.agri-card-holographic:hover::before {
  filter: blur(2px);
  animation-duration: 1.5s;
}

/* Floating Card */
.agri-card-floating {
  background: var(--glass-ultra-light);
  border: none;
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.3),
    0 8px 32px rgba(0, 245, 255, 0.1);
  transform: translateY(-4px);
}

.agri-card-floating:hover {
  transform: translateY(-16px) scale(1.02);
  box-shadow: 
    0 32px 80px rgba(0, 0, 0, 0.4),
    0 16px 48px rgba(0, 245, 255, 0.2);
}

/* ===== QUANTUM CARD SIZES ===== */
.agri-card-sm {
  padding: var(--space-md);
  border-radius: var(--radius-lg);
}

.agri-card-md {
  padding: var(--space-lg);
  border-radius: var(--radius-xl);
}

.agri-card-lg {
  padding: var(--space-xl);
  border-radius: var(--radius-2xl);
}

.agri-card-xl {
  padding: var(--space-2xl);
  border-radius: var(--radius-2xl);
}

/* ===== QUANTUM CARD CONTENT ===== */
.agri-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-md);
  border-bottom: 1px solid var(--glass-border-light);
}

.agri-card-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--quantum-text);
  margin: 0;
  background: var(--neural-primary);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 400% 400%;
  animation: holographic-rotate 3s ease infinite;
}

.agri-card-subtitle {
  font-size: 0.875rem;
  color: var(--quantum-text-secondary);
  margin: var(--space-xs) 0 0 0;
  font-weight: 500;
}

.agri-card-body {
  color: var(--quantum-text);
  line-height: 1.6;
}

.agri-card-footer {
  margin-top: var(--space-lg);
  padding-top: var(--space-md);
  border-top: 1px solid var(--glass-border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* ===== QUANTUM CARD ACTIONS ===== */
.agri-card-actions {
  display: flex;
  gap: var(--space-sm);
  align-items: center;
}

.agri-card-action {
  padding: var(--space-sm) var(--space-md);
  background: var(--glass-light);
  border: 1px solid var(--glass-border-light);
  border-radius: var(--radius-lg);
  color: var(--quantum-text);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all var(--timing-fast);
  cursor: pointer;
}

.agri-card-action:hover {
  background: var(--glass-medium);
  border-color: var(--glass-border-medium);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 245, 255, 0.2);
}

/* ===== QUANTUM CARD GRID ===== */
.agri-card-grid {
  display: grid;
  gap: var(--space-lg);
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.agri-card-grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
}

.agri-card-grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
}

.agri-card-grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

/* ===== QUANTUM CARD STATES ===== */
.agri-card-success {
  border-color: rgba(0, 255, 136, 0.3);
  box-shadow: 0 8px 32px rgba(0, 255, 136, 0.15);
}

.agri-card-success::before {
  background: var(--neural-success);
}

.agri-card-warning {
  border-color: rgba(255, 179, 71, 0.3);
  box-shadow: 0 8px 32px rgba(255, 179, 71, 0.15);
}

.agri-card-warning::before {
  background: var(--neural-warning);
}

.agri-card-danger {
  border-color: rgba(255, 107, 107, 0.3);
  box-shadow: 0 8px 32px rgba(255, 107, 107, 0.15);
}

.agri-card-danger::before {
  background: var(--neural-danger);
}

.agri-card-info {
  border-color: rgba(0, 212, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 212, 255, 0.15);
}

.agri-card-info::before {
  background: var(--neural-info);
}

/* ===== RESPONSIVE QUANTUM CARDS ===== */
@media (max-width: 768px) {
  .agri-card {
    padding: var(--space-md);
    border-radius: var(--radius-lg);
  }
  
  .agri-card-grid {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }
  
  .agri-card-grid-2,
  .agri-card-grid-3,
  .agri-card-grid-4 {
    grid-template-columns: 1fr;
  }
  
  .agri-card-title {
    font-size: 1.25rem;
  }
  
  .agri-card-actions {
    flex-direction: column;
    align-items: stretch;
  }
}

@media (max-width: 480px) {
  .agri-card {
    padding: var(--space-sm);
    border-radius: var(--radius-md);
  }
  
  .agri-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-sm);
  }
  
  .agri-card-footer {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-sm);
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  .agri-card,
  .agri-card-neural,
  .agri-card-holographic,
  .agri-card-floating {
    transform: none !important;
    animation: none !important;
  }
  
  .agri-card:hover,
  .agri-card-neural:hover,
  .agri-card-holographic:hover,
  .agri-card-floating:hover {
    transform: none !important;
  }
}
