import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Alert,
  <PERSON>,
  Stack,
  Divider,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TextField,
  useTheme,
  alpha
} from '@mui/material';
import {
  Route,
  CheckCircle,
  Error,
  Warning,
  Info,
  Navigation,
  ArrowForward
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import {
  routeConfigurations,
  resolveRoute,
  getNavigationItems,
  isValidRoute,
  getBreadcrumbs
} from '../../utils/routingManager';
import { useNavigationGuard } from '../navigation/EnhancedNavigation';

interface TestResult {
  test: string;
  status: 'pass' | 'fail' | 'warning' | 'info';
  message: string;
  timestamp: Date;
}

const RoutingTest: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  const { navigate: guardedNavigate, checkRoute, resolveConflicts } = useNavigationGuard();
  
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [testRoute, setTestRoute] = useState('/dashboard/animals');

  const addTestResult = (test: string, status: TestResult['status'], message: string) => {
    setTestResults(prev => [...prev, {
      test,
      status,
      message,
      timestamp: new Date()
    }]);
  };

  const runRoutingTests = async () => {
    setIsRunningTests(true);
    setTestResults([]);

    // Test 1: Current Route Validation
    const currentValid = isValidRoute(location.pathname, user);
    addTestResult(
      'Current Route Validation',
      currentValid ? 'pass' : 'warning',
      `Current route ${location.pathname} is ${currentValid ? 'valid' : 'invalid'}`
    );

    // Test 2: Route Resolution
    const testRoutes = [
      '/dashboard',
      '/beta-dashboard',
      '/dashboard/animals',
      '/beta-dashboard/animals',
      '/dashboard/breeding',
      '/beta-dashboard/breeding',
      '/dashboard/financial',
      '/dashboard/analytics',
      '/invalid-route'
    ];

    testRoutes.forEach(route => {
      const resolution = resolveRoute(route, user);
      addTestResult(
        `Route Resolution: ${route}`,
        resolution.shouldRedirect ? 'warning' : 'pass',
        resolution.shouldRedirect 
          ? `Redirects to ${resolution.path}: ${resolution.reason}`
          : 'Route is valid'
      );
    });

    // Test 3: Navigation Items
    const navItems = getNavigationItems(user);
    addTestResult(
      'Navigation Items',
      'info',
      `Generated ${navItems.length} navigation items for current user`
    );

    // Test 4: Breadcrumbs
    const breadcrumbs = getBreadcrumbs(location.pathname, user);
    addTestResult(
      'Breadcrumbs',
      'info',
      `Generated ${breadcrumbs.length} breadcrumb items: ${breadcrumbs.map(b => b.name).join(' > ')}`
    );

    // Test 5: Route Configurations
    const configCount = Object.keys(routeConfigurations).length;
    addTestResult(
      'Route Configurations',
      'pass',
      `${configCount} route configurations loaded`
    );

    // Test 6: User-Specific Route Access
    let accessibleRoutes = 0;
    let restrictedRoutes = 0;

    Object.values(routeConfigurations).forEach(config => {
      if (config.requiresAuth && user) {
        const hasAccess = config.allowedRoles.includes('*') || 
                         config.allowedRoles.includes(user.role) ||
                         user.role === 'admin';
        if (hasAccess) {
          accessibleRoutes++;
        } else {
          restrictedRoutes++;
        }
      }
    });

    addTestResult(
      'User Route Access',
      'info',
      `User can access ${accessibleRoutes} routes, ${restrictedRoutes} are restricted`
    );

    setIsRunningTests(false);
  };

  const testSpecificRoute = () => {
    const resolution = resolveConflicts(testRoute);
    addTestResult(
      `Manual Route Test: ${testRoute}`,
      resolution.shouldRedirect ? 'warning' : 'pass',
      resolution.shouldRedirect 
        ? `Would redirect to ${resolution.path}: ${resolution.reason}`
        : 'Route is valid for current user'
    );
  };

  const navigateToRoute = (route: string) => {
    guardedNavigate(route);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return <CheckCircle sx={{ color: theme.palette.success.main }} />;
      case 'fail':
        return <Error sx={{ color: theme.palette.error.main }} />;
      case 'warning':
        return <Warning sx={{ color: theme.palette.warning.main }} />;
      case 'info':
        return <Info sx={{ color: theme.palette.info.main }} />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return theme.palette.success.main;
      case 'fail':
        return theme.palette.error.main;
      case 'warning':
        return theme.palette.warning.main;
      case 'info':
        return theme.palette.info.main;
    }
  };

  const navigationItems = getNavigationItems(user);
  const currentBreadcrumbs = getBreadcrumbs(location.pathname, user);

  return (
    <Box sx={{ p: 3 }}>
      <Card
        sx={{
          background: alpha(theme.palette.background.paper, 0.9),
          backdropFilter: 'blur(20px)',
          border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
        }}
      >
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
            <Route sx={{ fontSize: 40, color: theme.palette.primary.main }} />
            <Box>
              <Typography variant="h4" fontWeight="bold">
                Routing & Navigation Testing
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                Test route resolution, navigation guards, and routing conflicts
              </Typography>
            </Box>
          </Box>

          {/* Current Status */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Current Route Status
                  </Typography>
                  <Typography><strong>Path:</strong> {location.pathname}</Typography>
                  <Typography><strong>Valid:</strong> 
                    <Chip 
                      label={isValidRoute(location.pathname, user) ? 'Yes' : 'No'}
                      color={isValidRoute(location.pathname, user) ? 'success' : 'error'}
                      size="small"
                      sx={{ ml: 1 }}
                    />
                  </Typography>
                  <Typography><strong>Breadcrumbs:</strong> {currentBreadcrumbs.map(b => b.name).join(' > ')}</Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Navigation Summary
                  </Typography>
                  <Typography><strong>Available Items:</strong> {navigationItems.length}</Typography>
                  <Typography><strong>Route Configs:</strong> {Object.keys(routeConfigurations).length}</Typography>
                  <Typography><strong>User Type:</strong> {user?.role || 'Not logged in'}</Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Route Testing */}
          <Typography variant="h6" gutterBottom>
            Route Testing
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, mb: 3, alignItems: 'center' }}>
            <TextField
              label="Test Route"
              value={testRoute}
              onChange={(e) => setTestRoute(e.target.value)}
              size="small"
              sx={{ minWidth: 200 }}
            />
            <Button
              variant="outlined"
              onClick={testSpecificRoute}
              startIcon={<Navigation />}
            >
              Test Route
            </Button>
            <Button
              variant="contained"
              onClick={() => navigateToRoute(testRoute)}
              startIcon={<ArrowForward />}
              sx={{
                background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`
              }}
            >
              Navigate
            </Button>
          </Box>

          {/* Navigation Items Table */}
          <Typography variant="h6" gutterBottom>
            Available Navigation Items
          </Typography>
          <TableContainer component={Paper} sx={{ mb: 3 }}>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Path</TableCell>
                  <TableCell>Available</TableCell>
                  <TableCell>Action</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {navigationItems.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>{item.name}</TableCell>
                    <TableCell>{item.path}</TableCell>
                    <TableCell>
                      <Chip
                        label={item.available ? 'Yes' : 'No'}
                        color={item.available ? 'success' : 'error'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Button
                        size="small"
                        onClick={() => navigateToRoute(item.path)}
                        disabled={!item.available}
                      >
                        Navigate
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          <Button
            variant="contained"
            startIcon={<Route />}
            onClick={runRoutingTests}
            disabled={isRunningTests}
            sx={{
              background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
              '&:hover': {
                background: `linear-gradient(135deg, ${theme.palette.primary.dark}, ${theme.palette.primary.main})`
              }
            }}
          >
            {isRunningTests ? 'Running Tests...' : 'Run Routing Tests'}
          </Button>

          {testResults.length > 0 && (
            <>
              <Divider sx={{ my: 3 }} />
              <Typography variant="h6" gutterBottom>
                Test Results
              </Typography>
              
              <Stack spacing={2}>
                {testResults.map((result, index) => (
                  <Alert
                    key={index}
                    severity={result.status === 'pass' ? 'success' : 
                             result.status === 'fail' ? 'error' :
                             result.status === 'warning' ? 'warning' : 'info'}
                    icon={getStatusIcon(result.status)}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="subtitle2" fontWeight="bold">
                          {result.test}
                        </Typography>
                        <Typography variant="body2">
                          {result.message}
                        </Typography>
                      </Box>
                      <Chip
                        label={result.status.toUpperCase()}
                        size="small"
                        sx={{
                          backgroundColor: getStatusColor(result.status),
                          color: 'white',
                          fontWeight: 'bold'
                        }}
                      />
                    </Box>
                  </Alert>
                ))}
              </Stack>
            </>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default RoutingTest;
