import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  Alert,
  Chip,
  LinearProgress,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Assessment,
  AutoAwesome,
  Upgrade,
  CheckCircle,
  Warning,
  Info,
  Lightbulb,
  Analytics,
  PieChart,
  BarChart,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON>hart as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'recharts';

interface KPIMetric {
  id: string;
  name: string;
  value: number;
  target: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  status: 'good' | 'warning' | 'critical';
  description: string;
}

interface AIRecommendation {
  id: string;
  type: 'optimization' | 'alert' | 'opportunity';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  category: string;
}

const BetaKPIDashboard: React.FC = () => {
  const [kpiMetrics, setKpiMetrics] = useState<KPIMetric[]>([]);
  const [aiRecommendations, setAiRecommendations] = useState<AIRecommendation[]>([]);

  // Sample KPI data
  useEffect(() => {
    const sampleKPIs: KPIMetric[] = [
      {
        id: '1',
        name: 'Milk Production',
        value: 850,
        target: 1000,
        unit: 'L/day',
        trend: 'up',
        status: 'warning',
        description: 'Daily milk production from dairy cattle'
      },
      {
        id: '2',
        name: 'Feed Efficiency',
        value: 1.2,
        target: 1.5,
        unit: 'kg milk/kg feed',
        trend: 'stable',
        status: 'warning',
        description: 'Milk production per kg of feed consumed'
      },
      {
        id: '3',
        name: 'Animal Health Score',
        value: 92,
        target: 95,
        unit: '%',
        trend: 'up',
        status: 'good',
        description: 'Overall health status of livestock'
      },
      {
        id: '4',
        name: 'Cost per Liter',
        value: 4.50,
        target: 4.00,
        unit: 'R/L',
        trend: 'down',
        status: 'critical',
        description: 'Production cost per liter of milk'
      },
      {
        id: '5',
        name: 'Breeding Success Rate',
        value: 78,
        target: 85,
        unit: '%',
        trend: 'up',
        status: 'warning',
        description: 'Successful pregnancies per breeding attempt'
      },
      {
        id: '6',
        name: 'Revenue per Animal',
        value: 2800,
        target: 3200,
        unit: 'R/month',
        trend: 'stable',
        status: 'warning',
        description: 'Monthly revenue generated per animal'
      }
    ];

    const sampleRecommendations: AIRecommendation[] = [
      {
        id: '1',
        type: 'optimization',
        title: 'Optimize Feed Mix for Higher Milk Production',
        description: 'AI analysis suggests increasing protein content by 12% could boost milk production by 15%',
        impact: 'high',
        category: 'Feed Management'
      },
      {
        id: '2',
        type: 'alert',
        title: 'Cost Control Alert',
        description: 'Feed costs have increased 8% this month. Consider alternative suppliers or feed types.',
        impact: 'medium',
        category: 'Financial'
      },
      {
        id: '3',
        type: 'opportunity',
        title: 'Breeding Program Enhancement',
        description: 'Genetic analysis shows potential for 20% improvement in milk yield with selective breeding.',
        impact: 'high',
        category: 'Breeding'
      },
      {
        id: '4',
        type: 'optimization',
        title: 'Health Monitoring Optimization',
        description: 'Implement weekly health checks to prevent 85% of common diseases.',
        impact: 'medium',
        category: 'Health'
      }
    ];

    setKpiMetrics(sampleKPIs);
    setAiRecommendations(sampleRecommendations);
  }, []);

  // Sample trend data
  const trendData = [
    { month: 'Jan', production: 800, cost: 4.8, efficiency: 1.1 },
    { month: 'Feb', production: 820, cost: 4.6, efficiency: 1.15 },
    { month: 'Mar', production: 840, cost: 4.5, efficiency: 1.18 },
    { month: 'Apr', production: 850, cost: 4.5, efficiency: 1.2 },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return '#4CAF50';
      case 'warning': return '#FF9800';
      case 'critical': return '#F44336';
      default: return '#9E9E9E';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp sx={{ color: '#4CAF50' }} />;
      case 'down': return <TrendingDown sx={{ color: '#F44336' }} />;
      default: return <TrendingUp sx={{ color: '#9E9E9E' }} />;
    }
  };

  const getRecommendationIcon = (type: string) => {
    switch (type) {
      case 'optimization': return <Lightbulb sx={{ color: '#FF9800' }} />;
      case 'alert': return <Warning sx={{ color: '#F44336' }} />;
      case 'opportunity': return <CheckCircle sx={{ color: '#4CAF50' }} />;
      default: return <Info sx={{ color: '#2196F3' }} />;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* BETA Limitation Alert */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>BETA KPI Dashboard:</strong> Basic KPI tracking with AI-generated recommendations preview. 
          Upgrade to Professional for real-time AI analytics, custom KPIs, and automated optimization suggestions.
        </Typography>
      </Alert>

      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" fontWeight="bold">
          Business KPI Dashboard (BETA)
        </Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<Analytics />}
            sx={{ borderRadius: 2 }}
          >
            Generate Report
          </Button>
          <Button
            variant="contained"
            startIcon={<Upgrade />}
            sx={{
              background: 'linear-gradient(135deg, #FF9800, #FFB74D)',
              borderRadius: 2,
            }}
          >
            Upgrade for Full AI
          </Button>
        </Box>
      </Box>

      {/* KPI Metrics Grid */}
      <Grid container spacing={3} mb={4}>
        {kpiMetrics.map((kpi, index) => (
          <Grid item xs={12} sm={6} md={4} key={kpi.id}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              whileHover={{ y: -4 }}
            >
              <Card sx={{ borderRadius: 3, height: '100%' }}>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                    <Box>
                      <Typography variant="h6" fontWeight="bold" mb={1}>
                        {kpi.name}
                      </Typography>
                      <Typography variant="h4" fontWeight="bold" color={getStatusColor(kpi.status)}>
                        {kpi.value}{kpi.unit}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Target: {kpi.target}{kpi.unit}
                      </Typography>
                    </Box>
                    <Box display="flex" flexDirection="column" alignItems="center" gap={1}>
                      {getTrendIcon(kpi.trend)}
                      <Chip
                        label={kpi.status}
                        size="small"
                        sx={{
                          backgroundColor: getStatusColor(kpi.status),
                          color: 'white',
                          textTransform: 'capitalize'
                        }}
                      />
                    </Box>
                  </Box>
                  
                  <LinearProgress
                    variant="determinate"
                    value={(kpi.value / kpi.target) * 100}
                    sx={{
                      height: 8,
                      borderRadius: 4,
                      backgroundColor: '#f0f0f0',
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: getStatusColor(kpi.status),
                        borderRadius: 4,
                      }
                    }}
                  />
                  
                  <Typography variant="caption" color="text.secondary" mt={1} display="block">
                    {kpi.description}
                  </Typography>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* Charts and AI Recommendations */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={8}>
          <Card sx={{ borderRadius: 3, height: '400px' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" mb={2}>
                Performance Trends
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={trendData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="production" stroke="#4CAF50" strokeWidth={2} name="Production (L)" />
                  <Line type="monotone" dataKey="cost" stroke="#F44336" strokeWidth={2} name="Cost (R/L)" />
                  <Line type="monotone" dataKey="efficiency" stroke="#2196F3" strokeWidth={2} name="Efficiency" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ borderRadius: 3, height: '400px' }}>
            <CardContent>
              <Box display="flex" alignItems="center" gap={1} mb={2}>
                <AutoAwesome sx={{ color: '#FF9800' }} />
                <Typography variant="h6" fontWeight="bold">
                  AI Recommendations
                </Typography>
              </Box>
              
              <List sx={{ maxHeight: 300, overflow: 'auto' }}>
                {aiRecommendations.map((rec, index) => (
                  <React.Fragment key={rec.id}>
                    <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                      <ListItemIcon sx={{ minWidth: 40 }}>
                        {getRecommendationIcon(rec.type)}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box display="flex" alignItems="center" gap={1} mb={1}>
                            <Typography variant="body2" fontWeight="bold">
                              {rec.title}
                            </Typography>
                            <Chip
                              label={rec.impact}
                              size="small"
                              color={rec.impact === 'high' ? 'error' : rec.impact === 'medium' ? 'warning' : 'info'}
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="caption" color="text.secondary">
                              {rec.description}
                            </Typography>
                            <Chip
                              label={rec.category}
                              size="small"
                              variant="outlined"
                              sx={{ mt: 1, fontSize: '0.7rem' }}
                            />
                          </Box>
                        }
                      />
                    </ListItem>
                    {index < aiRecommendations.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Upgrade Promotion */}
      <Card sx={{ borderRadius: 3, background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
        <CardContent>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={8}>
              <Box display="flex" alignItems="center" gap={2} mb={2}>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                  <AutoAwesome sx={{ fontSize: 32, color: 'white' }} />
                </Avatar>
                <Box>
                  <Typography variant="h5" fontWeight="bold" color="white">
                    Unlock Full AI-Powered Analytics
                  </Typography>
                  <Typography variant="body1" color="rgba(255,255,255,0.9)">
                    Get real-time AI insights, predictive analytics, and automated optimization recommendations
                  </Typography>
                </Box>
              </Box>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Box display="flex" alignItems="center" gap={1} mb={1}>
                    <CheckCircle sx={{ color: 'white', fontSize: 20 }} />
                    <Typography variant="body2" color="white">
                      Real-time AI monitoring
                    </Typography>
                  </Box>
                  <Box display="flex" alignItems="center" gap={1} mb={1}>
                    <CheckCircle sx={{ color: 'white', fontSize: 20 }} />
                    <Typography variant="body2" color="white">
                      Predictive health analytics
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Box display="flex" alignItems="center" gap={1} mb={1}>
                    <CheckCircle sx={{ color: 'white', fontSize: 20 }} />
                    <Typography variant="body2" color="white">
                      Automated optimization alerts
                    </Typography>
                  </Box>
                  <Box display="flex" alignItems="center" gap={1} mb={1}>
                    <CheckCircle sx={{ color: 'white', fontSize: 20 }} />
                    <Typography variant="body2" color="white">
                      Custom KPI dashboards
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Grid>
            
            <Grid item xs={12} md={4} textAlign="center">
              <Typography variant="h3" fontWeight="bold" color="white" mb={1}>
                R299
              </Typography>
              <Typography variant="body1" color="rgba(255,255,255,0.9)" mb={3}>
                per month
              </Typography>
              <Button
                variant="contained"
                size="large"
                sx={{
                  bgcolor: 'white',
                  color: '#667eea',
                  fontWeight: 'bold',
                  px: 4,
                  py: 1.5,
                  borderRadius: 3,
                  '&:hover': {
                    bgcolor: 'rgba(255,255,255,0.9)',
                  }
                }}
              >
                Upgrade to Professional
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
};

export default BetaKPIDashboard;
