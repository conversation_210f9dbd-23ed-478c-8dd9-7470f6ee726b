/* Agricultural Sections Styles */

/* Livestock Services Section */
.livestock-services-section {
  background: linear-gradient(135deg, rgba(46, 125, 50, 0.05) 0%, rgba(76, 175, 80, 0.05) 100%);
  position: relative;
  overflow: hidden;
}

.service-card {
  height: 100%;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.service-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.service-image-container {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.service-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.service-card:hover .service-image {
  transform: scale(1.05);
}

.service-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.7));
  display: flex;
  align-items: flex-end;
  padding: 24px;
}

.service-title {
  color: white;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.service-content {
  padding: 24px;
}

.service-subtitle {
  color: #2E7D32;
  font-weight: 600;
  margin-bottom: 12px;
}

.service-description {
  color: #666;
  margin-bottom: 20px;
  line-height: 1.6;
}

.service-features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.feature-item {
  font-size: 14px;
  color: #4CAF50;
  font-weight: 500;
}

/* Agricultural Products Section */
.agricultural-products-section {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(217, 119, 6, 0.05) 100%);
}

.product-card {
  height: 100%;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.product-image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image {
  transform: scale(1.05);
}

.product-price-tag {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(245, 158, 11, 0.9);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.product-title {
  color: #D97706;
  font-weight: 600;
  margin-bottom: 8px;
}

.product-description {
  color: #666;
  margin-bottom: 16px;
  line-height: 1.6;
}

.product-specs {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.product-specs span {
  font-size: 12px;
  background: rgba(245, 158, 11, 0.1);
  color: #D97706;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

/* Innovation/Technology Section */
.innovation-technology-section {
  background: linear-gradient(135deg, rgba(21, 101, 192, 0.05) 0%, rgba(25, 118, 210, 0.05) 100%);
}

.tech-card {
  height: 100%;
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.tech-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.tech-icon-container {
  margin-bottom: 20px;
}

.tech-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.tech-title {
  color: #1565C0;
  font-weight: 600;
  margin-bottom: 12px;
}

.tech-description {
  color: #666;
  margin-bottom: 16px;
  line-height: 1.6;
}

.tech-features {
  display: flex;
  flex-direction: column;
  gap: 4px;
  text-align: left;
}

.tech-features span {
  font-size: 14px;
  color: #1976D2;
  font-weight: 500;
}

.tech-showcase {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 24px;
  padding: 48px;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.showcase-title {
  color: #1565C0;
  font-weight: 700;
  margin-bottom: 16px;
}

.showcase-description {
  color: #666;
  line-height: 1.6;
  margin-bottom: 32px;
}

.tech-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin-bottom: 24px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  color: #1565C0;
  font-weight: 700;
  margin-bottom: 8px;
}

.stat-label {
  color: #666;
  font-weight: 500;
}

.showcase-image {
  width: 100%;
  border-radius: 16px;
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

/* Gallery Section */
.gallery-section {
  background: linear-gradient(135deg, rgba(139, 69, 19, 0.05) 0%, rgba(160, 82, 45, 0.05) 100%);
}

.gallery-item {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 300px;
}

.gallery-item.large {
  height: 400px;
}

.gallery-item:hover {
  transform: scale(1.02);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
}

.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-image {
  transform: scale(1.05);
}

.gallery-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  padding: 24px;
  color: white;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
  transform: translateY(0);
}

.gallery-title {
  font-weight: 600;
  margin-bottom: 8px;
}

.gallery-description {
  opacity: 0.9;
}

/* Contact Section */
.contact-section {
  background: linear-gradient(135deg, rgba(46, 125, 50, 0.05) 0%, rgba(76, 175, 80, 0.05) 100%);
}

.contact-form-card {
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.form-title {
  color: #2E7D32;
  font-weight: 600;
  margin-bottom: 24px;
}

.contact-input {
  margin-bottom: 16px;
}

.contact-submit-btn {
  background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
  color: white;
  font-weight: 600;
  padding: 16px;
  border-radius: 12px;
  text-transform: none;
  font-size: 16px;
}

.contact-submit-btn:hover {
  background: linear-gradient(135deg, #1B5E20 0%, #388E3C 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(46, 125, 50, 0.3);
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.contact-icon {
  font-size: 24px;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #2E7D32, #4CAF50);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.contact-title {
  color: #2E7D32;
  font-weight: 600;
  margin-bottom: 8px;
}

.contact-value {
  color: #666;
  line-height: 1.6;
}

.whatsapp-btn {
  margin-top: 8px;
  border-color: #25D366;
  color: #25D366;
  font-size: 12px;
  padding: 4px 12px;
}

.whatsapp-btn:hover {
  background-color: rgba(37, 211, 102, 0.1);
  border-color: #25D366;
}

/* Hero Title Gradient */
.hero-title-gradient {
  background: var(--agri-pro-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Responsive Design */
@media (max-width: 768px) {
  .service-features {
    grid-template-columns: 1fr;
  }
  
  .tech-stats {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .tech-showcase {
    padding: 24px;
  }
  
  .gallery-item,
  .gallery-item.large {
    height: 250px;
  }
  
  .contact-item {
    flex-direction: column;
    text-align: center;
  }
  
  .contact-icon {
    align-self: center;
  }
}

@media (max-width: 480px) {
  .service-overlay {
    padding: 16px;
  }
  
  .service-title {
    font-size: 1.5rem;
  }
  
  .tech-showcase {
    padding: 16px;
  }
  
  .showcase-title {
    font-size: 1.5rem;
  }
}
