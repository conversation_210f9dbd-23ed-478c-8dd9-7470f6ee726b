/**
 * Unified Access Control System for AgriIntel
 * Handles BETA V1 and Professional V1 tier-based access controls
 */

import { User } from '../types/user';

export type AccessLevel = 'beta' | 'professional' | 'admin';
export type ModuleCategory = 'core' | 'advanced' | 'premium' | 'marketplace';

export interface ModuleConfig {
  id: string;
  name: string;
  category: ModuleCategory;
  requiredLevel: AccessLevel;
  betaAccess: boolean;
  professionalAccess: boolean;
  description: string;
  features: string[];
  limitations?: {
    beta?: string[];
    professional?: string[];
  };
  upgradeMessage?: string;
}

// Unified module configurations for dual-tier system
export const moduleConfigurations: Record<string, ModuleConfig> = {
  dashboard: {
    id: 'dashboard',
    name: 'Dashboard',
    category: 'core',
    requiredLevel: 'beta',
    betaAccess: true,
    professionalAccess: true,
    description: 'Main dashboard with farm overview and key metrics',
    features: ['overview', 'quick_stats', 'recent_activities'],
    limitations: {
      beta: ['Basic metrics only', 'Limited historical data'],
      professional: ['Advanced analytics', 'Unlimited historical data']
    }
  },
  
  animals: {
    id: 'animals',
    name: 'Animal Management',
    category: 'core',
    requiredLevel: 'beta',
    betaAccess: true,
    professionalAccess: true,
    description: 'Livestock tracking and management system',
    features: ['animal_profiles', 'basic_tracking', 'health_records'],
    limitations: {
      beta: ['Maximum 50 animals', 'Basic search only', 'No bulk operations'],
      professional: ['Unlimited animals', 'Advanced search', 'Bulk operations', 'RFID integration']
    }
  },
  
  health: {
    id: 'health',
    name: 'Health Monitoring',
    category: 'core',
    requiredLevel: 'beta',
    betaAccess: true,
    professionalAccess: true,
    description: 'Animal health tracking and veterinary management',
    features: ['health_records', 'vaccination_tracking', 'basic_alerts'],
    limitations: {
      beta: ['Basic health records', 'Manual entry only', 'Email alerts'],
      professional: ['AI health predictions', 'Automated monitoring', 'Veterinary network access', 'Real-time alerts']
    }
  },
  
  feeding: {
    id: 'feeding',
    name: 'Feed Management',
    category: 'core',
    requiredLevel: 'beta',
    betaAccess: true,
    professionalAccess: true,
    description: 'Feed planning and nutrition management',
    features: ['feed_schedules', 'basic_nutrition', 'cost_tracking'],
    limitations: {
      beta: ['Basic feed schedules', 'Manual calculations', 'Simple cost tracking'],
      professional: ['AI-optimized nutrition', 'Automated feed calculations', 'Supplier connections', 'Cost optimization']
    }
  },
  
  financial: {
    id: 'financial',
    name: 'Financial Management',
    category: 'core',
    requiredLevel: 'beta',
    betaAccess: true,
    professionalAccess: true,
    description: 'Farm financial tracking and analysis',
    features: ['basic_accounting', 'expense_tracking', 'simple_reports'],
    limitations: {
      beta: ['Basic income/expense tracking', 'Excel-only reports', 'Manual calculations'],
      professional: ['Advanced financial analytics', 'Automated profit/loss', 'Tax-ready reports', 'Budget forecasting']
    }
  },
  
  reports: {
    id: 'reports',
    name: 'Reports & Analytics',
    category: 'premium',
    requiredLevel: 'professional',
    betaAccess: false,
    professionalAccess: true,
    description: 'Advanced farm reporting and data analysis',
    features: ['custom_reports', 'advanced_analytics', 'multiple_export_formats'],
    upgradeMessage: 'Upgrade to Professional V1 (R699/month) to access advanced reports and analytics'
  },
  
  resources: {
    id: 'resources',
    name: 'Resources & Support',
    category: 'core',
    requiredLevel: 'beta',
    betaAccess: true,
    professionalAccess: true,
    description: 'Educational resources and government programs',
    features: ['resource_library', 'government_programs', 'basic_support'],
    limitations: {
      beta: ['Basic resource library', 'Email support only', 'Community forums'],
      professional: ['Premium resources', 'Priority support', 'Expert consultations', 'Training programs']
    }
  },
  
  settings: {
    id: 'settings',
    name: 'Settings & Profile',
    category: 'premium',
    requiredLevel: 'professional',
    betaAccess: false,
    professionalAccess: true,
    description: 'Advanced user settings and profile management',
    features: ['advanced_settings', 'full_customization', 'multi_user_management'],
    upgradeMessage: 'Upgrade to Professional V1 (R699/month) to access advanced settings and profile management'
  },
  
  // PROFESSIONAL-ONLY MODULES
  breeding: {
    id: 'breeding',
    name: 'Breeding Management',
    category: 'premium',
    requiredLevel: 'professional',
    betaAccess: false,
    professionalAccess: true,
    description: 'Advanced breeding program management with AI insights',
    features: ['breeding_cycles', 'genetic_tracking', 'ai_predictions'],
    upgradeMessage: 'Upgrade to Professional V1 (R699/month) to access advanced breeding management with AI insights'
  },
  
  inventory: {
    id: 'inventory',
    name: 'Inventory Management',
    category: 'premium',
    requiredLevel: 'professional',
    betaAccess: false,
    professionalAccess: true,
    description: 'Complete farm inventory and asset management',
    features: ['asset_tracking', 'inventory_management', 'automated_ordering'],
    upgradeMessage: 'Upgrade to Professional V1 (R699/month) to access comprehensive inventory management'
  },
  
  commercial: {
    id: 'commercial',
    name: 'Commercial & Marketplace',
    category: 'marketplace',
    requiredLevel: 'professional',
    betaAccess: false,
    professionalAccess: true,
    description: 'Marketplace connections and commercial operations',
    features: ['marketplace_access', 'supplier_connections', 'auction_integration'],
    upgradeMessage: 'Upgrade to Professional V1 (R699/month) to access marketplace connections and increase revenue by 25%'
  },
  
  compliance: {
    id: 'compliance',
    name: 'Compliance & Regulations',
    category: 'premium',
    requiredLevel: 'professional',
    betaAccess: false,
    professionalAccess: true,
    description: 'Regulatory compliance and certification management',
    features: ['compliance_tracking', 'certification_management', 'audit_trails'],
    upgradeMessage: 'Upgrade to Professional V1 (R699/month) to access compliance management and certification tracking'
  },
  
  analytics: {
    id: 'analytics',
    name: 'Advanced Analytics',
    category: 'premium',
    requiredLevel: 'professional',
    betaAccess: false,
    professionalAccess: true,
    description: 'AI-powered analytics and business intelligence',
    features: ['ai_analytics', 'predictive_insights', 'business_intelligence'],
    upgradeMessage: 'Upgrade to Professional V1 (R699/month) to access AI-powered analytics and predictive insights'
  }
};

/**
 * Get user access level based on role and subscription
 */
export const getUserAccessLevel = (user: User | null): AccessLevel => {
  if (!user) return 'beta';
  
  // Admin users have highest access
  if (user.role === 'admin') return 'admin';
  
  // Check by role first
  if (user.role === 'professional' || user.role === 'manager' || user.role === 'super_user') {
    return 'professional';
  }
  
  // Check by subscription tier
  if (user.subscriptionTier === 'Professional') {
    return 'professional';
  }
  
  // Check by username for demo accounts
  if (user.username === 'Pro' || user.username === 'admin') {
    return 'professional';
  }
  
  // Default to beta for Demo users and others
  return 'beta';
};

/**
 * Check if user can access a module
 */
export const canAccessModule = (user: User | null, moduleId: string): boolean => {
  if (!user) return false;
  
  const module = moduleConfigurations[moduleId];
  if (!module) return false;
  
  const userLevel = getUserAccessLevel(user);
  
  // Admin users can access everything
  if (userLevel === 'admin') return true;
  
  // Professional users can access everything
  if (userLevel === 'professional') return true;
  
  // Beta users can only access modules with betaAccess: true
  if (userLevel === 'beta') {
    return module.betaAccess;
  }
  
  return false;
};

/**
 * Check if user is BETA user
 */
export const isBetaUser = (user: User | null): boolean => {
  if (!user) return true;
  return getUserAccessLevel(user) === 'beta';
};

/**
 * Check if user is Professional user
 */
export const isProfessionalUser = (user: User | null): boolean => {
  if (!user) return false;
  const level = getUserAccessLevel(user);
  return level === 'professional' || level === 'admin';
};

/**
 * Get modules accessible to user
 */
export const getAccessibleModules = (user: User | null): ModuleConfig[] => {
  if (!user) return [];
  
  return Object.values(moduleConfigurations).filter(module => 
    canAccessModule(user, module.id)
  );
};

/**
 * Get locked modules for user (for upgrade prompts)
 */
export const getLockedModules = (user: User | null): ModuleConfig[] => {
  if (!user) return Object.values(moduleConfigurations);
  
  return Object.values(moduleConfigurations).filter(module => 
    !canAccessModule(user, module.id)
  );
};

/**
 * Get module limitations for user
 */
export const getModuleLimitations = (user: User | null, moduleId: string): string[] => {
  if (!user) return ['Login required'];
  
  const module = moduleConfigurations[moduleId];
  if (!module) return ['Module not found'];
  
  if (!canAccessModule(user, moduleId)) {
    return [module.upgradeMessage || 'Upgrade required'];
  }
  
  const userLevel = getUserAccessLevel(user);
  return module.limitations?.[userLevel] || [];
};

/**
 * Get upgrade message for locked module
 */
export const getUpgradeMessage = (moduleId: string): string => {
  const module = moduleConfigurations[moduleId];
  if (!module) return 'Module not found';
  
  return module.upgradeMessage || `Upgrade to Professional V1 (R699/month) to access ${module.name}`;
};

/**
 * Get all BETA accessible modules
 */
export const getBetaModules = (): string[] => {
  return Object.values(moduleConfigurations)
    .filter(module => module.betaAccess)
    .map(module => module.id);
};

/**
 * Get all Professional-only modules
 */
export const getProfessionalOnlyModules = (): string[] => {
  return Object.values(moduleConfigurations)
    .filter(module => !module.betaAccess && module.professionalAccess)
    .map(module => module.id);
};
