import React from 'react';
import UpgradePrompt from './UpgradePrompt';

const InventoryUpgrade: React.FC = () => {
  const features = [
    'Advanced inventory management',
    'Feed and supply tracking',
    'Automated reorder alerts',
    'Supplier management',
    'Cost optimization analysis',
    'Inventory valuation reports',
    'Equipment maintenance tracking',
    'Barcode scanning support'
  ];

  return (
    <UpgradePrompt
      moduleName="Inventory Management"
      features={features}
      tier="Professional"
      price="R299/month"
    />
  );
};

export default InventoryUpgrade;
