/**
 * Enhanced Performance Optimization System for AgriIntel
 * Comprehensive performance monitoring, optimization, and error recovery
 */

import { useState, useEffect, useCallback, useRef } from 'react';

export interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  memoryUsage: number;
  chunkLoadErrors: number;
  networkLatency: number;
  errorRate: number;
  userInteractions: number;
  timestamp: number;
}

export interface OptimizationConfig {
  enableLazyLoading: boolean;
  enableImageOptimization: boolean;
  enableCodeSplitting: boolean;
  enableCaching: boolean;
  enablePreloading: boolean;
  maxRetries: number;
  retryDelay: number;
  chunkTimeout: number;
}

class EnhancedPerformanceManager {
  private static instance: EnhancedPerformanceManager;
  private metrics: Map<string, PerformanceMetrics[]> = new Map();
  private config: OptimizationConfig;
  private observers: PerformanceObserver[] = [];
  private errorCount: number = 0;
  private startTime: number = performance.now();

  private constructor() {
    this.config = {
      enableLazyLoading: true,
      enableImageOptimization: true,
      enableCodeSplitting: true,
      enableCaching: true,
      enablePreloading: true,
      maxRetries: 3,
      retryDelay: 1000,
      chunkTimeout: 30000
    };
    
    this.initializeMonitoring();
  }

  static getInstance(): EnhancedPerformanceManager {
    if (!EnhancedPerformanceManager.instance) {
      EnhancedPerformanceManager.instance = new EnhancedPerformanceManager();
    }
    return EnhancedPerformanceManager.instance;
  }

  private initializeMonitoring() {
    // Monitor navigation timing
    if ('PerformanceObserver' in window) {
      try {
        const navObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            if (entry.entryType === 'navigation') {
              this.recordNavigationMetrics(entry as PerformanceNavigationTiming);
            }
          });
        });
        navObserver.observe({ entryTypes: ['navigation'] });
        this.observers.push(navObserver);

        // Monitor resource loading
        const resourceObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            if (entry.entryType === 'resource') {
              this.recordResourceMetrics(entry as PerformanceResourceTiming);
            }
          });
        });
        resourceObserver.observe({ entryTypes: ['resource'] });
        this.observers.push(resourceObserver);

        // Monitor long tasks
        const longTaskObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            this.recordLongTask(entry);
          });
        });
        longTaskObserver.observe({ entryTypes: ['longtask'] });
        this.observers.push(longTaskObserver);
      } catch (error) {
        console.warn('Performance monitoring not fully supported:', error);
      }
    }

    // Monitor memory usage
    this.startMemoryMonitoring();
    
    // Monitor errors
    this.setupErrorTracking();
  }

  private recordNavigationMetrics(entry: PerformanceNavigationTiming) {
    const metrics: PerformanceMetrics = {
      loadTime: entry.loadEventEnd - entry.navigationStart,
      renderTime: entry.domContentLoadedEventEnd - entry.navigationStart,
      memoryUsage: this.getMemoryUsage(),
      chunkLoadErrors: this.errorCount,
      networkLatency: entry.responseStart - entry.requestStart,
      errorRate: this.calculateErrorRate(),
      userInteractions: 0,
      timestamp: Date.now()
    };

    this.addMetric('navigation', metrics);
  }

  private recordResourceMetrics(entry: PerformanceResourceTiming) {
    if (entry.name.includes('.js') || entry.name.includes('.css')) {
      const metrics: PerformanceMetrics = {
        loadTime: entry.responseEnd - entry.startTime,
        renderTime: 0,
        memoryUsage: this.getMemoryUsage(),
        chunkLoadErrors: this.errorCount,
        networkLatency: entry.responseStart - entry.requestStart,
        errorRate: this.calculateErrorRate(),
        userInteractions: 0,
        timestamp: Date.now()
      };

      this.addMetric('resource', metrics);
    }
  }

  private recordLongTask(entry: PerformanceEntry) {
    console.warn(`Long task detected: ${entry.duration}ms`, entry);
    
    const metrics: PerformanceMetrics = {
      loadTime: entry.duration,
      renderTime: entry.duration,
      memoryUsage: this.getMemoryUsage(),
      chunkLoadErrors: this.errorCount,
      networkLatency: 0,
      errorRate: this.calculateErrorRate(),
      userInteractions: 0,
      timestamp: Date.now()
    };

    this.addMetric('longtask', metrics);
  }

  private startMemoryMonitoring() {
    if ('memory' in performance) {
      setInterval(() => {
        const memory = (performance as any).memory;
        if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
          console.warn('High memory usage detected:', memory);
          this.triggerMemoryCleanup();
        }
      }, 30000); // Check every 30 seconds
    }
  }

  private setupErrorTracking() {
    window.addEventListener('error', (event) => {
      this.errorCount++;
      this.recordError('javascript', event.error);
    });

    window.addEventListener('unhandledrejection', (event) => {
      this.errorCount++;
      this.recordError('promise', event.reason);
    });
  }

  private getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }

  private calculateErrorRate(): number {
    const totalTime = performance.now() - this.startTime;
    return (this.errorCount / (totalTime / 1000)) * 100; // Errors per second * 100
  }

  private addMetric(category: string, metric: PerformanceMetrics) {
    if (!this.metrics.has(category)) {
      this.metrics.set(category, []);
    }
    
    const categoryMetrics = this.metrics.get(category)!;
    categoryMetrics.push(metric);
    
    // Keep only last 100 metrics per category
    if (categoryMetrics.length > 100) {
      categoryMetrics.shift();
    }
  }

  private recordError(type: string, error: any) {
    console.error(`Performance Manager - ${type} error:`, error);
    
    // Check if it's a chunk loading error
    if (error && error.message && error.message.includes('Loading chunk')) {
      this.handleChunkLoadError(error);
    }
  }

  private handleChunkLoadError(error: any) {
    console.warn('Chunk loading error detected, attempting recovery:', error);
    
    // Attempt to reload the page after a delay
    setTimeout(() => {
      if (this.errorCount > 3) {
        console.log('Multiple chunk errors detected, reloading page...');
        window.location.reload();
      }
    }, 2000);
  }

  private triggerMemoryCleanup() {
    // Clear old metrics
    this.metrics.forEach((metrics, category) => {
      if (metrics.length > 50) {
        this.metrics.set(category, metrics.slice(-25));
      }
    });

    // Suggest garbage collection if available
    if ('gc' in window) {
      (window as any).gc();
    }
  }

  // Public methods
  public getMetrics(category?: string): PerformanceMetrics[] {
    if (category) {
      return this.metrics.get(category) || [];
    }
    
    const allMetrics: PerformanceMetrics[] = [];
    this.metrics.forEach(metrics => allMetrics.push(...metrics));
    return allMetrics;
  }

  public getAverageMetrics(category: string): Partial<PerformanceMetrics> | null {
    const metrics = this.metrics.get(category);
    if (!metrics || metrics.length === 0) return null;

    const avg = metrics.reduce((acc, metric) => ({
      loadTime: acc.loadTime + metric.loadTime,
      renderTime: acc.renderTime + metric.renderTime,
      memoryUsage: acc.memoryUsage + metric.memoryUsage,
      networkLatency: acc.networkLatency + metric.networkLatency,
      errorRate: acc.errorRate + metric.errorRate
    }), { loadTime: 0, renderTime: 0, memoryUsage: 0, networkLatency: 0, errorRate: 0 });

    const count = metrics.length;
    return {
      loadTime: avg.loadTime / count,
      renderTime: avg.renderTime / count,
      memoryUsage: avg.memoryUsage / count,
      networkLatency: avg.networkLatency / count,
      errorRate: avg.errorRate / count
    };
  }

  public getPerformanceScore(): number {
    const navMetrics = this.getAverageMetrics('navigation');
    if (!navMetrics) return 0;

    let score = 100;
    
    // Deduct points for slow loading
    if (navMetrics.loadTime! > 3000) score -= 20;
    else if (navMetrics.loadTime! > 2000) score -= 10;
    
    // Deduct points for high error rate
    if (navMetrics.errorRate! > 1) score -= 30;
    else if (navMetrics.errorRate! > 0.5) score -= 15;
    
    // Deduct points for high memory usage
    if (navMetrics.memoryUsage! > 100 * 1024 * 1024) score -= 15; // 100MB
    
    return Math.max(0, score);
  }

  public getOptimizationRecommendations(): string[] {
    const recommendations: string[] = [];
    const navMetrics = this.getAverageMetrics('navigation');
    const resourceMetrics = this.getAverageMetrics('resource');

    if (navMetrics?.loadTime && navMetrics.loadTime > 3000) {
      recommendations.push('Consider optimizing initial bundle size');
      recommendations.push('Enable code splitting for better performance');
    }

    if (navMetrics?.errorRate && navMetrics.errorRate > 0.5) {
      recommendations.push('High error rate detected - review error handling');
    }

    if (resourceMetrics?.loadTime && resourceMetrics.loadTime > 1000) {
      recommendations.push('Optimize resource loading with preloading');
    }

    if (this.errorCount > 5) {
      recommendations.push('Multiple errors detected - consider error boundary improvements');
    }

    return recommendations;
  }

  public cleanup() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.metrics.clear();
  }
}

// React hooks for performance optimization
export const usePerformanceMonitoring = () => {
  const manager = EnhancedPerformanceManager.getInstance();
  const [metrics, setMetrics] = useState<PerformanceMetrics[]>([]);
  const [score, setScore] = useState<number>(0);

  useEffect(() => {
    const updateMetrics = () => {
      setMetrics(manager.getMetrics());
      setScore(manager.getPerformanceScore());
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, 5000);

    return () => clearInterval(interval);
  }, [manager]);

  return {
    metrics,
    score,
    recommendations: manager.getOptimizationRecommendations(),
    getAverageMetrics: (category: string) => manager.getAverageMetrics(category)
  };
};

export const useOptimizedComponent = (componentName: string) => {
  const renderStart = useRef<number>(0);
  const manager = EnhancedPerformanceManager.getInstance();

  useEffect(() => {
    renderStart.current = performance.now();
  }, []);

  useEffect(() => {
    const renderTime = performance.now() - renderStart.current;
    
    const metrics: PerformanceMetrics = {
      loadTime: 0,
      renderTime,
      memoryUsage: manager['getMemoryUsage'](),
      chunkLoadErrors: 0,
      networkLatency: 0,
      errorRate: 0,
      userInteractions: 0,
      timestamp: Date.now()
    };

    manager['addMetric'](`component-${componentName}`, metrics);
  });

  return {
    recordInteraction: useCallback(() => {
      // Record user interaction for this component
    }, [])
  };
};

export default EnhancedPerformanceManager;
