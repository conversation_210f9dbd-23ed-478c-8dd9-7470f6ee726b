import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Alert,
  <PERSON>,
  Stack,
  Divider,
  Grid,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  useTheme,
  alpha
} from '@mui/material';
import {
  Speed,
  Memory,
  NetworkCheck,
  Error as ErrorIcon,
  CheckCircle,
  Warning,
  Info,
  Refresh,
  TrendingUp,
  TrendingDown
} from '@mui/icons-material';
import { usePerformanceMonitoring } from '../../utils/enhancedPerformanceOptimization';
import { useErrorRecovery } from '../../utils/enhancedErrorRecovery';
import { AppError, ErrorType } from '../../utils/errorHandling';

interface PerformanceTestResult {
  test: string;
  status: 'pass' | 'fail' | 'warning' | 'info';
  value: number;
  threshold: number;
  unit: string;
  message: string;
}

const PerformanceTest: React.FC = () => {
  const theme = useTheme();
  const { metrics, score, recommendations, getAverageMetrics } = usePerformanceMonitoring();
  const { attemptRecovery, getRecoveryHistory } = useErrorRecovery();
  
  const [testResults, setTestResults] = useState<PerformanceTestResult[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [memoryUsage, setMemoryUsage] = useState<number>(0);
  const [networkLatency, setNetworkLatency] = useState<number>(0);

  useEffect(() => {
    // Monitor memory usage
    const updateMemoryUsage = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        setMemoryUsage(memory.usedJSHeapSize / (1024 * 1024)); // Convert to MB
      }
    };

    updateMemoryUsage();
    const memoryInterval = setInterval(updateMemoryUsage, 2000);

    // Monitor network latency
    const measureLatency = async () => {
      const start = performance.now();
      try {
        await fetch('/api/health', { method: 'HEAD' });
        setNetworkLatency(performance.now() - start);
      } catch (error) {
        setNetworkLatency(-1); // Error state
      }
    };

    measureLatency();
    const latencyInterval = setInterval(measureLatency, 10000);

    return () => {
      clearInterval(memoryInterval);
      clearInterval(latencyInterval);
    };
  }, []);

  const runPerformanceTests = async () => {
    setIsRunningTests(true);
    setTestResults([]);

    const results: PerformanceTestResult[] = [];

    // Test 1: Page Load Time
    const navMetrics = getAverageMetrics('navigation');
    if (navMetrics?.loadTime) {
      results.push({
        test: 'Page Load Time',
        status: navMetrics.loadTime < 2000 ? 'pass' : navMetrics.loadTime < 4000 ? 'warning' : 'fail',
        value: navMetrics.loadTime,
        threshold: 2000,
        unit: 'ms',
        message: `Page loads in ${navMetrics.loadTime.toFixed(0)}ms`
      });
    }

    // Test 2: Memory Usage
    results.push({
      test: 'Memory Usage',
      status: memoryUsage < 50 ? 'pass' : memoryUsage < 100 ? 'warning' : 'fail',
      value: memoryUsage,
      threshold: 50,
      unit: 'MB',
      message: `Using ${memoryUsage.toFixed(1)}MB of memory`
    });

    // Test 3: Network Latency
    if (networkLatency >= 0) {
      results.push({
        test: 'Network Latency',
        status: networkLatency < 100 ? 'pass' : networkLatency < 300 ? 'warning' : 'fail',
        value: networkLatency,
        threshold: 100,
        unit: 'ms',
        message: `API response time: ${networkLatency.toFixed(0)}ms`
      });
    } else {
      results.push({
        test: 'Network Latency',
        status: 'fail',
        value: 0,
        threshold: 100,
        unit: 'ms',
        message: 'Network connection failed'
      });
    }

    // Test 4: Error Rate
    const errorRate = navMetrics?.errorRate || 0;
    results.push({
      test: 'Error Rate',
      status: errorRate < 0.1 ? 'pass' : errorRate < 1 ? 'warning' : 'fail',
      value: errorRate,
      threshold: 0.1,
      unit: '%',
      message: `Error rate: ${errorRate.toFixed(2)}%`
    });

    // Test 5: Performance Score
    results.push({
      test: 'Overall Performance Score',
      status: score > 80 ? 'pass' : score > 60 ? 'warning' : 'fail',
      value: score,
      threshold: 80,
      unit: '/100',
      message: `Performance score: ${score}/100`
    });

    // Test 6: Resource Loading
    const resourceMetrics = getAverageMetrics('resource');
    if (resourceMetrics?.loadTime) {
      results.push({
        test: 'Resource Load Time',
        status: resourceMetrics.loadTime < 500 ? 'pass' : resourceMetrics.loadTime < 1000 ? 'warning' : 'fail',
        value: resourceMetrics.loadTime,
        threshold: 500,
        unit: 'ms',
        message: `Resources load in ${resourceMetrics.loadTime.toFixed(0)}ms`
      });
    }

    // Test 7: Long Tasks
    const longTaskMetrics = getAverageMetrics('longtask');
    if (longTaskMetrics?.loadTime) {
      results.push({
        test: 'Long Tasks',
        status: longTaskMetrics.loadTime < 50 ? 'pass' : longTaskMetrics.loadTime < 100 ? 'warning' : 'fail',
        value: longTaskMetrics.loadTime,
        threshold: 50,
        unit: 'ms',
        message: `Average long task duration: ${longTaskMetrics.loadTime.toFixed(0)}ms`
      });
    } else {
      results.push({
        test: 'Long Tasks',
        status: 'pass',
        value: 0,
        threshold: 50,
        unit: 'ms',
        message: 'No long tasks detected'
      });
    }

    setTestResults(results);
    setIsRunningTests(false);
  };

  const testErrorRecovery = async () => {
    // Simulate different types of errors for testing
    const testErrors = [
      new AppError('Network connection failed', ErrorType.NETWORK),
      new AppError('Loading chunk 123 failed', ErrorType.UNKNOWN),
      new AppError('Authentication token expired', ErrorType.AUTHENTICATION)
    ];

    for (const error of testErrors) {
      console.log(`Testing recovery for: ${error.message}`);
      const recovered = await attemptRecovery(error, {
        componentName: 'PerformanceTest',
        route: '/testing'
      });
      console.log(`Recovery ${recovered ? 'successful' : 'failed'} for: ${error.message}`);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass':
        return <CheckCircle sx={{ color: theme.palette.success.main }} />;
      case 'fail':
        return <ErrorIcon sx={{ color: theme.palette.error.main }} />;
      case 'warning':
        return <Warning sx={{ color: theme.palette.warning.main }} />;
      default:
        return <Info sx={{ color: theme.palette.info.main }} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass':
        return theme.palette.success.main;
      case 'fail':
        return theme.palette.error.main;
      case 'warning':
        return theme.palette.warning.main;
      default:
        return theme.palette.info.main;
    }
  };

  const recoveryHistory = getRecoveryHistory();

  return (
    <Box sx={{ p: 3 }}>
      <Card
        sx={{
          background: alpha(theme.palette.background.paper, 0.9),
          backdropFilter: 'blur(20px)',
          border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
        }}
      >
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
            <Speed sx={{ fontSize: 40, color: theme.palette.primary.main }} />
            <Box>
              <Typography variant="h4" fontWeight="bold">
                Performance & Error Recovery Testing
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                Monitor app performance, test error recovery, and optimize user experience
              </Typography>
            </Box>
          </Box>

          {/* Performance Overview */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={3}>
              <Card variant="outlined">
                <CardContent sx={{ textAlign: 'center' }}>
                  <TrendingUp sx={{ fontSize: 40, color: theme.palette.success.main, mb: 1 }} />
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    {score}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Performance Score
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={3}>
              <Card variant="outlined">
                <CardContent sx={{ textAlign: 'center' }}>
                  <Memory sx={{ fontSize: 40, color: theme.palette.info.main, mb: 1 }} />
                  <Typography variant="h4" fontWeight="bold" color="info.main">
                    {memoryUsage.toFixed(1)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Memory Usage (MB)
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={3}>
              <Card variant="outlined">
                <CardContent sx={{ textAlign: 'center' }}>
                  <NetworkCheck sx={{ fontSize: 40, color: theme.palette.warning.main, mb: 1 }} />
                  <Typography variant="h4" fontWeight="bold" color="warning.main">
                    {networkLatency >= 0 ? networkLatency.toFixed(0) : 'N/A'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Network Latency (ms)
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={3}>
              <Card variant="outlined">
                <CardContent sx={{ textAlign: 'center' }}>
                  <ErrorIcon sx={{ fontSize: 40, color: theme.palette.error.main, mb: 1 }} />
                  <Typography variant="h4" fontWeight="bold" color="error.main">
                    {recoveryHistory.size}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Recovery Attempts
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Test Controls */}
          <Stack direction="row" spacing={2} sx={{ mb: 3 }}>
            <Button
              variant="contained"
              startIcon={<Speed />}
              onClick={runPerformanceTests}
              disabled={isRunningTests}
              sx={{
                background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
                '&:hover': {
                  background: `linear-gradient(135deg, ${theme.palette.primary.dark}, ${theme.palette.primary.main})`
                }
              }}
            >
              {isRunningTests ? 'Running Tests...' : 'Run Performance Tests'}
            </Button>

            <Button
              variant="outlined"
              startIcon={<Refresh />}
              onClick={testErrorRecovery}
            >
              Test Error Recovery
            </Button>
          </Stack>

          {/* Recommendations */}
          {recommendations.length > 0 && (
            <Alert severity="info" sx={{ mb: 3 }}>
              <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
                Performance Recommendations:
              </Typography>
              {recommendations.map((rec, index) => (
                <Typography key={index} variant="body2">
                  • {rec}
                </Typography>
              ))}
            </Alert>
          )}

          {/* Test Results */}
          {testResults.length > 0 && (
            <>
              <Typography variant="h6" gutterBottom>
                Performance Test Results
              </Typography>
              
              <TableContainer component={Paper} sx={{ mb: 3 }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Test</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Value</TableCell>
                      <TableCell>Threshold</TableCell>
                      <TableCell>Message</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {testResults.map((result, index) => (
                      <TableRow key={index}>
                        <TableCell>{result.test}</TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {getStatusIcon(result.status)}
                            <Chip
                              label={result.status.toUpperCase()}
                              size="small"
                              sx={{
                                backgroundColor: getStatusColor(result.status),
                                color: 'white',
                                fontWeight: 'bold'
                              }}
                            />
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography fontWeight="bold">
                            {result.value.toFixed(result.unit === 'ms' ? 0 : 2)} {result.unit}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography color="text.secondary">
                            {'<'} {result.threshold} {result.unit}
                          </Typography>
                        </TableCell>
                        <TableCell>{result.message}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </>
          )}

          {/* Performance Progress */}
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2">Overall Performance</Typography>
              <Typography variant="body2">{score}/100</Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={score}
              sx={{
                height: 8,
                borderRadius: 4,
                backgroundColor: alpha(theme.palette.grey[500], 0.3),
                '& .MuiLinearProgress-bar': {
                  background: score > 80 
                    ? `linear-gradient(90deg, ${theme.palette.success.main}, ${theme.palette.success.light})`
                    : score > 60
                    ? `linear-gradient(90deg, ${theme.palette.warning.main}, ${theme.palette.warning.light})`
                    : `linear-gradient(90deg, ${theme.palette.error.main}, ${theme.palette.error.light})`
                }
              }}
            />
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default PerformanceTest;
