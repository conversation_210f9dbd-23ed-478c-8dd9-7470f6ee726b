import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  useTheme,
  alpha,
  Chip,
  Divider
} from '@mui/material';
import {
  Download,
  Assessment,
  Lock,
  Star,
  Upgrade,
  TableChart,
  PieChart,
  BarChart,
  TrendingUp,
  MonetizationOn,
  Pets,
  Restaurant,
  LocalHospital,
  Warning
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../../contexts/LanguageContext';
import { useMongoAnimalData } from '../../hooks/useMongoAnimalData';
import * as XLSX from 'xlsx';

interface BetaReportsModuleProps {
  userTier: 'beta' | 'professional' | 'enterprise';
}

const BetaReportsModule: React.FC<BetaReportsModuleProps> = ({ userTier }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { translate } = useLanguage();
  const { animals, stats } = useMongoAnimalData();
  
  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);

  // Available reports for BETA users (Excel only)
  const betaReports = [
    {
      id: 'animals-basic',
      title: 'Animals Basic Report',
      description: 'Complete list of all animals with basic information',
      icon: <Pets />,
      available: true,
      format: 'Excel',
      data: animals || []
    },
    {
      id: 'feed-summary',
      title: 'Feed Summary Report',
      description: 'Basic feed consumption and cost summary',
      icon: <Restaurant />,
      available: true,
      format: 'Excel',
      data: [] // Would be populated from feed module
    },
    {
      id: 'health-basic',
      title: 'Health Status Report',
      description: 'Current health status of all animals',
      icon: <LocalHospital />,
      available: true,
      format: 'Excel',
      data: animals?.map(animal => ({
        id: animal.id,
        name: animal.name,
        species: animal.species,
        healthStatus: animal.healthStatus || 'Unknown',
        lastCheckup: animal.lastHealthCheck || 'N/A'
      })) || []
    }
  ];

  // Premium reports (locked for BETA users)
  const premiumReports = [
    {
      id: 'financial-analysis',
      title: 'Financial Analysis Report',
      description: 'Comprehensive financial performance analysis with ROI calculations',
      icon: <MonetizationOn />,
      available: false,
      premium: true,
      formats: ['PDF', 'Excel', 'Interactive Dashboard']
    },
    {
      id: 'breeding-optimization',
      title: 'Breeding Optimization Report',
      description: 'AI-powered breeding recommendations and genetic analysis',
      icon: <TrendingUp />,
      available: false,
      premium: true,
      formats: ['PDF', 'Interactive Dashboard', 'Mobile App']
    },
    {
      id: 'predictive-analytics',
      title: 'Predictive Analytics Report',
      description: 'Future trends, health predictions, and market insights',
      icon: <Assessment />,
      available: false,
      premium: true,
      formats: ['Interactive Dashboard', 'PDF', 'API Access']
    },
    {
      id: 'custom-reports',
      title: 'Custom Report Builder',
      description: 'Build your own reports with drag-and-drop interface',
      icon: <BarChart />,
      available: false,
      premium: true,
      formats: ['Any Format', 'Scheduled Delivery', 'White-label']
    }
  ];

  const generateExcelReport = async (reportId: string, data: any[], title: string) => {
    setIsGenerating(true);
    
    try {
      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      if (data.length === 0) {
        // Generate sample data for demo
        data = [
          { id: 1, name: 'Sample Data', note: 'This is demo data for BETA users' },
          { id: 2, name: 'Upgrade to Professional', note: 'Get real data with paid subscription' }
        ];
      }

      const worksheet = XLSX.utils.json_to_sheet(data);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, title);
      
      // Add metadata sheet
      const metaData = [
        { Field: 'Generated By', Value: 'AgriIntel BETA' },
        { Field: 'Date', Value: new Date().toLocaleDateString() },
        { Field: 'User Tier', Value: 'BETA (Limited)' },
        { Field: 'Upgrade Info', Value: 'Professional: R299/month | Enterprise: R599/month' }
      ];
      const metaSheet = XLSX.utils.json_to_sheet(metaData);
      XLSX.utils.book_append_sheet(workbook, metaSheet, 'Report Info');
      
      XLSX.writeFile(workbook, `AgriIntel_${title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.xlsx`);
    } catch (error) {
      console.error('Error generating report:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handlePremiumReport = () => {
    setShowUpgradeDialog(true);
  };

  const handleUpgrade = (tier: 'professional' | 'enterprise') => {
    navigate(`/subscription?tier=${tier}`);
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" fontWeight="bold" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Assessment sx={{ color: theme.palette.primary.main }} />
          Reports & Analytics
          <Chip 
            label="BETA" 
            size="small" 
            sx={{ 
              backgroundColor: alpha(theme.palette.warning.main, 0.2),
              color: theme.palette.warning.main,
              fontWeight: 'bold'
            }} 
          />
        </Typography>
        
        <Button
          variant="outlined"
          startIcon={<Upgrade />}
          onClick={() => setShowUpgradeDialog(true)}
          sx={{ borderColor: theme.palette.warning.main, color: theme.palette.warning.main }}
        >
          Upgrade for Advanced Reports
        </Button>
      </Box>

      {/* BETA Limitation Alert */}
      <Alert severity="info" sx={{ mb: 4 }}>
        <Typography variant="body2">
          <strong>BETA Reports:</strong> Excel format only with basic data. 
          Upgrade to Professional for PDF reports, interactive dashboards, and advanced analytics.
        </Typography>
      </Alert>

      {/* Available Reports (BETA) */}
      <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
        Available Reports (Excel Format)
      </Typography>
      
      <Grid container spacing={3} sx={{ mb: 6 }}>
        {betaReports.map((report, index) => (
          <Grid item xs={12} md={6} lg={4} key={report.id}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ y: -4 }}
            >
              <Card sx={{ 
                height: '100%', 
                borderRadius: 3,
                background: 'rgba(255,255,255,0.05)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255,255,255,0.1)'
              }}>
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Box sx={{ 
                      p: 1.5, 
                      borderRadius: 2, 
                      backgroundColor: alpha(theme.palette.primary.main, 0.1),
                      color: theme.palette.primary.main,
                      mr: 2
                    }}>
                      {report.icon}
                    </Box>
                    <Chip 
                      label={report.format} 
                      size="small" 
                      color="primary" 
                      variant="outlined"
                    />
                  </Box>
                  
                  <Typography variant="h6" fontWeight="bold" gutterBottom>
                    {report.title}
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    {report.description}
                  </Typography>
                  
                  <Button
                    variant="contained"
                    fullWidth
                    startIcon={<Download />}
                    onClick={() => generateExcelReport(report.id, report.data, report.title)}
                    disabled={isGenerating}
                    sx={{ borderRadius: 2 }}
                  >
                    {isGenerating ? 'Generating...' : 'Download Excel'}
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* Premium Reports (Locked) */}
      <Typography variant="h5" fontWeight="bold" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
        Premium Reports
        <Lock sx={{ color: theme.palette.warning.main }} />
      </Typography>
      
      <Grid container spacing={3}>
        {premiumReports.map((report, index) => (
          <Grid item xs={12} md={6} lg={4} key={report.id}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: (betaReports.length + index) * 0.1 }}
              whileHover={{ y: -4 }}
            >
              <Card sx={{ 
                height: '100%', 
                borderRadius: 3,
                background: 'linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 193, 7, 0.05))',
                backdropFilter: 'blur(10px)',
                border: '2px solid rgba(255, 215, 0, 0.3)',
                position: 'relative',
                overflow: 'hidden'
              }}>
                {/* Premium Badge */}
                <Box sx={{
                  position: 'absolute',
                  top: 16,
                  right: 16,
                  zIndex: 1
                }}>
                  <Chip 
                    label="PREMIUM" 
                    size="small" 
                    sx={{ 
                      backgroundColor: '#FFD700',
                      color: '#2E7D32',
                      fontWeight: 'bold'
                    }}
                    icon={<Star sx={{ color: '#2E7D32' }} />}
                  />
                </Box>

                <CardContent sx={{ p: 3, opacity: 0.8 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Box sx={{ 
                      p: 1.5, 
                      borderRadius: 2, 
                      backgroundColor: alpha('#FFD700', 0.2),
                      color: '#FFD700',
                      mr: 2
                    }}>
                      {report.icon}
                    </Box>
                    <Box>
                      {report.formats?.slice(0, 2).map((format, idx) => (
                        <Chip 
                          key={idx}
                          label={format} 
                          size="small" 
                          sx={{ 
                            mr: 0.5, 
                            mb: 0.5,
                            backgroundColor: alpha('#FFD700', 0.2),
                            color: '#FFD700'
                          }}
                        />
                      ))}
                    </Box>
                  </Box>
                  
                  <Typography variant="h6" fontWeight="bold" gutterBottom>
                    {report.title}
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    {report.description}
                  </Typography>
                  
                  <Button
                    variant="contained"
                    fullWidth
                    startIcon={<Lock />}
                    onClick={handlePremiumReport}
                    sx={{ 
                      borderRadius: 2,
                      backgroundColor: '#FFD700',
                      color: '#2E7D32',
                      '&:hover': {
                        backgroundColor: '#FFC107'
                      }
                    }}
                  >
                    Upgrade to Unlock
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* Upgrade Dialog */}
      <Dialog open={showUpgradeDialog} onClose={() => setShowUpgradeDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle sx={{ textAlign: 'center', pb: 1 }}>
          <Star sx={{ color: '#FFD700', fontSize: 48, mb: 1 }} />
          <Typography variant="h5" fontWeight="bold">
            Unlock Advanced Reporting
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" sx={{ textAlign: 'center', mb: 3 }}>
            Get comprehensive insights with professional reporting tools and analytics.
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Card sx={{ border: '2px solid', borderColor: theme.palette.primary.main }}>
                <CardContent>
                  <Typography variant="h6" fontWeight="bold" color="primary" gutterBottom>
                    Professional - R299/month
                  </Typography>
                  <List dense>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <TableChart sx={{ color: theme.palette.primary.main, fontSize: 20 }} />
                      </ListItemIcon>
                      <ListItemText primary="PDF & Excel Reports" />
                    </ListItem>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <PieChart sx={{ color: theme.palette.primary.main, fontSize: 20 }} />
                      </ListItemIcon>
                      <ListItemText primary="Interactive Dashboards" />
                    </ListItem>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <TrendingUp sx={{ color: theme.palette.primary.main, fontSize: 20 }} />
                      </ListItemIcon>
                      <ListItemText primary="Financial Analytics" />
                    </ListItem>
                  </List>
                  <Button 
                    variant="contained" 
                    fullWidth 
                    onClick={() => handleUpgrade('professional')}
                    sx={{ mt: 2 }}
                  >
                    Choose Professional
                  </Button>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Card sx={{ border: '2px solid', borderColor: theme.palette.secondary.main }}>
                <CardContent>
                  <Typography variant="h6" fontWeight="bold" color="secondary" gutterBottom>
                    Enterprise - R599/month
                  </Typography>
                  <List dense>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <Assessment sx={{ color: theme.palette.secondary.main, fontSize: 20 }} />
                      </ListItemIcon>
                      <ListItemText primary="Custom Report Builder" />
                    </ListItem>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <BarChart sx={{ color: theme.palette.secondary.main, fontSize: 20 }} />
                      </ListItemIcon>
                      <ListItemText primary="Advanced AI Analytics" />
                    </ListItem>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <MonetizationOn sx={{ color: theme.palette.secondary.main, fontSize: 20 }} />
                      </ListItemIcon>
                      <ListItemText primary="White-label Reports" />
                    </ListItem>
                  </List>
                  <Button 
                    variant="contained" 
                    color="secondary" 
                    fullWidth 
                    onClick={() => handleUpgrade('enterprise')}
                    sx={{ mt: 2 }}
                  >
                    Choose Enterprise
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowUpgradeDialog(false)}>
            Continue with BETA
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BetaReportsModule;
