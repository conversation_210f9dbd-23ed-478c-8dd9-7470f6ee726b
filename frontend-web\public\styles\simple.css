/* Simple HTML Page Styles */

.simple-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
  color: white;
  font-family: Arial, sans-serif;
  padding: 2rem;
}

.simple-container {
  max-width: 1200px;
  margin: 0 auto;
}

.simple-header {
  text-align: center;
  margin-bottom: 4rem;
}

.simple-title {
  font-size: 3rem;
  margin-bottom: 1rem;
  font-weight: bold;
}

.simple-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 2rem;
}

.simple-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.simple-btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
}

.simple-btn-beta {
  background: #FF9800;
  color: white;
}

.simple-btn-beta:hover {
  background: #F57C00;
  transform: translateY(-2px);
}

.simple-btn-live {
  background: #2196F3;
  color: white;
}

.simple-btn-live:hover {
  background: #1976D2;
  transform: translateY(-2px);
}

.simple-success-message {
  background: rgba(76, 175, 80, 0.2);
  border: 2px solid #4CAF50;
  padding: 1rem;
  border-radius: 10px;
  margin-bottom: 2rem;
  text-align: center;
}

.simple-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.simple-feature {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 10px;
  text-align: center;
  transition: transform 0.3s ease;
}

.simple-feature:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
}

.simple-feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.simple-feature h3 {
  margin: 0 0 1rem 0;
  font-size: 1.3rem;
}

.simple-feature p {
  margin: 0;
  opacity: 0.9;
  line-height: 1.5;
}

.simple-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.simple-stat {
  text-align: center;
}

.simple-stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: #FFD700;
  margin-bottom: 0.5rem;
}

.simple-stat-label {
  font-size: 1rem;
  opacity: 0.9;
}

.simple-pricing {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.simple-price-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 10px;
  text-align: center;
  transition: transform 0.3s ease;
}

.simple-price-card:hover {
  transform: translateY(-5px);
}

.simple-price-card.featured {
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid #FFD700;
}

.simple-price-card h3 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
}

.simple-price {
  font-size: 2rem;
  font-weight: bold;
  color: #FFD700;
  margin-bottom: 1rem;
}

.simple-price-description {
  margin-bottom: 1.5rem;
  opacity: 0.9;
}

.simple-price-features {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
}

.simple-price-features li {
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 5px;
}

.simple-price-button {
  width: 100%;
  padding: 1rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
}

.simple-price-button.beta {
  background: #FF9800;
  color: white;
}

.simple-price-button.beta:hover {
  background: #F57C00;
}

.simple-price-button.live {
  background: #2196F3;
  color: white;
}

.simple-price-button.live:hover {
  background: #1976D2;
}

.simple-footer {
  text-align: center;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  margin-top: 3rem;
}

.simple-footer h3 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
}

.simple-footer p {
  margin: 0 0 1rem 0;
  opacity: 0.9;
}

.simple-footer-contact {
  margin-top: 1rem;
}

.simple-footer-contact span {
  margin: 0 1rem;
  display: inline-block;
}

.simple-footer-copyright {
  margin-top: 1rem;
  opacity: 0.7;
}

/* Responsive Design */
@media (max-width: 768px) {
  .simple-page {
    padding: 1rem;
  }
  
  .simple-title {
    font-size: 2rem;
  }
  
  .simple-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .simple-btn {
    width: 100%;
    max-width: 300px;
  }
  
  .simple-footer-contact span {
    display: block;
    margin: 0.5rem 0;
  }
}
