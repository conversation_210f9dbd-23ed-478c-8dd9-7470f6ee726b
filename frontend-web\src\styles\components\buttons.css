/**
 * <PERSON><PERSON> Component Styles
 * Professional button designs for AgriIntel platform
 */

/* ===== BASE BUTTON STYLES ===== */

.agri-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-lg);
  font-family: inherit;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  text-decoration: none;
  text-transform: none;
  cursor: pointer;
  transition: var(--transition-normal);
  -webkit-user-select: none;
  user-select: none;
  white-space: nowrap;
  min-height: 44px;
  min-width: 44px;
}

.agri-btn:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}

.agri-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* ===== BUTTON VARIANTS ===== */

/* Primary Button */
.agri-btn-primary {
  background: var(--agri-pro-gradient);
  color: var(--color-white);
  border: none;
  box-shadow: 0 4px 15px rgba(46, 125, 50, 0.3);
}

.agri-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(46, 125, 50, 0.4);
}

.agri-btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(46, 125, 50, 0.3);
}

/* Secondary Button */
.agri-btn-secondary {
  background: var(--agri-beta-gradient);
  color: var(--color-white);
  border: none;
  box-shadow: 0 4px 15px rgba(245, 124, 0, 0.3);
}

.agri-btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(245, 124, 0, 0.4);
}

.agri-btn-secondary:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(245, 124, 0, 0.3);
}

/* Outline Button */
.agri-btn-outline {
  background: transparent;
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.agri-btn-outline:hover {
  background: var(--color-background-hover);
  border-color: var(--agri-primary);
  color: var(--agri-primary);
  transform: translateY(-1px);
}

.agri-btn-outline:active {
  transform: translateY(0);
}

/* Ghost Button */
.agri-btn-ghost {
  background: transparent;
  color: var(--color-text-primary);
  border: none;
}

.agri-btn-ghost:hover {
  background: var(--color-background-hover);
  color: var(--agri-primary);
}

/* Text Button */
.agri-btn-text {
  background: transparent;
  color: var(--agri-primary);
  border: none;
  padding: var(--spacing-xs) var(--spacing-sm);
  min-height: 0;
  min-width: auto;
}

.agri-btn-text:hover {
  background: rgba(21, 101, 192, 0.1);
  color: var(--agri-primary-dark);
}

/* ===== BUTTON SIZES ===== */

.agri-btn-xs {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
  min-height: 32px;
  border-radius: var(--radius-md);
}

.agri-btn-sm {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
  min-height: 36px;
  border-radius: var(--radius-md);
}

.agri-btn-md {
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-base);
  min-height: 44px;
  border-radius: var(--radius-lg);
}

.agri-btn-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
  min-height: 52px;
  border-radius: var(--radius-xl);
}

.agri-btn-xl {
  padding: var(--spacing-lg) var(--spacing-2xl);
  font-size: var(--font-size-xl);
  min-height: 60px;
  border-radius: var(--radius-xl);
}

/* ===== BUTTON STATES ===== */

.agri-btn-success {
  background: var(--color-success);
  color: var(--color-white);
  border: none;
}

.agri-btn-success:hover {
  background: var(--color-success-dark);
  transform: translateY(-1px);
}

.agri-btn-warning {
  background: var(--color-warning);
  color: var(--color-white);
  border: none;
}

.agri-btn-warning:hover {
  background: var(--color-warning-dark);
  transform: translateY(-1px);
}

.agri-btn-error {
  background: var(--color-error);
  color: var(--color-white);
  border: none;
}

.agri-btn-error:hover {
  background: var(--color-error-dark);
  transform: translateY(-1px);
}

.agri-btn-info {
  background: var(--color-info);
  color: var(--color-white);
  border: none;
}

.agri-btn-info:hover {
  background: var(--color-info-dark);
  transform: translateY(-1px);
}

/* ===== BUTTON GROUPS ===== */

.agri-btn-group {
  display: inline-flex;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.agri-btn-group .agri-btn {
  border-radius: 0;
  border-right: 1px solid var(--color-border);
}

.agri-btn-group .agri-btn:first-child {
  border-radius: var(--radius-lg) 0 0 var(--radius-lg);
}

.agri-btn-group .agri-btn:last-child {
  border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
  border-right: none;
}

.agri-btn-group .agri-btn:only-child {
  border-radius: var(--radius-lg);
  border-right: none;
}

/* ===== ICON BUTTONS ===== */

.agri-btn-icon {
  padding: var(--spacing-sm);
  min-width: 44px;
  min-height: 44px;
  border-radius: var(--radius-full);
}

.agri-btn-icon-sm {
  padding: var(--spacing-xs);
  min-width: 36px;
  min-height: 36px;
}

.agri-btn-icon-lg {
  padding: var(--spacing-md);
  min-width: 52px;
  min-height: 52px;
}

/* ===== FLOATING ACTION BUTTON ===== */

.agri-fab {
  position: fixed;
  bottom: var(--spacing-xl);
  right: var(--spacing-xl);
  width: 56px;
  height: 56px;
  border-radius: var(--radius-full);
  background: var(--agri-primary);
  color: var(--color-white);
  border: none;
  box-shadow: 0 6px 20px rgba(21, 101, 192, 0.4);
  cursor: pointer;
  transition: var(--transition-normal);
  z-index: var(--z-fab);
}

.agri-fab:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(21, 101, 192, 0.5);
}

.agri-fab:active {
  transform: scale(1.05);
}

/* ===== LOADING STATES ===== */

.agri-btn-loading {
  position: relative;
  color: transparent;
  pointer-events: none;
}

.agri-btn-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: var(--radius-full);
  animation: agri-btn-spin 1s linear infinite;
}

@keyframes agri-btn-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
  .agri-btn-lg {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-base);
    min-height: 44px;
  }
  
  .agri-btn-xl {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
    min-height: 52px;
  }
  
  .agri-fab {
    bottom: var(--spacing-lg);
    right: var(--spacing-lg);
    width: 48px;
    height: 48px;
  }
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  .agri-btn:hover,
  .agri-fab:hover {
    transform: none;
  }
  
  .agri-btn-loading::after {
    animation: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .agri-btn-primary,
  .agri-btn-secondary {
    background: var(--color-text-primary);
    color: var(--color-background-primary);
    border: 2px solid var(--color-text-primary);
  }
  
  .agri-btn-outline {
    border: 2px solid var(--color-text-primary);
  }
  
  .agri-btn-ghost,
  .agri-btn-text {
    border: 1px solid var(--color-text-primary);
  }
}
