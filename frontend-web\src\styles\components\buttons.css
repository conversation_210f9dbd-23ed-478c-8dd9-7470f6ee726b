/**
 * AgriIntel 2026 - Next-Generation Button System
 * Futuristic, AI-Enhanced Agricultural Interface Components
 * Features: Glassmorphism, Neural Networks, Quantum Gradients, Haptic Feedback
 */

/* ===== CSS CUSTOM PROPERTIES FOR 2026 DESIGN ===== */
:root {
  /* Quantum Gradient System */
  --quantum-primary: linear-gradient(135deg, #00f5ff 0%, #0066ff 25%, #6600ff 50%, #ff0066 75%, #ff6600 100%);
  --quantum-secondary: linear-gradient(135deg, #ff6b35 0%, #f7931e 25%, #ffd23f 50%, #06ffa5 75%, #00d4ff 100%);
  --quantum-success: linear-gradient(135deg, #00ff88 0%, #00cc6a 50%, #009951 100%);
  --quantum-warning: linear-gradient(135deg, #ffb347 0%, #ff8c42 50%, #ff6b35 100%);
  --quantum-danger: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 50%, #d63031 100%);

  /* Neural Network Patterns */
  --neural-glow: 0 0 20px rgba(0, 245, 255, 0.3), 0 0 40px rgba(0, 245, 255, 0.2), 0 0 60px rgba(0, 245, 255, 0.1);
  --neural-pulse: 0 0 0 0 rgba(0, 245, 255, 0.7);

  /* Glassmorphism Effects */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-backdrop: blur(20px) saturate(180%);

  /* Micro-interaction Timings */
  --micro-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --micro-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --micro-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  --micro-bounce: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* ===== NEXT-GEN BASE BUTTON ARCHITECTURE ===== */

.agri-btn {
  /* Core Structure */
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 12px;

  /* Advanced Typography */
  font-family: 'Inter Variable', 'SF Pro Display', -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
  font-size: 16px;
  font-weight: 600;
  font-variation-settings: 'wght' 600, 'slnt' 0;
  letter-spacing: -0.01em;
  line-height: 1.2;

  /* Quantum Spacing & Sizing */
  padding: 16px 32px;
  min-height: 56px;
  min-width: 120px;

  /* Neural Border System */
  border: 1px solid transparent;
  border-radius: 16px;

  /* Glassmorphism Foundation */
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);

  /* Quantum Transitions */
  transition: all var(--micro-normal),
              transform var(--micro-fast),
              box-shadow var(--micro-normal),
              filter var(--micro-normal);

  /* Advanced Cursor & Interaction */
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;

  /* Neural Network Glow */
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.1),
    0 1px 3px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Quantum Focus State */
.agri-btn:focus-visible {
  outline: none;
  box-shadow:
    var(--neural-glow),
    0 0 0 4px var(--neural-pulse),
    0 4px 20px rgba(0, 0, 0, 0.1);
  animation: neural-pulse 2s infinite;
}

/* Neural Pulse Animation */
@keyframes neural-pulse {
  0% { box-shadow: var(--neural-glow), 0 0 0 0 rgba(0, 245, 255, 0.7); }
  70% { box-shadow: var(--neural-glow), 0 0 0 10px rgba(0, 245, 255, 0); }
  100% { box-shadow: var(--neural-glow), 0 0 0 0 rgba(0, 245, 255, 0); }
}

/* Quantum Disabled State */
.agri-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  pointer-events: none;
  filter: grayscale(100%);
  transform: none !important;
}

/* ===== QUANTUM BUTTON VARIANTS ===== */

/* Neural Primary Button - AI-Enhanced */
.agri-btn-primary {
  background: var(--quantum-primary);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.agri-btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--micro-slow);
}

.agri-btn-primary:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow:
    0 12px 40px rgba(0, 245, 255, 0.3),
    0 6px 20px rgba(0, 102, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  filter: brightness(1.1) saturate(1.2);
}

.agri-btn-primary:hover::before {
  left: 100%;
}

.agri-btn-primary:active {
  transform: translateY(-2px) scale(1.01);
  transition: transform var(--micro-fast);
}

/* Holographic Secondary Button */
.agri-btn-secondary {
  background: var(--quantum-secondary);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.agri-btn-secondary::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  transition: all var(--micro-normal);
  transform: translate(-50%, -50%);
  border-radius: 50%;
}

.agri-btn-secondary:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow:
    0 12px 40px rgba(255, 107, 53, 0.3),
    0 6px 20px rgba(247, 147, 30, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  filter: brightness(1.1) saturate(1.2);
}

.agri-btn-secondary:hover::after {
  width: 200px;
  height: 200px;
}

.agri-btn-secondary:active {
  transform: translateY(-2px) scale(1.01);
  transition: transform var(--micro-fast);
}

/* Neomorphic Outline Button */
.agri-btn-outline {
  background: rgba(255, 255, 255, 0.05);
  color: #00f5ff;
  border: 2px solid;
  border-image: var(--quantum-primary) 1;
  position: relative;
  overflow: hidden;
}

.agri-btn-outline::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 2px;
  background: var(--quantum-primary);
  border-radius: inherit;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
}

.agri-btn-outline:hover {
  background: rgba(0, 245, 255, 0.1);
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 8px 32px rgba(0, 245, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.agri-btn-outline:active {
  transform: translateY(-1px) scale(1.01);
}

/* Morphic Ghost Button */
.agri-btn-ghost {
  background: rgba(255, 255, 255, 0.03);
  color: #00f5ff;
  border: 1px solid rgba(0, 245, 255, 0.2);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.agri-btn-ghost:hover {
  background: rgba(0, 245, 255, 0.08);
  border-color: rgba(0, 245, 255, 0.4);
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 6px 24px rgba(0, 245, 255, 0.15);
}

/* Minimal Text Button */
.agri-btn-text {
  background: transparent;
  color: #00f5ff;
  border: none;
  padding: 12px 20px;
  min-height: 40px;
  min-width: auto;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.agri-btn-text::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--quantum-primary);
  transition: all var(--micro-normal);
  transform: translateX(-50%);
}

.agri-btn-text:hover {
  color: #ffffff;
  background: rgba(0, 245, 255, 0.05);
}

.agri-btn-text:hover::after {
  width: 100%;
}

/* ===== QUANTUM SIZE SYSTEM ===== */

.agri-btn-xs {
  padding: 8px 16px;
  font-size: 13px;
  font-weight: 500;
  min-height: 36px;
  border-radius: 12px;
  gap: 6px;
}

.agri-btn-sm {
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  min-height: 44px;
  border-radius: 14px;
  gap: 8px;
}

.agri-btn-md {
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  min-height: 56px;
  border-radius: 16px;
  gap: 12px;
}

.agri-btn-lg {
  padding: 20px 40px;
  font-size: 18px;
  font-weight: 600;
  min-height: 64px;
  border-radius: 20px;
  gap: 14px;
  font-variation-settings: 'wght' 650;
}

.agri-btn-xl {
  padding: 24px 48px;
  font-size: 20px;
  font-weight: 700;
  min-height: 72px;
  border-radius: 24px;
  gap: 16px;
  font-variation-settings: 'wght' 700;
}

/* ===== QUANTUM STATE BUTTONS ===== */

.agri-btn-success {
  background: var(--quantum-success);
  color: #ffffff;
  border: 1px solid rgba(0, 255, 136, 0.3);
  position: relative;
  overflow: hidden;
}

.agri-btn-success::before {
  content: '✓';
  position: absolute;
  top: 50%;
  left: -30px;
  transform: translateY(-50%);
  font-size: 16px;
  font-weight: bold;
  opacity: 0;
  transition: all var(--micro-normal);
}

.agri-btn-success:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 32px rgba(0, 255, 136, 0.3);
  padding-left: 48px;
}

.agri-btn-success:hover::before {
  left: 16px;
  opacity: 1;
}

.agri-btn-warning {
  background: var(--quantum-warning);
  color: #ffffff;
  border: 1px solid rgba(255, 179, 71, 0.3);
  position: relative;
  overflow: hidden;
}

.agri-btn-warning::before {
  content: '⚠';
  position: absolute;
  top: 50%;
  left: -30px;
  transform: translateY(-50%);
  font-size: 16px;
  opacity: 0;
  transition: all var(--micro-normal);
}

.agri-btn-warning:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 32px rgba(255, 179, 71, 0.3);
  padding-left: 48px;
}

.agri-btn-warning:hover::before {
  left: 16px;
  opacity: 1;
}

.agri-btn-danger {
  background: var(--quantum-danger);
  color: #ffffff;
  border: 1px solid rgba(255, 107, 107, 0.3);
  position: relative;
  overflow: hidden;
}

.agri-btn-danger::before {
  content: '✕';
  position: absolute;
  top: 50%;
  left: -30px;
  transform: translateY(-50%);
  font-size: 16px;
  font-weight: bold;
  opacity: 0;
  transition: all var(--micro-normal);
}

.agri-btn-danger:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 32px rgba(255, 107, 107, 0.3);
  padding-left: 48px;
}

.agri-btn-danger:hover::before {
  left: 16px;
  opacity: 1;
}

.agri-btn-info {
  background: linear-gradient(135deg, #00d4ff 0%, #0099cc 50%, #006699 100%);
  color: #ffffff;
  border: 1px solid rgba(0, 212, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.agri-btn-info::before {
  content: 'ℹ';
  position: absolute;
  top: 50%;
  left: -30px;
  transform: translateY(-50%);
  font-size: 16px;
  font-weight: bold;
  opacity: 0;
  transition: all var(--micro-normal);
}

.agri-btn-info:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 32px rgba(0, 212, 255, 0.3);
  padding-left: 48px;
}

.agri-btn-info:hover::before {
  left: 16px;
  opacity: 1;
}

/* ===== NEURAL BUTTON GROUPS ===== */

.agri-btn-group {
  display: inline-flex;
  border-radius: 20px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.05);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.agri-btn-group .agri-btn {
  border-radius: 0;
  border: none;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  background: transparent;
  -webkit-backdrop-filter: none;
  backdrop-filter: none;
  box-shadow: none;
}

.agri-btn-group .agri-btn:first-child {
  border-radius: 20px 0 0 20px;
}

.agri-btn-group .agri-btn:last-child {
  border-radius: 0 20px 20px 0;
  border-right: none;
}

.agri-btn-group .agri-btn:only-child {
  border-radius: 20px;
  border-right: none;
}

.agri-btn-group .agri-btn:hover {
  background: rgba(0, 245, 255, 0.1);
  transform: none;
  box-shadow: inset 0 0 20px rgba(0, 245, 255, 0.2);
}

/* ===== QUANTUM ICON BUTTONS ===== */

.agri-btn-icon {
  padding: 16px;
  min-width: 56px;
  min-height: 56px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.08);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.agri-btn-icon::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 50%;
  background: var(--quantum-primary);
  opacity: 0;
  transition: opacity var(--micro-normal);
}

.agri-btn-icon:hover {
  transform: translateY(-4px) scale(1.1);
  box-shadow:
    0 12px 40px rgba(0, 245, 255, 0.3),
    0 6px 20px rgba(0, 245, 255, 0.2);
}

.agri-btn-icon:hover::before {
  opacity: 0.1;
}

.agri-btn-icon-sm {
  padding: 12px;
  min-width: 44px;
  min-height: 44px;
  border-radius: 50%;
}

.agri-btn-icon-lg {
  padding: 20px;
  min-width: 68px;
  min-height: 68px;
  border-radius: 50%;
}

/* ===== QUANTUM FLOATING ACTION BUTTON ===== */

.agri-fab {
  position: fixed;
  bottom: 32px;
  right: 32px;
  width: 72px;
  height: 72px;
  border-radius: 50%;
  background: var(--quantum-primary);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
  font-size: 24px;
  cursor: pointer;
  z-index: 1000;
  box-shadow:
    0 8px 32px rgba(0, 245, 255, 0.3),
    0 4px 16px rgba(0, 0, 0, 0.2);
  transition: all var(--micro-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
}

.agri-fab:hover {
  transform: scale(1.1) translateY(-4px);
  box-shadow:
    0 16px 48px rgba(0, 245, 255, 0.4),
    0 8px 24px rgba(0, 0, 0, 0.3);
  filter: brightness(1.1);
}

.agri-fab:active {
  transform: scale(1.05) translateY(-2px);
}

/* ===== QUANTUM LOADING STATES ===== */

.agri-btn-loading {
  position: relative;
  color: transparent !important;
  pointer-events: none;
  overflow: hidden;
}

.agri-btn-loading::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: agri-btn-shimmer 1.5s infinite;
}

.agri-btn-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 24px;
  height: 24px;
  margin: -12px 0 0 -12px;
  border: 3px solid transparent;
  border-top: 3px solid currentColor;
  border-radius: 50%;
  animation: agri-btn-spin 1s linear infinite;
  color: rgba(255, 255, 255, 0.8);
}

.agri-fab:active {
  transform: scale(1.05);
}

/* ===== LOADING STATES ===== */

.agri-btn-loading {
  position: relative;
  color: transparent;
  pointer-events: none;
}

.agri-btn-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: var(--radius-full);
  animation: agri-btn-spin 1s linear infinite;
}

@keyframes agri-btn-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes agri-btn-shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* ===== QUANTUM RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
  .agri-btn {
    min-height: 52px;
    padding: 14px 28px;
    font-size: 15px;
  }

  .agri-btn-lg {
    padding: 18px 36px;
    font-size: 17px;
    min-height: 60px;
  }

  .agri-btn-xl {
    padding: 22px 44px;
    font-size: 19px;
    min-height: 68px;
  }

  .agri-btn-group {
    flex-direction: column;
    border-radius: 16px;
  }

  .agri-btn-group .agri-btn {
    border-radius: 0;
    border-right: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .agri-btn-group .agri-btn:first-child {
    border-radius: 16px 16px 0 0;
  }

  .agri-btn-group .agri-btn:last-child {
    border-radius: 0 0 16px 16px;
    border-bottom: none;
  }

  .agri-fab {
    bottom: 24px;
    right: 24px;
    width: 64px;
    height: 64px;
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .agri-btn {
    min-height: 48px;
    padding: 12px 24px;
    font-size: 14px;
  }

  .agri-btn-lg {
    min-height: 56px;
    padding: 16px 32px;
    font-size: 16px;
  }

  .agri-fab {
    width: 56px;
    height: 56px;
    font-size: 18px;
    bottom: 20px;
    right: 20px;
  }
}

/* ===== QUANTUM ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  .agri-btn:hover,
  .agri-fab:hover,
  .agri-btn-primary:hover,
  .agri-btn-secondary:hover,
  .agri-btn-outline:hover,
  .agri-btn-ghost:hover,
  .agri-btn-success:hover,
  .agri-btn-warning:hover,
  .agri-btn-danger:hover,
  .agri-btn-info:hover {
    transform: none;
    animation: none;
  }

  .agri-btn-loading::after,
  .agri-btn-loading::before {
    animation: none;
  }

  @keyframes agri-btn-spin {
    0%, 100% { transform: none; }
  }

  @keyframes agri-btn-shimmer {
    0%, 100% { transform: none; }
  }

  @keyframes neural-pulse {
    0%, 100% { transform: none; }
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .agri-btn {
    border: 2px solid;
    background: ButtonFace;
    color: ButtonText;
  }

  .agri-btn:hover {
    background: Highlight;
    color: HighlightText;
  }
}

/* ===== QUANTUM SPECIAL EFFECTS ===== */

/* Magnetic hover effect */
.agri-btn-magnetic {
  transition: all var(--micro-normal);
}

.agri-btn-magnetic:hover {
  transform: translateY(-6px) scale(1.03);
  box-shadow:
    0 20px 60px rgba(0, 245, 255, 0.4),
    0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Neon glow effect */
.agri-btn-neon {
  position: relative;
  overflow: visible;
}

.agri-btn-neon::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: var(--quantum-primary);
  border-radius: inherit;
  filter: blur(8px);
  opacity: 0;
  transition: opacity var(--micro-normal);
  z-index: -1;
}

.agri-btn-neon:hover::before {
  opacity: 0.7;
  animation: neon-pulse 2s infinite alternate;
}

@keyframes neon-pulse {
  0% { filter: blur(8px) brightness(1); }
  100% { filter: blur(12px) brightness(1.5); }
}

/* Holographic effect */
.agri-btn-holographic {
  background: linear-gradient(45deg, #ff0080, #ff8c00, #40e0d0, #ff0080);
  background-size: 400% 400%;
  animation: holographic-shift 3s ease infinite;
}

@keyframes holographic-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .agri-btn-primary,
  .agri-btn-secondary {
    background: var(--color-text-primary);
    color: var(--color-background-primary);
    border: 2px solid var(--color-text-primary);
  }
  
  .agri-btn-outline {
    border: 2px solid var(--color-text-primary);
  }
  
  .agri-btn-ghost,
  .agri-btn-text {
    border: 1px solid var(--color-text-primary);
  }
}
