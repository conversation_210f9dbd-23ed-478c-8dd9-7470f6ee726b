import React from 'react';
import { Box, CircularProgress, Typography } from '@mui/material';

const SimpleLoadingFallback: React.FC = () => {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%)',
        color: 'white',
        gap: 2
      }}
    >
      <CircularProgress 
        size={60} 
        sx={{ 
          color: 'white',
          '& .MuiCircularProgress-circle': {
            strokeLinecap: 'round',
          }
        }} 
      />
      <Typography 
        variant="h6" 
        sx={{ 
          fontWeight: 600,
          textAlign: 'center',
          opacity: 0.9
        }}
      >
        Loading AgriIntel...
      </Typography>
      <Typography 
        variant="body2" 
        sx={{ 
          textAlign: 'center',
          opacity: 0.7,
          maxWidth: 300
        }}
      >
        Smart Livestock Management for South African Farmers
      </Typography>
    </Box>
  );
};

export default SimpleLoadingFallback;
