// Test script to identify problematic route files
const path = require('path');

const routeFiles = [
  './src/routes/api/animals',
  './src/routes/api/auth',
  './src/routes/api/users',
  './src/routes/api/health',
  './src/routes/api/breeding',
  './src/routes/api/financial',
  './src/routes/api/inventory',
  './src/routes/api/business',
  './src/routes/api/reports',
  './src/routes/api/resources',
  './src/routes/api/feeding',
  './src/routes/api/compliance',
  './src/routes/api/subscription'
];

async function testRouteFile(routeFile) {
  try {
    console.log(`Testing ${routeFile}...`);
    const route = require(routeFile);
    console.log(`✅ ${routeFile} loaded successfully`);
    return true;
  } catch (error) {
    console.error(`❌ Error loading ${routeFile}:`, error.message);
    return false;
  }
}

async function testAllRoutes() {
  console.log('Testing individual route files...\n');
  
  let allPassed = true;
  
  for (const routeFile of routeFiles) {
    const passed = await testRouteFile(routeFile);
    if (!passed) {
      allPassed = false;
    }
    console.log(''); // Empty line for readability
  }
  
  if (allPassed) {
    console.log('✅ All route files loaded successfully!');
  } else {
    console.log('❌ Some route files failed to load.');
  }
}

// Run the test
testAllRoutes()
  .then(() => {
    console.log('Route testing completed');
  })
  .catch((error) => {
    console.error('Error in route testing:', error);
  });
