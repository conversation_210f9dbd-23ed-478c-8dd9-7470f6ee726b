/**
 * Spacing Utilities - AgriIntel
 * Consistent spacing system for the application
 */

/* Margin Utilities */
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-xs); }
.m-2 { margin: var(--spacing-sm); }
.m-3 { margin: var(--spacing-md); }
.m-4 { margin: var(--spacing-lg); }
.m-5 { margin: var(--spacing-xl); }
.m-6 { margin: var(--spacing-2xl); }
.m-8 { margin: var(--spacing-3xl); }
.m-10 { margin: var(--spacing-4xl); }
.m-auto { margin: auto; }

/* Margin Top */
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }
.mt-6 { margin-top: var(--spacing-2xl); }
.mt-8 { margin-top: var(--spacing-3xl); }
.mt-10 { margin-top: var(--spacing-4xl); }
.mt-auto { margin-top: auto; }

/* Margin Right */
.mr-0 { margin-right: 0; }
.mr-1 { margin-right: var(--spacing-xs); }
.mr-2 { margin-right: var(--spacing-sm); }
.mr-3 { margin-right: var(--spacing-md); }
.mr-4 { margin-right: var(--spacing-lg); }
.mr-5 { margin-right: var(--spacing-xl); }
.mr-6 { margin-right: var(--spacing-2xl); }
.mr-8 { margin-right: var(--spacing-3xl); }
.mr-10 { margin-right: var(--spacing-4xl); }
.mr-auto { margin-right: auto; }

/* Margin Bottom */
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }
.mb-6 { margin-bottom: var(--spacing-2xl); }
.mb-8 { margin-bottom: var(--spacing-3xl); }
.mb-10 { margin-bottom: var(--spacing-4xl); }
.mb-auto { margin-bottom: auto; }

/* Margin Left */
.ml-0 { margin-left: 0; }
.ml-1 { margin-left: var(--spacing-xs); }
.ml-2 { margin-left: var(--spacing-sm); }
.ml-3 { margin-left: var(--spacing-md); }
.ml-4 { margin-left: var(--spacing-lg); }
.ml-5 { margin-left: var(--spacing-xl); }
.ml-6 { margin-left: var(--spacing-2xl); }
.ml-8 { margin-left: var(--spacing-3xl); }
.ml-10 { margin-left: var(--spacing-4xl); }
.ml-auto { margin-left: auto; }

/* Margin X (horizontal) */
.mx-0 { margin-left: 0; margin-right: 0; }
.mx-1 { margin-left: var(--spacing-xs); margin-right: var(--spacing-xs); }
.mx-2 { margin-left: var(--spacing-sm); margin-right: var(--spacing-sm); }
.mx-3 { margin-left: var(--spacing-md); margin-right: var(--spacing-md); }
.mx-4 { margin-left: var(--spacing-lg); margin-right: var(--spacing-lg); }
.mx-5 { margin-left: var(--spacing-xl); margin-right: var(--spacing-xl); }
.mx-6 { margin-left: var(--spacing-2xl); margin-right: var(--spacing-2xl); }
.mx-8 { margin-left: var(--spacing-3xl); margin-right: var(--spacing-3xl); }
.mx-10 { margin-left: var(--spacing-4xl); margin-right: var(--spacing-4xl); }
.mx-auto { margin-left: auto; margin-right: auto; }

/* Margin Y (vertical) */
.my-0 { margin-top: 0; margin-bottom: 0; }
.my-1 { margin-top: var(--spacing-xs); margin-bottom: var(--spacing-xs); }
.my-2 { margin-top: var(--spacing-sm); margin-bottom: var(--spacing-sm); }
.my-3 { margin-top: var(--spacing-md); margin-bottom: var(--spacing-md); }
.my-4 { margin-top: var(--spacing-lg); margin-bottom: var(--spacing-lg); }
.my-5 { margin-top: var(--spacing-xl); margin-bottom: var(--spacing-xl); }
.my-6 { margin-top: var(--spacing-2xl); margin-bottom: var(--spacing-2xl); }
.my-8 { margin-top: var(--spacing-3xl); margin-bottom: var(--spacing-3xl); }
.my-10 { margin-top: var(--spacing-4xl); margin-bottom: var(--spacing-4xl); }
.my-auto { margin-top: auto; margin-bottom: auto; }

/* Padding Utilities */
.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }
.p-6 { padding: var(--spacing-2xl); }
.p-8 { padding: var(--spacing-3xl); }
.p-10 { padding: var(--spacing-4xl); }

/* Padding Top */
.pt-0 { padding-top: 0; }
.pt-1 { padding-top: var(--spacing-xs); }
.pt-2 { padding-top: var(--spacing-sm); }
.pt-3 { padding-top: var(--spacing-md); }
.pt-4 { padding-top: var(--spacing-lg); }
.pt-5 { padding-top: var(--spacing-xl); }
.pt-6 { padding-top: var(--spacing-2xl); }
.pt-8 { padding-top: var(--spacing-3xl); }
.pt-10 { padding-top: var(--spacing-4xl); }

/* Padding Right */
.pr-0 { padding-right: 0; }
.pr-1 { padding-right: var(--spacing-xs); }
.pr-2 { padding-right: var(--spacing-sm); }
.pr-3 { padding-right: var(--spacing-md); }
.pr-4 { padding-right: var(--spacing-lg); }
.pr-5 { padding-right: var(--spacing-xl); }
.pr-6 { padding-right: var(--spacing-2xl); }
.pr-8 { padding-right: var(--spacing-3xl); }
.pr-10 { padding-right: var(--spacing-4xl); }

/* Padding Bottom */
.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: var(--spacing-xs); }
.pb-2 { padding-bottom: var(--spacing-sm); }
.pb-3 { padding-bottom: var(--spacing-md); }
.pb-4 { padding-bottom: var(--spacing-lg); }
.pb-5 { padding-bottom: var(--spacing-xl); }
.pb-6 { padding-bottom: var(--spacing-2xl); }
.pb-8 { padding-bottom: var(--spacing-3xl); }
.pb-10 { padding-bottom: var(--spacing-4xl); }

/* Padding Left */
.pl-0 { padding-left: 0; }
.pl-1 { padding-left: var(--spacing-xs); }
.pl-2 { padding-left: var(--spacing-sm); }
.pl-3 { padding-left: var(--spacing-md); }
.pl-4 { padding-left: var(--spacing-lg); }
.pl-5 { padding-left: var(--spacing-xl); }
.pl-6 { padding-left: var(--spacing-2xl); }
.pl-8 { padding-left: var(--spacing-3xl); }
.pl-10 { padding-left: var(--spacing-4xl); }

/* Padding X (horizontal) */
.px-0 { padding-left: 0; padding-right: 0; }
.px-1 { padding-left: var(--spacing-xs); padding-right: var(--spacing-xs); }
.px-2 { padding-left: var(--spacing-sm); padding-right: var(--spacing-sm); }
.px-3 { padding-left: var(--spacing-md); padding-right: var(--spacing-md); }
.px-4 { padding-left: var(--spacing-lg); padding-right: var(--spacing-lg); }
.px-5 { padding-left: var(--spacing-xl); padding-right: var(--spacing-xl); }
.px-6 { padding-left: var(--spacing-2xl); padding-right: var(--spacing-2xl); }
.px-8 { padding-left: var(--spacing-3xl); padding-right: var(--spacing-3xl); }
.px-10 { padding-left: var(--spacing-4xl); padding-right: var(--spacing-4xl); }

/* Padding Y (vertical) */
.py-0 { padding-top: 0; padding-bottom: 0; }
.py-1 { padding-top: var(--spacing-xs); padding-bottom: var(--spacing-xs); }
.py-2 { padding-top: var(--spacing-sm); padding-bottom: var(--spacing-sm); }
.py-3 { padding-top: var(--spacing-md); padding-bottom: var(--spacing-md); }
.py-4 { padding-top: var(--spacing-lg); padding-bottom: var(--spacing-lg); }
.py-5 { padding-top: var(--spacing-xl); padding-bottom: var(--spacing-xl); }
.py-6 { padding-top: var(--spacing-2xl); padding-bottom: var(--spacing-2xl); }
.py-8 { padding-top: var(--spacing-3xl); padding-bottom: var(--spacing-3xl); }
.py-10 { padding-top: var(--spacing-4xl); padding-bottom: var(--spacing-4xl); }

/* Gap Utilities for Flexbox and Grid */
.gap-0 { gap: 0; }
.gap-1 { gap: var(--spacing-xs); }
.gap-2 { gap: var(--spacing-sm); }
.gap-3 { gap: var(--spacing-md); }
.gap-4 { gap: var(--spacing-lg); }
.gap-5 { gap: var(--spacing-xl); }
.gap-6 { gap: var(--spacing-2xl); }
.gap-8 { gap: var(--spacing-3xl); }
.gap-10 { gap: var(--spacing-4xl); }

/* Row Gap */
.row-gap-0 { row-gap: 0; }
.row-gap-1 { row-gap: var(--spacing-xs); }
.row-gap-2 { row-gap: var(--spacing-sm); }
.row-gap-3 { row-gap: var(--spacing-md); }
.row-gap-4 { row-gap: var(--spacing-lg); }
.row-gap-5 { row-gap: var(--spacing-xl); }
.row-gap-6 { row-gap: var(--spacing-2xl); }
.row-gap-8 { row-gap: var(--spacing-3xl); }
.row-gap-10 { row-gap: var(--spacing-4xl); }

/* Column Gap */
.col-gap-0 { column-gap: 0; }
.col-gap-1 { column-gap: var(--spacing-xs); }
.col-gap-2 { column-gap: var(--spacing-sm); }
.col-gap-3 { column-gap: var(--spacing-md); }
.col-gap-4 { column-gap: var(--spacing-lg); }
.col-gap-5 { column-gap: var(--spacing-xl); }
.col-gap-6 { column-gap: var(--spacing-2xl); }
.col-gap-8 { column-gap: var(--spacing-3xl); }
.col-gap-10 { column-gap: var(--spacing-4xl); }
