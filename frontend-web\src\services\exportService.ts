import axios from 'axios';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import jsPDF from 'jspdf';
import 'jspdf-autotable';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

export const exportDataFromAPI = async (entityType: string, format: 'csv' | 'xlsx' | 'pdf', filters = {}) => {
  try {
    const response = await axios({
      url: `${API_URL}/batch/export`,
      method: 'POST',
      responseType: 'blob',
      params: { entityType, format },
      data: { filters },
    });

    const contentDisposition = response.headers['content-disposition'];
    let filename = `${entityType}_export.${format}`;

    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch.length === 2) filename = filenameMatch[1];
    }

    saveAs(new Blob([response.data]), filename);
    return true;
  } catch (error) {
    console.error('Export failed:', error);
    return false;
  }
};

/**
 * Export analytics data to Excel format
 */
export const exportAnalyticsData = async (data: any, timeRange: string) => {
  try {
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `agriintel-analytics-${timeRange}-${timestamp}.xlsx`;

    const workbook = XLSX.utils.book_new();

    // Create summary sheet
    const summaryData = [
      { Metric: 'Total Animals', Value: data.totalAnimals },
      { Metric: 'Healthy Animals', Value: data.healthyAnimals },
      { Metric: 'Pregnant Animals', Value: data.pregnantAnimals },
      { Metric: 'Revenue', Value: data.revenue },
      { Metric: 'Expenses', Value: data.expenses },
      { Metric: 'Profit', Value: data.profit },
      { Metric: 'Animals Trend', Value: `${data.trends.animals}%` },
      { Metric: 'Health Trend', Value: `${data.trends.health}%` },
      { Metric: 'Breeding Trend', Value: `${data.trends.breeding}%` },
      { Metric: 'Financial Trend', Value: `${data.trends.financial}%` }
    ];

    const summarySheet = XLSX.utils.json_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');

    // Add monthly data sheet
    if (data.chartData?.monthly) {
      const monthlySheet = XLSX.utils.json_to_sheet(data.chartData.monthly);
      XLSX.utils.book_append_sheet(workbook, monthlySheet, 'Monthly Data');
    }

    // Add species data sheet
    if (data.chartData?.species) {
      const speciesSheet = XLSX.utils.json_to_sheet(data.chartData.species);
      XLSX.utils.book_append_sheet(workbook, speciesSheet, 'Species Distribution');
    }

    // Add health data sheet
    if (data.chartData?.health) {
      const healthSheet = XLSX.utils.json_to_sheet(data.chartData.health);
      XLSX.utils.book_append_sheet(workbook, healthSheet, 'Health Status');
    }

    // Add financial data sheet
    if (data.chartData?.financial) {
      const financialSheet = XLSX.utils.json_to_sheet(data.chartData.financial);
      XLSX.utils.book_append_sheet(workbook, financialSheet, 'Financial Breakdown');
    }

    // Save the file
    XLSX.writeFile(workbook, filename);

    return true;
  } catch (error) {
    console.error('Analytics export failed:', error);
    return false;
  }
};

/**
 * Export animal data to Excel
 */
export const exportAnimalData = async (animals: any[]) => {
  try {
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `agriintel-animals-${timestamp}.xlsx`;

    const exportData = animals.map(animal => ({
      'Tag Number': animal.tagNumber,
      'Name': animal.name || '',
      'Species': animal.type || '',
      'Breed': animal.breed,
      'Gender': animal.gender,
      'Date of Birth': animal.birthDate ? new Date(animal.birthDate).toLocaleDateString() : '',
      'Age': '', // Age calculation would need to be done separately
      'Weight (kg)': animal.weight || '',
      'Status': animal.status,
      'Health Status': animal.healthStatus || 'Unknown',
      'Location': animal.location || '',
      'Created Date': '' // createdAt not available on Animal type
    }));

    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(exportData);
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Animals');
    XLSX.writeFile(workbook, filename);

    return true;
  } catch (error) {
    console.error('Animal data export failed:', error);
    return false;
  }
};

/**
 * Import data from Excel/CSV file
 */
export const importDataFromFile = async (file: File): Promise<any[]> => {
  try {
    const fileExtension = file.name.split('.').pop()?.toLowerCase();

    if (fileExtension === 'csv') {
      return await importFromCSV(file);
    } else if (fileExtension === 'xlsx' || fileExtension === 'xls') {
      return await importFromExcel(file);
    } else {
      throw new Error('Unsupported file format. Please use CSV or Excel files.');
    }
  } catch (error) {
    console.error('Import failed:', error);
    throw error;
  }
};

/**
 * Import from CSV file
 */
const importFromCSV = async (file: File): Promise<any[]> => {
  const text = await file.text();
  const lines = text.split('\n');
  const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));

  return lines.slice(1)
    .filter(line => line.trim())
    .map(line => {
      const values = line.split(',').map(v => v.trim().replace(/"/g, ''));
      const obj: any = {};
      headers.forEach((header, index) => {
        obj[header] = values[index] || '';
      });
      return obj;
    });
};

/**
 * Import from Excel file
 */
const importFromExcel = async (file: File): Promise<any[]> => {
  const buffer = await file.arrayBuffer();
  const workbook = XLSX.read(buffer, { type: 'buffer' });
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];
  return XLSX.utils.sheet_to_json(worksheet);
};