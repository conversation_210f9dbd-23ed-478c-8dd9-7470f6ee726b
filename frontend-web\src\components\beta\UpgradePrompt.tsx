import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ack,
  Chip,
  useTheme,
  alpha
} from '@mui/material';
import {
  Lock,
  Star,
  Upgrade,
  CheckCircle
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';

interface UpgradePromptProps {
  moduleName: string;
  features: string[];
  tier: 'Professional' | 'Enterprise';
  price: string;
}

const UpgradePrompt: React.FC<UpgradePromptProps> = ({
  moduleName,
  features,
  tier,
  price
}) => {
  const theme = useTheme();
  const navigate = useNavigate();

  const handleUpgrade = () => {
    navigate('/login'); // Redirect to live login for upgrade
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
        p: 2
      }}
    >
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Card
          sx={{
            maxWidth: 500,
            width: '100%',
            textAlign: 'center',
            background: alpha('#fff', 0.95),
            backdropFilter: 'blur(20px)',
            border: `2px solid ${alpha(theme.palette.warning.main, 0.3)}`,
            boxShadow: theme.shadows[8]
          }}
        >
          <CardContent sx={{ p: 4 }}>
            <Box
              sx={{
                width: 80,
                height: 80,
                borderRadius: '50%',
                background: `linear-gradient(135deg, ${theme.palette.warning.main}, ${theme.palette.warning.dark})`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mx: 'auto',
                mb: 3
              }}
            >
              <Lock sx={{ fontSize: 40, color: 'white' }} />
            </Box>

            <Typography variant="h4" fontWeight="bold" gutterBottom>
              {moduleName} Module
            </Typography>

            <Chip
              label="Premium Feature"
              color="warning"
              icon={<Star />}
              sx={{ mb: 3, fontWeight: 'bold' }}
            />

            <Typography variant="h6" color="text.secondary" paragraph>
              Unlock advanced {moduleName.toLowerCase()} features with {tier}
            </Typography>

            <Typography variant="h5" color="primary" fontWeight="bold" sx={{ mb: 3 }}>
              {price}
            </Typography>

            <Stack spacing={2} sx={{ mb: 4 }}>
              <Typography variant="subtitle1" fontWeight="bold">
                What you'll get:
              </Typography>
              {features.map((feature, index) => (
                <Box
                  key={index}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    textAlign: 'left'
                  }}
                >
                  <CheckCircle sx={{ color: theme.palette.success.main, fontSize: 20 }} />
                  <Typography variant="body2">{feature}</Typography>
                </Box>
              ))}
            </Stack>

            <Stack direction="row" spacing={2} justifyContent="center">
              <Button
                variant="contained"
                size="large"
                startIcon={<Upgrade />}
                onClick={handleUpgrade}
                sx={{
                  background: `linear-gradient(135deg, ${theme.palette.warning.main}, ${theme.palette.warning.dark})`,
                  px: 4,
                  py: 1.5,
                  fontSize: '1.1rem',
                  fontWeight: 'bold',
                  '&:hover': {
                    background: `linear-gradient(135deg, ${theme.palette.warning.dark}, ${theme.palette.warning.main})`
                  }
                }}
              >
                Upgrade Now
              </Button>
              
              <Button
                variant="outlined"
                size="large"
                onClick={() => navigate('/beta-dashboard')}
                sx={{ px: 3, py: 1.5 }}
              >
                Back to Dashboard
              </Button>
            </Stack>
          </CardContent>
        </Card>
      </motion.div>
    </Box>
  );
};

export default UpgradePrompt;
