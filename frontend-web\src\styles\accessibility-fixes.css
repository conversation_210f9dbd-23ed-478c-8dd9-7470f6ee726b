/* Accessibility Fixes CSS */

/* Screen reader only content */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Form accessibility improvements */
select[aria-label] {
  /* Ensure select elements with aria-label are properly styled */
  border: 1px solid #ccc;
  padding: 0.5rem;
  border-radius: 4px;
  background-color: white;
  color: #333;
}

select[aria-label]:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
  border-color: #007bff;
}

/* Button accessibility improvements */
button[type="button"] {
  cursor: pointer;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  background-color: #007bff;
  color: white;
  transition: all 0.3s ease;
}

button[type="button"]:hover {
  background-color: #0056b3;
  transform: translateY(-1px);
}

button[type="button"]:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

button[type="button"]:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  transform: none;
}

/* Link accessibility improvements */
a[href] {
  color: #007bff;
  text-decoration: none;
  transition: color 0.3s ease;
}

a[href]:hover {
  color: #0056b3;
  text-decoration: underline;
}

a[href]:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Input accessibility improvements */
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="password"],
textarea {
  border: 1px solid #ccc;
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="tel"]:focus,
input[type="password"]:focus,
textarea:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
  border-color: #007bff;
}

/* Form group styling */
.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.25rem;
  font-weight: 600;
  color: #333;
}

/* Contact form specific styles */
.contact-submit {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.contact-submit:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.contact-submit:focus {
  outline: 2px solid #28a745;
  outline-offset: 2px;
}

/* Navigation accessibility */
.nav-tab {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-tab:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.nav-tab:focus {
  outline: 2px solid white;
  outline-offset: 2px;
}

.nav-tab.active {
  background: rgba(255, 255, 255, 0.2);
  border-color: white;
}

/* Footer accessibility */
.footer-section button {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  text-decoration: underline;
  padding: 0;
  font-size: inherit;
}

.footer-section button:hover {
  opacity: 0.8;
}

.footer-section button:focus {
  outline: 2px solid currentColor;
  outline-offset: 2px;
}

/* Loading states */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  flex-direction: column;
  gap: 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error states */
.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  margin: 1rem 0;
}

.success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 1rem;
  border: 1px solid #c3e6cb;
  border-radius: 4px;
  margin: 1rem 0;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  button, select, input, textarea {
    border-width: 2px;
  }
  
  .nav-tab {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus visible support for better keyboard navigation */
@supports selector(:focus-visible) {
  button:focus:not(:focus-visible),
  select:focus:not(:focus-visible),
  input:focus:not(:focus-visible),
  textarea:focus:not(:focus-visible),
  a:focus:not(:focus-visible) {
    outline: none;
  }
}

/* Print styles */
@media print {
  .nav-tab,
  .contact-submit,
  button {
    background: white !important;
    color: black !important;
    border: 1px solid black !important;
  }
}
