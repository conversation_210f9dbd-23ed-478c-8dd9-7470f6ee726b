import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  IconButton
} from '@mui/material';
import {
  Close as CloseIcon,
  Star as StarIcon,
  TrendingUp as TrendingUpIcon,
  Psychology as PsychologyIcon,
  AutoAwesome as AutoAwesomeIcon,
  Rocket as RocketIcon
} from '@mui/icons-material';

interface UpgradePromptProps {
  open: boolean;
  onClose: () => void;
  feature: string;
  module: string;
}

const UpgradePrompt: React.FC<UpgradePromptProps> = ({
  open,
  onClose,
  feature,
  module
}) => {
  const premiumFeatures = [
    { icon: <PsychologyIcon />, text: 'AI-Powered Analytics & Predictions' },
    { icon: <AutoAwesomeIcon />, text: 'Automated Task Management' },
    { icon: <TrendingUpIcon />, text: 'Advanced Business Intelligence' },
    { icon: <RocketIcon />, text: 'Unlimited Animals & Data Storage' },
    { icon: <StarIcon />, text: 'Priority Support & Training' }
  ];

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          background: 'var(--bio-glass-strong)',
          backdropFilter: 'var(--bio-backdrop-intense)',
          WebkitBackdropFilter: 'var(--bio-backdrop-intense)',
          border: '1px solid var(--bio-border-medium)',
          borderRadius: 'var(--radius-consciousness)',
          boxShadow: 'var(--shadow-consciousness)',
          color: 'var(--quantum-text)',
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            inset: 0,
            background: 'var(--neural-consciousness)',
            opacity: 0.03,
            borderRadius: 'inherit',
            animation: 'holographic-consciousness 12s ease infinite',
          }
        }
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          pb: 2,
          position: 'relative',
          zIndex: 1
        }}
      >
        <Box display="flex" alignItems="center" gap={2}>
          <Box
            sx={{
              width: 56,
              height: 56,
              borderRadius: 'var(--radius-consciousness)',
              background: 'var(--neural-consciousness)',
              backgroundSize: '400% 400%',
              animation: 'holographic-consciousness 8s ease infinite',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: '2px solid var(--bio-border-medium)',
            }}
          >
            <PsychologyIcon sx={{ fontSize: 28, color: 'white' }} />
          </Box>
          <Box>
            <Typography
              variant="h5"
              sx={{
                fontFamily: 'var(--font-quantum)',
                fontWeight: 800,
                background: 'var(--neural-consciousness)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
                backgroundSize: '400% 400%',
                animation: 'holographic-consciousness 8s ease infinite',
              }}
            >
              Unlock Neural Agriculture
            </Typography>
            <Typography
              variant="subtitle1"
              sx={{
                color: 'var(--quantum-text-secondary)',
                fontFamily: 'var(--font-quantum)',
                fontWeight: 500,
              }}
            >
              Upgrade to Professional V1
            </Typography>
          </Box>
        </Box>
        <IconButton
          onClick={onClose}
          sx={{
            color: 'var(--quantum-text-secondary)',
            '&:hover': {
              background: 'var(--bio-glass-light)',
              color: 'var(--quantum-text)',
            }
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ position: 'relative', zIndex: 1 }}>
        <Box mb={3}>
          <Chip
            label={`🔒 ${feature} - Premium Feature`}
            sx={{
              background: 'var(--neural-warning)',
              color: 'white',
              fontFamily: 'var(--font-quantum)',
              fontWeight: 600,
              mb: 2,
              fontSize: '0.9rem',
            }}
          />
          <Typography
            variant="body1"
            sx={{
              color: 'var(--quantum-text)',
              fontFamily: 'var(--font-quantum)',
              lineHeight: 1.6,
              mb: 2,
            }}
          >
            You're trying to access <strong>{feature}</strong> in the <strong>{module}</strong> module. 
            This advanced feature is available in our Professional V1 plan with AI-powered automation and unlimited capabilities.
          </Typography>
        </Box>

        <Divider sx={{ borderColor: 'var(--bio-border-light)', mb: 3 }} />

        <Box mb={3}>
          <Typography
            variant="h6"
            sx={{
              fontFamily: 'var(--font-quantum)',
              fontWeight: 700,
              color: 'var(--quantum-text)',
              mb: 2,
            }}
          >
            🚀 Professional V1 Features
          </Typography>
          <List dense>
            {premiumFeatures.map((feature, index) => (
              <ListItem key={index} sx={{ py: 0.5 }}>
                <ListItemIcon sx={{ minWidth: 40, color: '#00ff88' }}>
                  {feature.icon}
                </ListItemIcon>
                <ListItemText
                  primary={feature.text}
                  sx={{
                    '& .MuiListItemText-primary': {
                      fontFamily: 'var(--font-quantum)',
                      fontWeight: 500,
                      color: 'var(--quantum-text)',
                    }
                  }}
                />
              </ListItem>
            ))}
          </List>
        </Box>

        <Box
          sx={{
            background: 'var(--bio-glass-medium)',
            border: '1px solid var(--bio-border-light)',
            borderRadius: 'var(--radius-lg)',
            p: 3,
            textAlign: 'center',
          }}
        >
          <Typography
            variant="h4"
            sx={{
              fontFamily: 'var(--font-quantum)',
              fontWeight: 800,
              background: 'var(--neural-success)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
              mb: 1,
            }}
          >
            R699/month
          </Typography>
          <Typography
            variant="body2"
            sx={{
              color: 'var(--quantum-text-secondary)',
              fontFamily: 'var(--font-quantum)',
            }}
          >
            Professional V1 • Neural Agriculture • Quantum Intelligence
          </Typography>
        </Box>
      </DialogContent>

      <DialogActions
        sx={{
          p: 3,
          gap: 2,
          position: 'relative',
          zIndex: 1,
        }}
      >
        <Button
          onClick={onClose}
          variant="outlined"
          className="agri-btn agri-btn-outline"
          sx={{
            fontFamily: 'var(--font-quantum)',
            fontWeight: 600,
            textTransform: 'none',
          }}
        >
          Continue with BETA
        </Button>
        <Button
          variant="contained"
          className="agri-btn agri-btn-primary"
          sx={{
            fontFamily: 'var(--font-quantum)',
            fontWeight: 700,
            textTransform: 'none',
            background: 'var(--neural-success)',
            '&:hover': {
              background: 'var(--neural-success)',
              filter: 'brightness(1.1)',
            }
          }}
        >
          🚀 Upgrade to Professional
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default UpgradePrompt;
