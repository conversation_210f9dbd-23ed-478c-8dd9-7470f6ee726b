/**
 * Accessibility Styles
 * WCAG 2.1 AA compliant styles for inclusive design
 */

/* ===== FOCUS MANAGEMENT ===== */

/* Remove default focus outline and add custom focus styles */
*:focus {
  outline: none;
}

/* Custom focus styles for interactive elements */
button:focus,
input:focus,
textarea:focus,
select:focus,
a:focus,
[tabindex]:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px var(--color-focus-ring);
}

/* Focus styles for custom components */
.agri-btn:focus,
.agri-card:focus,
.agri-form-input:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px var(--color-focus-ring);
}

/* Focus-visible for better keyboard navigation */
button:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible,
a:focus-visible,
[tabindex]:focus-visible {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px var(--color-focus-ring);
}

/* ===== SCREEN READER SUPPORT ===== */

/* Screen reader only content */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Screen reader only content that becomes visible on focus */
.sr-only-focusable:focus {
  position: static !important;
  width: auto !important;
  height: auto !important;
  padding: inherit !important;
  margin: inherit !important;
  overflow: visible !important;
  clip: auto !important;
  white-space: normal !important;
}

/* ===== HIGH CONTRAST MODE ===== */

@media (prefers-contrast: high) {
  /* Ensure sufficient contrast in high contrast mode */
  * {
    border-color: ButtonText !important;
    color: ButtonText !important;
  }
  
  button,
  input,
  textarea,
  select {
    background: ButtonFace !important;
    border: 1px solid ButtonText !important;
  }
  
  a {
    color: LinkText !important;
  }
  
  a:visited {
    color: VisitedText !important;
  }
  
  /* Remove background images in high contrast mode */
  .agri-card,
  .landing-background,
  .hero-section {
    background-image: none !important;
  }
  
  /* Ensure glassmorphism effects don't interfere */
  .glass-card,
  .agri-card {
    background: ButtonFace !important;
    -webkit-backdrop-filter: none !important;
    backdrop-filter: none !important;
    border: 2px solid ButtonText !important;
  }
}

/* ===== REDUCED MOTION ===== */

@media (prefers-reduced-motion: reduce) {
  /* Disable animations and transitions for users who prefer reduced motion */
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  /* Remove transform animations */
  .agri-btn:hover,
  .agri-card:hover {
    transform: none !important;
  }
}

/* ===== COLOR CONTRAST ===== */

/* Ensure minimum color contrast ratios */
.text-low-contrast {
  color: var(--color-text-secondary);
}

.text-high-contrast {
  color: var(--color-text-primary);
}

/* Error states with sufficient contrast */
.error-text {
  color: var(--color-error);
  font-weight: var(--font-weight-medium);
}

.success-text {
  color: var(--color-success);
  font-weight: var(--font-weight-medium);
}

.warning-text {
  color: var(--color-warning);
  font-weight: var(--font-weight-medium);
}

/* ===== KEYBOARD NAVIGATION ===== */

/* Skip links for keyboard navigation */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-background-primary);
  color: var(--color-text-primary);
  padding: 8px;
  text-decoration: none;
  border-radius: var(--radius-md);
  border: 2px solid var(--color-focus);
  z-index: var(--z-navigation);
}

.skip-link:focus {
  top: 6px;
}

/* Ensure interactive elements are large enough */
button,
input,
textarea,
select,
a {
  min-height: 44px;
  min-width: 44px;
}

/* Exception for inline links */
a:not(.agri-btn) {
  min-height: unset;
  min-width: unset;
}

/* ===== FORM ACCESSIBILITY ===== */

/* Required field indicators */
.required::after {
  content: " *";
  color: var(--color-error);
  font-weight: var(--font-weight-bold);
}

/* Error states for form fields */
.form-field-error {
  border-color: var(--color-error) !important;
  box-shadow: 0 0 0 2px rgba(211, 47, 47, 0.2) !important;
}

.form-error-message {
  color: var(--color-error);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-1);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

/* Success states for form fields */
.form-field-success {
  border-color: var(--color-success) !important;
  box-shadow: 0 0 0 2px rgba(46, 125, 50, 0.2) !important;
}

.form-success-message {
  color: var(--color-success);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-1);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

/* ===== LOADING STATES ===== */

/* Loading indicators for screen readers */
.loading-indicator {
  position: relative;
}

.loading-indicator::after {
  content: "Loading...";
  position: absolute;
  left: -9999px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* ===== ARIA LIVE REGIONS ===== */

/* Live regions for dynamic content */
.live-region {
  position: absolute;
  left: -9999px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

.live-region[aria-live="polite"] {
  /* Polite announcements */
}

.live-region[aria-live="assertive"] {
  /* Assertive announcements */
}

/* ===== PRINT STYLES ===== */

@media print {
  /* Ensure good contrast for printing */
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  /* Hide non-essential elements when printing */
  .no-print,
  nav,
  .sidebar,
  button:not(.print-button) {
    display: none !important;
  }
  
  /* Ensure links are visible when printed */
  a,
  a:visited {
    text-decoration: underline;
  }
  
  /* Show URLs for external links */
  a[href^="http"]:after {
    content: " (" attr(href) ")";
  }
  
  /* Page break controls */
  .page-break-before {
    page-break-before: always;
  }
  
  .page-break-after {
    page-break-after: always;
  }
  
  .page-break-inside-avoid {
    page-break-inside: avoid;
  }
}

/* ===== TOUCH ACCESSIBILITY ===== */

@media (pointer: coarse) {
  /* Larger touch targets for touch devices */
  button,
  input,
  textarea,
  select,
  a.agri-btn {
    min-height: 48px;
    min-width: 48px;
    padding: var(--spacing-3) var(--spacing-4);
  }
}

/* ===== LANGUAGE SUPPORT ===== */

/* Right-to-left language support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .agri-flex {
  flex-direction: row-reverse;
}

[dir="rtl"] .agri-nav-brand {
  margin-left: auto;
  margin-right: 0;
}

/* ===== ERROR HANDLING ===== */

/* Graceful degradation for unsupported features */
@supports not (backdrop-filter: blur(10px)) {
  .glass-card,
  .agri-card {
    background: var(--color-background-primary) !important;
    border: 1px solid var(--color-border) !important;
  }
}

@supports not (display: grid) {
  .agri-grid {
    display: flex;
    flex-wrap: wrap;
  }
  
  .agri-grid > * {
    flex: 1;
    min-width: 250px;
  }
}
