/* Global AgriIntel Theme System */

:root {
  /* Primary Colors - Deep Blues and Emerald Greens */
  --primary-50: #e8f5e8;
  --primary-100: #c8e6c8;
  --primary-200: #a5d6a5;
  --primary-300: #81c784;
  --primary-400: #66bb6a;
  --primary-500: #4caf50;
  --primary-600: #43a047;
  --primary-700: #388e3c;
  --primary-800: #2e7d32;
  --primary-900: #1b5e20;

  /* Secondary Colors - Warm Golds */
  --secondary-50: #fff8e1;
  --secondary-100: #ffecb3;
  --secondary-200: #ffe082;
  --secondary-300: #ffd54f;
  --secondary-400: #ffca28;
  --secondary-500: #ffc107;
  --secondary-600: #ffb300;
  --secondary-700: #ffa000;
  --secondary-800: #ff8f00;
  --secondary-900: #ff6f00;

  /* Accent Colors - Industrial Yellow and Dull Green Blend */
  --accent-50: #f9fbe7;
  --accent-100: #f0f4c3;
  --accent-200: #e6ee9c;
  --accent-300: #dce775;
  --accent-400: #d4e157;
  --accent-500: #cddc39;
  --accent-600: #c0ca33;
  --accent-700: #afb42b;
  --accent-800: #9e9d24;
  --accent-900: #827717;

  /* Neutral Colors */
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #eeeeee;
  --neutral-300: #e0e0e0;
  --neutral-400: #bdbdbd;
  --neutral-500: #9e9e9e;
  --neutral-600: #757575;
  --neutral-700: #616161;
  --neutral-800: #424242;
  --neutral-900: #212121;

  /* Gradient Definitions */
  --gradient-primary: linear-gradient(135deg, var(--primary-800) 0%, var(--primary-500) 25%, var(--accent-400) 50%, var(--primary-600) 75%, var(--primary-700) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--secondary-700) 0%, var(--secondary-500) 25%, var(--secondary-300) 50%, var(--secondary-600) 75%, var(--secondary-800) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-700) 0%, var(--accent-500) 25%, var(--accent-300) 50%, var(--accent-600) 75%, var(--accent-800) 100%);
  
  /* Page Background Gradients */
  --gradient-page-light: linear-gradient(135deg, rgba(46, 125, 50, 0.05) 0%, rgba(76, 175, 80, 0.03) 25%, rgba(139, 195, 74, 0.02) 50%, rgba(102, 187, 106, 0.03) 75%, rgba(67, 160, 71, 0.05) 100%);
  --gradient-page-dark: linear-gradient(135deg, rgba(46, 125, 50, 0.15) 0%, rgba(76, 175, 80, 0.12) 25%, rgba(139, 195, 74, 0.08) 50%, rgba(102, 187, 106, 0.12) 75%, rgba(67, 160, 71, 0.15) 100%);
  
  /* Card Background Gradients */
  --gradient-card-light: rgba(255, 255, 255, 0.8);
  --gradient-card-dark: rgba(0, 0, 0, 0.6);
  --gradient-card-glassmorphism: rgba(255, 255, 255, 0.1);
  
  /* Typography */
  --font-family-primary: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-family-heading: 'Segoe UI', system-ui, -apple-system, sans-serif;
  
  /* Font Sizes */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 2.5rem;
  --spacing-3xl: 3rem;
  --spacing-4xl: 4rem;
  --spacing-5xl: 5rem;
  --spacing-6xl: 6rem;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-3xl: 2rem;
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-glassmorphism: 0 8px 32px rgba(0, 0, 0, 0.1);
  
  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
  --transition-bounce: 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* Dark Theme Variables */
[data-theme="dark"] {
  --gradient-page-light: var(--gradient-page-dark);
  --gradient-card-light: var(--gradient-card-dark);
  
  /* Adjust colors for dark theme */
  --text-primary: var(--neutral-100);
  --text-secondary: var(--neutral-300);
  --text-muted: var(--neutral-500);
  --bg-primary: var(--neutral-900);
  --bg-secondary: var(--neutral-800);
  --bg-tertiary: var(--neutral-700);
}

/* Light Theme Variables (Default) */
[data-theme="light"], :root {
  --text-primary: var(--neutral-900);
  --text-secondary: var(--neutral-700);
  --text-muted: var(--neutral-500);
  --bg-primary: var(--neutral-50);
  --bg-secondary: var(--neutral-100);
  --bg-tertiary: var(--neutral-200);
}

/* Global Reset and Base Styles */
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  color: var(--text-primary);
  background: var(--bg-primary);
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Utility Classes */
.gradient-primary {
  background: var(--gradient-primary);
}

.gradient-secondary {
  background: var(--gradient-secondary);
}

.gradient-accent {
  background: var(--gradient-accent);
}

.gradient-page {
  background: var(--gradient-page-light);
}

.glassmorphism {
  background: var(--gradient-card-glassmorphism);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.shadow-glassmorphism {
  box-shadow: var(--shadow-glassmorphism);
}

/* Responsive Typography */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  html {
    font-size: 13px;
  }
}
