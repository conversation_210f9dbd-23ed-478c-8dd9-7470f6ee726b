export interface ModuleAccess {
  id: string;
  title: string;
  icon: string;
  description: string;
  path: string;
  category: 'core' | 'advanced' | 'analytics' | 'premium';
  requiredPlan: 'beta' | 'professional';
  limitations?: {
    beta?: string;
    professional?: string;
  };
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  price: string;
  duration: string;
  description: string;
  features: string[];
  moduleAccess: string[];
  limitations: Record<string, any>;
  popular?: boolean;
  color: string;
}

// Module definitions with subscription requirements
export const moduleAccess: ModuleAccess[] = [
  // BETA ACCESS MODULES (Free Trial)
  {
    id: 'dashboard',
    title: 'Dashboard Overview',
    icon: 'Dashboard',
    description: 'Basic farm overview and key metrics',
    path: '/dashboard',
    category: 'core',
    requiredPlan: 'beta',
    limitations: {
      beta: 'Basic metrics only',
      professional: 'Advanced analytics included'
    }
  },
  {
    id: 'animals',
    title: 'Animal Management',
    icon: 'Pets',
    description: 'Manage your livestock records',
    path: '/dashboard/animals',
    category: 'core',
    requiredPlan: 'beta',
    limitations: {
      beta: 'Up to 50 animals',
      professional: 'Up to 500 animals'
    }
  },
  {
    id: 'health',
    title: 'Animal Health',
    icon: 'LocalHospital',
    description: 'Health monitoring and vaccination tracking',
    path: '/dashboard/health',
    category: 'core',
    requiredPlan: 'beta',
    limitations: {
      beta: 'Basic health records',
      professional: 'AI health predictions'
    }
  },
  {
    id: 'resources',
    title: 'Resources & Information',
    icon: 'MenuBook',
    description: 'Government resources, auctions, and information',
    path: '/dashboard/resources',
    category: 'core',
    requiredPlan: 'beta'
  },

  // PROFESSIONAL MODULES (R299/month)
  {
    id: 'breeding',
    title: 'Breeding Management',
    icon: 'Favorite',
    description: 'Advanced breeding optimization and genetics',
    path: '/dashboard/breeding',
    category: 'advanced',
    requiredPlan: 'professional'
  },
  {
    id: 'feeding',
    title: 'Feed Management',
    icon: 'Restaurant',
    description: 'Automated feeding schedules and nutrition',
    path: '/dashboard/feeding',
    category: 'advanced',
    requiredPlan: 'professional'
  },
  {
    id: 'financial',
    title: 'Financial Management',
    icon: 'AccountBalance',
    description: 'Complete P&L analysis and tax optimization',
    path: '/dashboard/financial',
    category: 'advanced',
    requiredPlan: 'professional'
  },
  {
    id: 'inventory',
    title: 'Inventory Management',
    icon: 'Inventory',
    description: 'Smart inventory tracking and automated ordering',
    path: '/dashboard/inventory',
    category: 'advanced',
    requiredPlan: 'professional'
  },
  {
    id: 'reports',
    title: 'Advanced Reports',
    icon: 'Assessment',
    description: 'Comprehensive reporting and basic analytics',
    path: '/dashboard/reports',
    category: 'analytics',
    requiredPlan: 'professional'
  },

  // ENTERPRISE MODULES (R599/month)
  {
    id: 'commercial',
    title: 'Commercial Operations',
    icon: 'Business',
    description: 'Market analysis and commercial optimization',
    path: '/dashboard/commercial',
    category: 'premium',
    requiredPlan: 'enterprise'
  },
  {
    id: 'compliance',
    title: 'Compliance Management',
    icon: 'Gavel',
    description: 'Automated compliance tracking and reporting',
    path: '/dashboard/compliance',
    category: 'premium',
    requiredPlan: 'enterprise'
  },
  {
    id: 'analytics',
    title: 'AI Analytics & Insights',
    icon: 'Analytics',
    description: 'AI-powered predictive analytics and insights',
    path: '/dashboard/analytics',
    category: 'premium',
    requiredPlan: 'enterprise'
  },
  {
    id: 'settings',
    title: 'Advanced Settings',
    icon: 'Settings',
    description: 'Multi-user management and advanced configurations',
    path: '/dashboard/settings',
    category: 'core',
    requiredPlan: 'professional'
  }
];

// Subscription plans
export const subscriptionPlans: SubscriptionPlan[] = [
  {
    id: 'beta',
    name: 'Beta Access',
    price: 'Free',
    duration: '30 days trial',
    description: 'Perfect for small-scale South African farmers',
    features: [
      '🐄 Up to 50 animals',
      '📱 Mobile app access',
      '📊 Basic health monitoring',
      '📈 Simple reports',
      '📧 Email support',
      '🇿🇦 South African language support'
    ],
    moduleAccess: ['dashboard', 'animals', 'health', 'resources'],
    limitations: {
      animals: 50,
      healthRecords: 'basic',
      reports: 'simple',
      support: 'email'
    },
    popular: false,
    color: 'linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%)'
  },
  {
    id: 'professional',
    name: 'Professional',
    price: 'R299',
    duration: 'per month',
    description: 'For growing commercial farms across SA',
    features: [
      '🐄 Up to 500 animals',
      '🤖 AI health analytics',
      '💰 Financial management (ZAR)',
      '🧬 Breeding optimization',
      '📞 Priority support',
      '📊 Advanced reports',
      '🔗 API access',
      '🌍 Multi-location support'
    ],
    moduleAccess: ['dashboard', 'animals', 'health', 'resources', 'breeding', 'feeding', 'financial', 'inventory', 'reports', 'settings'],
    limitations: {
      animals: 500,
      healthRecords: 'advanced',
      reports: 'comprehensive',
      support: 'priority'
    },
    popular: true,
    color: 'linear-gradient(135deg, #2196F3 0%, #1565C0 100%)'
  }
];

// Helper functions
export const getModulesByPlan = (planId: string): ModuleAccess[] => {
  const plan = subscriptionPlans.find(p => p.id === planId);
  if (!plan) return [];
  
  return moduleAccess.filter(module => plan.moduleAccess.includes(module.id));
};

export const getLockedModules = (planId: string): ModuleAccess[] => {
  const plan = subscriptionPlans.find(p => p.id === planId);
  if (!plan) return moduleAccess;
  
  return moduleAccess.filter(module => !plan.moduleAccess.includes(module.id));
};

export const canAccessModule = (moduleId: string, userPlan: string): boolean => {
  const plan = subscriptionPlans.find(p => p.id === userPlan);
  return plan ? plan.moduleAccess.includes(moduleId) : false;
};

export const getUpgradeRequirement = (moduleId: string): string => {
  const module = moduleAccess.find(m => m.id === moduleId);
  return module ? module.requiredPlan : 'professional';
};

export const getPlanByModule = (moduleId: string): SubscriptionPlan | undefined => {
  const module = moduleAccess.find(m => m.id === moduleId);
  if (!module) return undefined;
  
  return subscriptionPlans.find(p => p.id === module.requiredPlan);
};
