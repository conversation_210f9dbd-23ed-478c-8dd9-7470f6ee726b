// Test script to identify problematic route files
const path = require('path');

const routeFiles = [
  './src/routes/api/animals',
  './src/routes/api/auth',
  './src/routes/api/users',
  './src/routes/api/health',
  './src/routes/api/breeding',
  './src/routes/api/financial',
  './src/routes/api/inventory',
  './src/routes/api/business',
  './src/routes/api/reports',
  './src/routes/api/resources',
  './src/routes/api/feeding',
  './src/routes/api/compliance',
  './src/routes/api/subscription'
];

console.log('Testing route file imports...\n');

routeFiles.forEach(routeFile => {
  try {
    const route = require(routeFile);
    if (route && typeof route === 'function') {
      console.log(`✅ ${routeFile} - OK (function)`);
    } else if (route && typeof route === 'object') {
      console.log(`✅ ${routeFile} - OK (router object)`);
    } else {
      console.log(`❌ ${routeFile} - INVALID EXPORT: ${typeof route}`);
    }
  } catch (error) {
    console.log(`❌ ${routeFile} - ERROR: ${error.message}`);
  }
});
