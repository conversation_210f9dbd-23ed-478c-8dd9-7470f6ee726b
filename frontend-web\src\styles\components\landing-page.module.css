/* Landing Page Component Styles - CSS Module */

/* <PERSON><PERSON> */
.buttonBase {
  padding: 1.2rem 2.5rem;
  margin: 0.5rem;
  border: none;
  border-radius: 50px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  text-transform: uppercase;
  letter-spacing: 1.5px;
  position: relative;
  overflow: hidden;
}

.primaryButton {
  composes: buttonBase;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.primaryButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.6);
}

.secondaryButton {
  composes: buttonBase;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.secondaryButton:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Card Styles */
.card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2.5rem;
  text-align: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.card:hover::before {
  left: 100%;
}

/* Section Styles */
.sectionFull {
  width: 100%;
  padding: 6rem 4rem;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .sectionFull {
    padding: 4rem 2rem;
  }
  
  .card {
    padding: 2rem;
  }
  
  .buttonBase {
    padding: 1rem 2rem;
    font-size: 0.9rem;
  }
}

/* Animation Classes */
.fadeInUp {
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slideInLeft {
  animation: slideInLeft 0.8s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slideInRight {
  animation: slideInRight 0.8s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Glassmorphism Effects */
.glassmorphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
}

.glassmorphismDark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
}

/* AgriIntel Theme Colors */
.primaryGradient {
  background: linear-gradient(135deg, #2e7d32 0%, #4caf50 25%, #8bc34a 50%, #66bb6a 75%, #43a047 100%);
}

.secondaryGradient {
  background: linear-gradient(135deg, #1565c0 0%, #1976d2 25%, #2196f3 50%, #42a5f5 75%, #1e88e5 100%);
}

.accentGradient {
  background: linear-gradient(135deg, #f57c00 0%, #ff9800 25%, #ffb74d 50%, #ffa726 75%, #fb8c00 100%);
}

/* Section Layouts */
.sectionWithBackground {
  background: rgba(0, 0, 0, 0.2);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  padding: 4rem 2rem;
  position: relative;
  z-index: 10;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

.sectionOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(27, 94, 32, 0.9) 0%,
    rgba(46, 125, 50, 0.85) 25%,
    rgba(76, 175, 80, 0.8) 50%,
    rgba(139, 195, 74, 0.85) 75%,
    rgba(67, 160, 71, 0.9) 100%
  );
  z-index: 1;
}

.sectionContent {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

/* Tab Navigation */
.tabNavigation {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.tabButton {
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.tabButtonActive {
  composes: tabButton;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.3), rgba(255, 193, 7, 0.2));
  border: 2px solid rgba(255, 215, 0, 0.6);
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
}

.tabButton:hover {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.25), rgba(255, 193, 7, 0.15));
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
}

/* Content Grids */
.featuresGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.pricingGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.servicesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Typography */
.sectionTitle {
  font-size: 2.5rem;
  margin-bottom: 3rem;
  background: linear-gradient(45deg, #FFD700, #FFA500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sectionSubtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 3rem;
  color: rgba(255, 255, 255, 0.9);
}

.cardTitle {
  margin-bottom: 1rem;
  font-size: 1.3rem;
  color: #FFD700;
}

.cardDescription {
  opacity: 0.9;
  line-height: 1.6;
}

/* Pricing Cards */
.pricingCard {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(30px);
  backdrop-filter: blur(30px);
  padding: 2.5rem;
  border-radius: 25px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  cursor: pointer;
  transform: scale(1);
  transition: all 0.4s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.pricingCardPopular {
  composes: pricingCard;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.1));
  border: 2px solid rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
  box-shadow: 0 20px 60px rgba(255, 255, 255, 0.2);
}

.pricingCard:hover {
  transform: scale(1.03);
  box-shadow: 0 25px 80px rgba(255, 255, 255, 0.3);
}

.pricingCardPopular:hover {
  transform: scale(1.08);
}

.popularBadge {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #ffffff, rgba(255,255,255,0.9));
  color: #667eea;
  padding: 0.5rem 1.5rem;
  border-radius: 25px;
  font-weight: 800;
  font-size: 0.8rem;
  letter-spacing: 1px;
  box-shadow: 0 4px 20px rgba(255, 255, 255, 0.3);
}

.pricingTitle {
  margin-bottom: 1rem;
  font-size: 1.6rem;
  color: white;
  font-weight: 700;
}

.pricingPrice {
  font-size: 3rem;
  font-weight: 900;
  color: white;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.pricingDuration {
  opacity: 0.8;
  margin-bottom: 2rem;
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
}

.pricingDescription {
  margin-bottom: 2rem;
  opacity: 0.9;
  font-size: 1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
}

.pricingFeatures {
  list-style: none;
  padding: 0;
  margin-bottom: 2.5rem;
  text-align: left;
}

.pricingFeature {
  padding: 0.8rem 0;
  border-bottom: 1px solid rgba(255,255,255,0.1);
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.95rem;
}

.pricingButton {
  width: 100%;
  padding: 1.2rem;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  cursor: pointer;
  font-weight: 700;
  font-size: 1rem;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.pricingButtonPopular {
  composes: pricingButton;
  background: linear-gradient(135deg, #ffffff, rgba(255,255,255,0.9));
  color: #667eea;
  border: none;
}

.pricingButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
}

/* Contact Section */
.contactGrid {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.contactItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255,255,255,0.1);
  border-radius: 12px;
}

.contactIcon {
  font-size: 2rem;
}

.contactLabel {
  font-weight: bold;
  color: #FFD700;
}

/* Stats Section */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.statItem {
  text-align: center;
}

.statIcon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.statNumber {
  font-size: 1.8rem;
  font-weight: bold;
  color: #FFD700;
  margin-bottom: 0.5rem;
}

.statLabel {
  opacity: 0.8;
}

/* Footer */
.footer {
  background: rgba(0,0,0,0.3);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  padding: 3rem 2rem 1rem;
  color: white;
}

.footerContent {
  max-width: 1400px;
  margin: 0 auto;
}

.footerBrand {
  margin-bottom: 2rem;
}

.footerTitle {
  font-size: 2rem;
  font-weight: 800;
  background: linear-gradient(45deg, #FFD700, #FFA500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
}

.footerDescription {
  opacity: 0.9;
  font-size: 1rem;
  line-height: 1.6;
  max-width: 600px;
}

.footerGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footerSection h4 {
  color: #FFD700;
  margin-bottom: 1rem;
}

.footerLinks {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footerLink {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  text-align: left;
  padding: 0;
  font-size: 1rem;
  transition: color 0.3s ease;
}

.footerLink:hover {
  color: #FFD700;
}

.footerBottom {
  border-top: 1px solid rgba(255,255,255,0.1);
  padding-top: 2rem;
  text-align: center;
  opacity: 0.7;
}

/* Animations */
.fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sectionWithBackground {
    padding: 3rem 1rem;
  }

  .tabNavigation {
    flex-direction: column;
    align-items: center;
  }

  .featuresGrid,
  .pricingGrid,
  .servicesGrid {
    grid-template-columns: 1fr;
  }

  .sectionTitle {
    font-size: 2rem;
  }

  .pricingCardPopular {
    transform: scale(1);
  }

  .pricingCardPopular:hover {
    transform: scale(1.02);
  }
}
