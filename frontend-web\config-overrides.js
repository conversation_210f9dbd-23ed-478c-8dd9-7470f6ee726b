
const webpack = require('webpack');
const path = require('path');
const { override } = require('customize-cra');
const webpackConfig = require('./webpack.config');

module.exports = override(
  // Apply our webpack config
  (config) => {
    // Merge our webpack config with the create-react-app config
    config.resolve.fallback = {
      ...config.resolve.fallback,
      ...webpackConfig.resolve.fallback
    };

    config.resolve.alias = {
      ...config.resolve.alias,
      ...webpackConfig.resolve.alias
    };

    // Add mainFields to prioritize browser field in package.json
    config.resolve.mainFields = ['browser', 'module', 'main'];

    config.plugins = [
      ...config.plugins,
      ...webpackConfig.plugins,
      // Add plugin to suppress ResizeObserver errors
      new webpack.DefinePlugin({
        'process.env.SUPPRESS_RESIZE_OBSERVER_ERRORS': JSON.stringify('true')
      })
    ];

    // Add module rules for handling Node.js modules
    config.module.rules.push({
      test: /\.m?js$/,
      resolve: {
        fullySpecified: false
      }
    });

    // Add optimization to suppress ResizeObserver errors
    config.optimization = {
      ...config.optimization,
      minimizer: [
        ...config.optimization.minimizer,
        new webpack.optimize.ModuleConcatenationPlugin()
      ]
    };

    // Add Babel loader for JSX
    config.module.rules.push({
      test: /\.(js|jsx|ts|tsx)$/,
      exclude: /node_modules/,
      use: {
        loader: 'babel-loader',
        options: {
          presets: [
            '@babel/preset-env',
            ['@babel/preset-react', { runtime: 'automatic' }]
          ],
          plugins: [
            ['@babel/plugin-proposal-class-properties', { loose: true }]
          ]
        }
      }
    });

    return config;
  }
);
