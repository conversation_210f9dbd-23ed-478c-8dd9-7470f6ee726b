/**
 * BETA V1 Login Styles
 * Yellow accent theme for BETA tier authentication
 */

/* BETA V1 Login Container */
.beta-v1-login-container {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #F57C00 0%, #FFB74D 100%);
}

/* Background with Livestock Photography */
.login-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  z-index: 0;
}

/* Dynamic background styles */
.login-background-dynamic {
  background-image: url('../../public/images/animals/cattle-1.jpeg');
}

/* Logo styling */
.agri-logo {
  height: 60px;
  margin-bottom: 16px;
  width: auto;
  object-fit: contain;
}

.background-overlay-beta {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(245, 124, 0, 0.85) 0%,
    rgba(255, 183, 77, 0.75) 50%,
    rgba(245, 124, 0, 0.85) 100%
  );
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
}

/* Glassmorphism Card for BETA */
.glass-card-beta {
  background: rgba(255, 255, 255, 0.15) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(255, 255, 255, 0.25) !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
  transition: all 0.3s ease-in-out !important;
}

.glass-card-beta:hover {
  transform: translateY(-4px) !important;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
}

/* BETA V1 Specific Styling */
.beta-v1-login-container .MuiTextField-root {
  margin-bottom: 16px;
}

.beta-v1-login-container .MuiTextField-root .MuiOutlinedInput-root {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease-in-out;
}

.beta-v1-login-container .MuiTextField-root .MuiOutlinedInput-root:hover {
  background-color: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

.beta-v1-login-container .MuiTextField-root .MuiOutlinedInput-root.Mui-focused {
  background-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 0 0 2px rgba(245, 124, 0, 0.3);
}

/* Button Styling */
.beta-v1-login-container .MuiButton-contained {
  background: linear-gradient(135deg, #F57C00 0%, #FFB74D 100%) !important;
  border: none !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 15px rgba(245, 124, 0, 0.3) !important;
  transition: all 0.3s ease-in-out !important;
  text-transform: none !important;
  font-weight: 600 !important;
  font-size: 1.1rem !important;
  padding: 12px 24px !important;
}

.beta-v1-login-container .MuiButton-contained:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(245, 124, 0, 0.4) !important;
}

.beta-v1-login-container .MuiButton-contained:active {
  transform: translateY(0) !important;
}

/* Outlined Button for Upgrade */
.beta-v1-login-container .MuiButton-outlined {
  border-color: #2E7D32 !important;
  color: #2E7D32 !important;
  border-radius: 12px !important;
  font-weight: 600 !important;
  text-transform: none !important;
  transition: all 0.3s ease-in-out !important;
}

.beta-v1-login-container .MuiButton-outlined:hover {
  background-color: rgba(46, 125, 50, 0.1) !important;
  border-color: #2E7D32 !important;
  transform: translateY(-1px) !important;
}

/* Typography */
.beta-v1-login-container .MuiTypography-h3 {
  font-weight: 700 !important;
  color: var(--agri-white) !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.beta-v1-login-container .MuiTypography-h4 {
  font-weight: 700 !important;
  color: var(--agri-white) !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.beta-v1-login-container .MuiTypography-h6 {
  font-weight: 600 !important;
  color: var(--agri-white) !important;
}

/* List Items */
.beta-v1-login-container .MuiListItem-root {
  padding: 8px 0 !important;
  border-radius: 8px !important;
  transition: all 0.2s ease-in-out !important;
}

.beta-v1-login-container .MuiListItem-root:hover {
  background-color: rgba(255, 255, 255, 0.05) !important;
  transform: translateX(4px) !important;
}

/* Chip Styling */
.beta-v1-login-container .MuiChip-root {
  border-radius: 20px !important;
  font-weight: 700 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

/* Alert Styling */
.beta-v1-login-container .MuiAlert-root {
  background-color: rgba(244, 67, 54, 0.1) !important;
  border: 1px solid rgba(244, 67, 54, 0.3) !important;
  color: #ffcdd2 !important;
}

/* Divider */
.beta-v1-login-container .MuiDivider-root {
  border-color: rgba(255, 255, 255, 0.2) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .beta-v1-login-container {
    padding: 16px;
  }
  
  .glass-card-beta {
    margin: 16px 0 !important;
  }
  
  .beta-v1-login-container .MuiTypography-h3 {
    font-size: 2rem !important;
  }
  
  .beta-v1-login-container .MuiContainer-root {
    padding: 16px !important;
  }
}

/* Animation for form elements */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.beta-v1-login-container .MuiTextField-root,
.beta-v1-login-container .MuiButton-root {
  animation: fadeInUp 0.6s ease-out;
}

/* Focus states for accessibility */
.beta-v1-login-container .MuiButton-root:focus,
.beta-v1-login-container .MuiTextField-root:focus-within {
  outline: 2px solid #F57C00 !important;
  outline-offset: 2px !important;
}

/* Loading state */
.beta-v1-login-container .MuiButton-root.Mui-disabled {
  background: rgba(245, 124, 0, 0.5) !important;
  color: rgba(255, 255, 255, 0.7) !important;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .beta-v1-login-container {
    background: #000000;
  }
  
  .glass-card-beta {
    background: rgba(255, 255, 255, 0.9) !important;
    color: #000000 !important;
  }
  
  .beta-v1-login-container .MuiTypography-root {
    color: #000000 !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .beta-v1-login-container * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .glass-card-beta:hover,
  .beta-v1-login-container .MuiButton-root:hover,
  .beta-v1-login-container .MuiTextField-root:hover {
    transform: none !important;
  }
}
