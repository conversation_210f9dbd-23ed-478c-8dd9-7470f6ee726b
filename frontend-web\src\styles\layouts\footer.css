/**
 * Footer Layout Styles
 * Professional footer for AgriIntel platform
 */

/* ===== FOOTER CONTAINER ===== */

.agri-footer {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border-top: 1px solid var(--glass-border);
  margin-top: auto;
  position: relative;
}

.agri-footer.dark-mode {
  background: rgba(18, 18, 18, 0.95);
}

/* ===== FOOTER CONTENT ===== */

.agri-footer-content {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: var(--spacing-2xl) var(--spacing-lg) var(--spacing-lg);
}

.agri-footer-main {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-xl);
}

/* ===== FOOTER BRAND SECTION ===== */

.agri-footer-brand {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.agri-footer-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.agri-footer-logo img {
  height: 48px;
  width: auto;
  object-fit: contain;
}

.agri-footer-brand-text {
  display: flex;
  flex-direction: column;
}

.agri-footer-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0;
  line-height: 1.2;
}

.agri-footer-tagline {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
  line-height: 1.2;
}

.agri-footer-description {
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-lg);
}

/* Social Links */
.agri-footer-social {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.agri-social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-full);
  background: var(--color-background-primary);
  color: var(--color-text-primary);
  text-decoration: none;
  transition: var(--transition-normal);
}

.agri-social-link:hover {
  background: var(--agri-primary);
  border-color: var(--agri-primary);
  color: var(--color-white);
  transform: translateY(-2px);
}

.agri-social-link:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}

/* ===== FOOTER SECTIONS ===== */

.agri-footer-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.agri-footer-section-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0 0 var(--spacing-md) 0;
}

.agri-footer-links {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  list-style: none;
  margin: 0;
  padding: 0;
}

.agri-footer-link {
  color: var(--color-text-secondary);
  text-decoration: none;
  font-size: var(--font-size-base);
  transition: var(--transition-normal);
  padding: var(--spacing-xs) 0;
}

.agri-footer-link:hover {
  color: var(--agri-primary);
  text-decoration: underline;
}

.agri-footer-link:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}

/* ===== CONTACT INFO ===== */

.agri-footer-contact {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.agri-contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

.agri-contact-icon {
  width: 20px;
  height: 20px;
  color: var(--agri-primary);
  flex-shrink: 0;
}

.agri-contact-text {
  line-height: var(--line-height-normal);
}

.agri-contact-link {
  color: inherit;
  text-decoration: none;
  transition: var(--transition-normal);
}

.agri-contact-link:hover {
  color: var(--agri-primary);
  text-decoration: underline;
}

/* ===== NEWSLETTER SIGNUP ===== */

.agri-newsletter {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.agri-newsletter-description {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
}

.agri-newsletter-form {
  display: flex;
  gap: var(--spacing-sm);
}

.agri-newsletter-input {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  background: var(--color-background-primary);
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  transition: var(--transition-normal);
}

.agri-newsletter-input:focus {
  outline: none;
  border-color: var(--agri-primary);
  box-shadow: 0 0 0 3px rgba(21, 101, 192, 0.1);
}

.agri-newsletter-input::placeholder {
  color: var(--color-text-placeholder);
}

.agri-newsletter-btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-lg);
  background: var(--agri-primary);
  color: var(--color-white);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: var(--transition-normal);
  white-space: nowrap;
}

.agri-newsletter-btn:hover {
  background: var(--agri-primary-dark);
  transform: translateY(-1px);
}

.agri-newsletter-btn:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}

/* ===== FOOTER BOTTOM ===== */

.agri-footer-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--color-border);
  gap: var(--spacing-lg);
}

.agri-footer-copyright {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin: 0;
}

.agri-footer-legal {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  list-style: none;
  margin: 0;
  padding: 0;
}

.agri-footer-legal .agri-footer-link {
  font-size: var(--font-size-sm);
  padding: var(--spacing-xs);
}

/* ===== FOOTER BADGES ===== */

.agri-footer-badges {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.agri-footer-badge {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  background: var(--color-background-secondary);
  color: var(--color-text-secondary);
  font-size: var(--font-size-xs);
  text-decoration: none;
  transition: var(--transition-normal);
}

.agri-footer-badge:hover {
  background: var(--color-background-hover);
  border-color: var(--agri-primary);
  color: var(--agri-primary);
}

.agri-badge-icon {
  width: 16px;
  height: 16px;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 1024px) {
  .agri-footer-main {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
  }
  
  .agri-footer-brand {
    grid-column: 1 / -1;
  }
}

@media (max-width: 768px) {
  .agri-footer-content {
    padding: var(--spacing-xl) var(--spacing-md) var(--spacing-lg);
  }
  
  .agri-footer-main {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .agri-footer-bottom {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }
  
  .agri-footer-legal {
    flex-wrap: wrap;
    gap: var(--spacing-md);
  }
  
  .agri-newsletter-form {
    flex-direction: column;
  }
  
  .agri-newsletter-btn {
    align-self: flex-start;
  }
}

@media (max-width: 480px) {
  .agri-footer-content {
    padding: var(--spacing-lg) var(--spacing-sm) var(--spacing-md);
  }
  
  .agri-footer-social {
    justify-content: center;
  }
  
  .agri-social-link {
    width: 40px;
    height: 40px;
  }
  
  .agri-footer-badges {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .agri-footer-legal {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  .agri-social-link:hover,
  .agri-newsletter-btn:hover {
    transform: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .agri-footer {
    background: var(--color-background-primary);
    border-top: 2px solid var(--color-text-primary);
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
  }
  
  .agri-social-link,
  .agri-newsletter-input,
  .agri-footer-badge {
    border: 2px solid var(--color-text-primary);
  }
  
  .agri-social-link:hover,
  .agri-newsletter-btn {
    background: var(--color-text-primary);
    color: var(--color-background-primary);
    border-color: var(--color-text-primary);
  }
}

/* ===== PRINT STYLES ===== */

@media print {
  .agri-footer {
    background: none;
    border-top: 1px solid #000;
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
  }
  
  .agri-footer-social,
  .agri-newsletter,
  .agri-footer-badges {
    display: none;
  }
  
  .agri-footer-main {
    grid-template-columns: 1fr 1fr;
  }
  
  .agri-footer-brand {
    grid-column: 1;
  }
}
