/**
 * Dashboard Layout Styles
 * Professional dashboard layout for AgriIntel platform
 */

/* ===== DASHBOARD CONTAINER ===== */

.agri-dashboard {
  display: flex;
  min-height: 100vh;
  background: var(--color-background-primary);
  position: relative;
}

.agri-dashboard.dark-mode {
  background: var(--color-background-dark);
}

/* ===== SIDEBAR ===== */

.agri-sidebar {
  width: 280px;
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border-right: 1px solid var(--glass-border);
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: var(--z-sidebar);
  transition: var(--transition-normal);
  overflow-y: auto;
}

.agri-sidebar.collapsed {
  width: 80px;
}

.agri-sidebar.mobile-hidden {
  transform: translateX(-100%);
}

/* Sidebar Header */
.agri-sidebar-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.agri-sidebar-logo {
  height: 40px;
  width: auto;
  object-fit: contain;
}

.agri-sidebar-brand {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  white-space: nowrap;
  overflow: hidden;
  transition: var(--transition-normal);
}

.agri-sidebar.collapsed .agri-sidebar-brand {
  opacity: 0;
  width: 0;
}

/* Sidebar Navigation */
.agri-sidebar-nav {
  padding: var(--spacing-md) 0;
}

.agri-nav-section {
  margin-bottom: var(--spacing-lg);
}

.agri-nav-section-title {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
  transition: var(--transition-normal);
}

.agri-sidebar.collapsed .agri-nav-section-title {
  opacity: 0;
  height: 0;
  margin: 0;
  padding: 0;
}

.agri-nav-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  color: var(--color-text-primary);
  text-decoration: none;
  transition: var(--transition-normal);
  position: relative;
  border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
  margin-right: var(--spacing-md);
}

.agri-nav-item:hover {
  background: var(--color-background-hover);
  color: var(--agri-primary);
}

.agri-nav-item.active {
  background: var(--agri-primary);
  color: var(--color-white);
}

.agri-nav-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--agri-accent);
}

.agri-nav-icon {
  width: 24px;
  height: 24px;
  margin-right: var(--spacing-md);
  flex-shrink: 0;
}

.agri-nav-text {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
  overflow: hidden;
  transition: var(--transition-normal);
}

.agri-sidebar.collapsed .agri-nav-text {
  opacity: 0;
  width: 0;
  margin: 0;
}

.agri-nav-badge {
  margin-left: auto;
  background: var(--agri-accent);
  color: var(--color-white);
  font-size: var(--font-size-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  min-width: 20px;
  text-align: center;
}

.agri-sidebar.collapsed .agri-nav-badge {
  display: none;
}

/* ===== MAIN CONTENT ===== */

.agri-main {
  flex: 1;
  margin-left: 280px;
  transition: var(--transition-normal);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.agri-sidebar.collapsed + .agri-main {
  margin-left: 80px;
}

/* Main Header */
.agri-main-header {
  background: var(--color-background-primary);
  border-bottom: 1px solid var(--color-border);
  padding: var(--spacing-md) var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: var(--z-header);
}

.agri-header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.agri-sidebar-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--radius-md);
  background: transparent;
  color: var(--color-text-primary);
  cursor: pointer;
  transition: var(--transition-normal);
}

.agri-sidebar-toggle:hover {
  background: var(--color-background-hover);
}

.agri-page-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0;
}

.agri-breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.agri-breadcrumb-item {
  color: inherit;
  text-decoration: none;
}

.agri-breadcrumb-item:hover {
  color: var(--agri-primary);
}

.agri-breadcrumb-separator {
  color: var(--color-text-disabled);
}

.agri-header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* Header Actions */
.agri-header-search {
  position: relative;
  min-width: 300px;
}

.agri-search-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md) var(--spacing-sm) var(--spacing-xl);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-full);
  background: var(--color-background-secondary);
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  transition: var(--transition-normal);
}

.agri-search-input:focus {
  outline: none;
  border-color: var(--agri-primary);
  background: var(--color-background-primary);
}

.agri-search-icon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-text-secondary);
  pointer-events: none;
}

.agri-header-notifications {
  position: relative;
}

.agri-notification-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--radius-full);
  background: transparent;
  color: var(--color-text-primary);
  cursor: pointer;
  transition: var(--transition-normal);
  position: relative;
}

.agri-notification-btn:hover {
  background: var(--color-background-hover);
}

.agri-notification-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  background: var(--color-error);
  border-radius: var(--radius-full);
  border: 2px solid var(--color-background-primary);
}

.agri-header-profile {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: var(--transition-normal);
}

.agri-header-profile:hover {
  background: var(--color-background-hover);
}

.agri-profile-avatar {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  background: var(--agri-primary);
  color: var(--color-white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-sm);
}

.agri-profile-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.agri-profile-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  line-height: 1.2;
}

.agri-profile-role {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  line-height: 1.2;
}

/* ===== MAIN CONTENT AREA ===== */

.agri-main-content {
  flex: 1;
  padding: var(--spacing-lg);
  overflow-y: auto;
}

.agri-content-container {
  max-width: 1400px;
  margin: 0 auto;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 1024px) {
  .agri-sidebar {
    transform: translateX(-100%);
  }
  
  .agri-sidebar.mobile-open {
    transform: translateX(0);
  }
  
  .agri-main {
    margin-left: 0;
  }
  
  .agri-header-search {
    min-width: 200px;
  }
}

@media (max-width: 768px) {
  .agri-main-header {
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  .agri-header-search {
    display: none;
  }
  
  .agri-profile-info {
    display: none;
  }
  
  .agri-main-content {
    padding: var(--spacing-md);
  }
  
  .agri-page-title {
    font-size: var(--font-size-lg);
  }
}

@media (max-width: 480px) {
  .agri-main-content {
    padding: var(--spacing-sm);
  }
  
  .agri-sidebar {
    width: 100%;
  }
  
  .agri-header-right {
    gap: var(--spacing-sm);
  }
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  .agri-sidebar,
  .agri-main,
  .agri-nav-item,
  .agri-sidebar-brand,
  .agri-nav-text {
    transition: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .agri-sidebar {
    background: var(--color-background-primary);
    border-right: 2px solid var(--color-text-primary);
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
  }
  
  .agri-nav-item.active {
    background: var(--color-text-primary);
    color: var(--color-background-primary);
  }
  
  .agri-main-header {
    border-bottom: 2px solid var(--color-text-primary);
  }
}
