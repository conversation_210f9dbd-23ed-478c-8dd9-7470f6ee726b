import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>rid,
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  StepLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel,
  Alert,
  Divider,
  useTheme,
  alpha,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip
} from '@mui/material';
import {
  Description,
  Payment,
  Security,
  CheckCircle,
  Gavel,
  AccountBalance,
  CreditCard,
  Receipt,
  Shield,
  Assignment,
  MonetizationOn,
  Warning,
  Info
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useLanguage } from '../../contexts/LanguageContext';

interface Contract {
  id: string;
  serviceType: string;
  providerName: string;
  farmerName: string;
  description: string;
  amount: number;
  currency: string;
  terms: string[];
  status: 'draft' | 'pending_signature' | 'active' | 'completed' | 'cancelled';
  createdAt: string;
  signedAt?: string;
  completedAt?: string;
}

interface PaymentMethod {
  id: string;
  type: 'card' | 'bank_transfer' | 'eft' | 'cash';
  name: string;
  details: string;
  preferred: boolean;
}

const ContractPaymentSystem: React.FC = () => {
  const theme = useTheme();
  const { translate } = useLanguage();
  
  const [activeStep, setActiveStep] = useState(0);
  const [showContractDialog, setShowContractDialog] = useState(false);
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);
  const [selectedContract, setSelectedContract] = useState<Contract | null>(null);
  const [contracts, setContracts] = useState<Contract[]>([]);
  
  const [newContract, setNewContract] = useState({
    serviceType: '',
    providerName: '',
    description: '',
    amount: 0,
    terms: [] as string[]
  });

  const [paymentDetails, setPaymentDetails] = useState({
    method: 'card',
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    bankAccount: '',
    reference: ''
  });

  const paymentMethods: PaymentMethod[] = [
    {
      id: 'card',
      type: 'card',
      name: 'Credit/Debit Card',
      details: 'Visa, Mastercard, American Express',
      preferred: true
    },
    {
      id: 'eft',
      type: 'eft',
      name: 'EFT Transfer',
      details: 'Electronic Funds Transfer',
      preferred: false
    },
    {
      id: 'bank',
      type: 'bank_transfer',
      name: 'Bank Transfer',
      details: 'Direct bank-to-bank transfer',
      preferred: false
    }
  ];

  const saTermsAndConditions = [
    'Service provider must be registered with relevant South African authorities',
    'All work must comply with South African labour laws and safety regulations',
    'Payment terms: 50% upfront, 50% on completion (as per SA commercial practices)',
    'Dispute resolution through South African Commercial Mediation',
    'Contract governed by South African law',
    'VAT included where applicable (15% standard rate)',
    'Consumer Protection Act (CPA) rights apply',
    'Professional indemnity insurance required for service providers',
    'Work must meet industry standards and regulations',
    'Cancellation policy: 24-hour notice required'
  ];

  const contractSteps = [
    'Service Details',
    'Terms & Conditions',
    'Payment Setup',
    'Contract Signing',
    'Service Delivery'
  ];

  const handleCreateContract = () => {
    const contract: Contract = {
      id: `contract_${Date.now()}`,
      serviceType: newContract.serviceType,
      providerName: newContract.providerName,
      farmerName: 'Current User', // Would be from user context
      description: newContract.description,
      amount: newContract.amount,
      currency: 'ZAR',
      terms: saTermsAndConditions,
      status: 'draft',
      createdAt: new Date().toISOString()
    };
    
    setContracts([...contracts, contract]);
    setSelectedContract(contract);
    setShowContractDialog(false);
    setActiveStep(1);
  };

  const handleSignContract = () => {
    if (selectedContract) {
      const updatedContract = {
        ...selectedContract,
        status: 'pending_signature' as const,
        signedAt: new Date().toISOString()
      };
      setContracts(prev => prev.map(c => c.id === selectedContract.id ? updatedContract : c));
      setSelectedContract(updatedContract);
      setActiveStep(2);
    }
  };

  const handlePayment = () => {
    if (selectedContract) {
      const updatedContract = {
        ...selectedContract,
        status: 'active' as const
      };
      setContracts(prev => prev.map(c => c.id === selectedContract.id ? updatedContract : c));
      setSelectedContract(updatedContract);
      setShowPaymentDialog(false);
      setActiveStep(4);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return theme.palette.success.main;
      case 'completed': return theme.palette.info.main;
      case 'pending_signature': return theme.palette.warning.main;
      case 'cancelled': return theme.palette.error.main;
      default: return theme.palette.grey[500];
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" fontWeight="bold" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Description sx={{ color: theme.palette.primary.main }} />
          Contracts & Payments
        </Typography>
        
        <Button
          variant="contained"
          startIcon={<Assignment />}
          onClick={() => setShowContractDialog(true)}
        >
          Create Contract
        </Button>
      </Box>

      {/* SA Compliance Notice */}
      <Alert severity="info" sx={{ mb: 4 }}>
        <Typography variant="body2">
          <strong>South African Compliance:</strong> All contracts are generated in compliance with SA commercial law, 
          Consumer Protection Act, and include mandatory terms for service agreements.
        </Typography>
      </Alert>

      {/* Contract Process Stepper */}
      {selectedContract && (
        <Card sx={{ mb: 4, backgroundColor: alpha(theme.palette.primary.main, 0.05) }}>
          <CardContent>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              Contract Process: {selectedContract.providerName}
            </Typography>
            <Stepper activeStep={activeStep} sx={{ mt: 2 }}>
              {contractSteps.map((label) => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>
            
            <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
              {activeStep === 1 && (
                <Button variant="contained" onClick={handleSignContract}>
                  Review & Sign Contract
                </Button>
              )}
              {activeStep === 2 && (
                <Button variant="contained" onClick={() => setShowPaymentDialog(true)}>
                  Setup Payment
                </Button>
              )}
              {activeStep === 4 && (
                <Chip label="Service Active" color="success" icon={<CheckCircle />} />
              )}
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Active Contracts */}
      <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
        Contract Management
      </Typography>

      <Grid container spacing={3}>
        {contracts.length === 0 ? (
          <Grid item xs={12}>
            <Card sx={{ textAlign: 'center', py: 6 }}>
              <CardContent>
                <Description sx={{ fontSize: 64, color: theme.palette.grey[400], mb: 2 }} />
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  No contracts yet
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Create your first service contract to get started
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<Assignment />}
                  onClick={() => setShowContractDialog(true)}
                >
                  Create Contract
                </Button>
              </CardContent>
            </Card>
          </Grid>
        ) : (
          contracts.map((contract, index) => (
            <Grid item xs={12} md={6} key={contract.id}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -4 }}
              >
                <Card sx={{ 
                  borderRadius: 3,
                  border: `1px solid ${alpha(getStatusColor(contract.status), 0.3)}`,
                  background: alpha(getStatusColor(contract.status), 0.05)
                }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start', mb: 2 }}>
                      <Typography variant="h6" fontWeight="bold">
                        {contract.serviceType}
                      </Typography>
                      <Chip 
                        label={contract.status.replace('_', ' ').toUpperCase()} 
                        size="small"
                        sx={{ 
                          backgroundColor: getStatusColor(contract.status),
                          color: 'white'
                        }}
                      />
                    </Box>
                    
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Provider: {contract.providerName}
                    </Typography>
                    
                    <Typography variant="body2" sx={{ mb: 2 }}>
                      {contract.description}
                    </Typography>
                    
                    <Divider sx={{ my: 2 }} />
                    
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="h6" fontWeight="bold" color="primary">
                        R{contract.amount.toLocaleString()} {contract.currency}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Created: {new Date(contract.createdAt).toLocaleDateString()}
                      </Typography>
                    </Box>
                    
                    <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                      <Button size="small" startIcon={<Description />}>
                        View Contract
                      </Button>
                      {contract.status === 'active' && (
                        <Button size="small" startIcon={<Payment />} color="success">
                          Payment History
                        </Button>
                      )}
                    </Box>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          ))
        )}
      </Grid>

      {/* Create Contract Dialog */}
      <Dialog open={showContractDialog} onClose={() => setShowContractDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Create Service Contract</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Service Type</InputLabel>
                <Select
                  value={newContract.serviceType}
                  onChange={(e) => setNewContract({ ...newContract, serviceType: e.target.value })}
                  label="Service Type"
                >
                  <MenuItem value="Veterinary Services">Veterinary Services</MenuItem>
                  <MenuItem value="Feed Supply">Feed Supply</MenuItem>
                  <MenuItem value="Livestock Auction">Livestock Auction</MenuItem>
                  <MenuItem value="Security Services">Security Services</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Service Provider"
                value={newContract.providerName}
                onChange={(e) => setNewContract({ ...newContract, providerName: e.target.value })}
                placeholder="Provider name"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Service Description"
                value={newContract.description}
                onChange={(e) => setNewContract({ ...newContract, description: e.target.value })}
                multiline
                rows={3}
                placeholder="Detailed description of services to be provided"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Contract Amount (ZAR)"
                type="number"
                value={newContract.amount}
                onChange={(e) => setNewContract({ ...newContract, amount: Number(e.target.value) })}
              />
            </Grid>
          </Grid>
          
          <Alert severity="info" sx={{ mt: 3 }}>
            <Typography variant="body2">
              This contract will include all required South African terms and conditions for commercial service agreements.
            </Typography>
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowContractDialog(false)}>Cancel</Button>
          <Button variant="contained" onClick={handleCreateContract}>
            Create Contract
          </Button>
        </DialogActions>
      </Dialog>

      {/* Payment Setup Dialog */}
      <Dialog open={showPaymentDialog} onClose={() => setShowPaymentDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Payment />
          Setup Payment
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Secure payment processing for contract: {selectedContract?.serviceType}
          </Typography>
          
          <FormControl fullWidth sx={{ mb: 3 }}>
            <InputLabel>Payment Method</InputLabel>
            <Select
              value={paymentDetails.method}
              onChange={(e) => setPaymentDetails({ ...paymentDetails, method: e.target.value })}
              label="Payment Method"
            >
              {paymentMethods.map((method) => (
                <MenuItem key={method.id} value={method.id}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {method.type === 'card' && <CreditCard />}
                    {method.type === 'bank_transfer' && <AccountBalance />}
                    {method.type === 'eft' && <Receipt />}
                    <Box>
                      <Typography variant="body2">{method.name}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {method.details}
                      </Typography>
                    </Box>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {paymentDetails.method === 'card' && (
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Card Number"
                  value={paymentDetails.cardNumber}
                  onChange={(e) => setPaymentDetails({ ...paymentDetails, cardNumber: e.target.value })}
                  placeholder="1234 5678 9012 3456"
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Expiry Date"
                  value={paymentDetails.expiryDate}
                  onChange={(e) => setPaymentDetails({ ...paymentDetails, expiryDate: e.target.value })}
                  placeholder="MM/YY"
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="CVV"
                  value={paymentDetails.cvv}
                  onChange={(e) => setPaymentDetails({ ...paymentDetails, cvv: e.target.value })}
                  placeholder="123"
                />
              </Grid>
            </Grid>
          )}

          <Alert severity="success" sx={{ mt: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Shield />
              <Typography variant="body2">
                Payments secured by 256-bit SSL encryption and PCI DSS compliance
              </Typography>
            </Box>
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowPaymentDialog(false)}>Cancel</Button>
          <Button variant="contained" onClick={handlePayment} startIcon={<Security />}>
            Process Payment
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ContractPaymentSystem;
