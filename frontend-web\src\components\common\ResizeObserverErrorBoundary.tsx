import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

/**
 * Error boundary specifically designed to catch and suppress ResizeObserver errors
 */
class ResizeObserverErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Check if this is a ResizeObserver error
    if (error.message && 
        (error.message.includes('ResizeObserver loop') || 
         error.message.includes('ResizeObserver') ||
         error.message.includes('undelivered notifications'))) {
      // Don't update state for ResizeObserver errors - just ignore them
      return { hasError: false };
    }
    
    // For other errors, update state to show fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Only log non-ResizeObserver errors
    if (!(error.message && 
          (error.message.includes('ResizeObserver loop') || 
           error.message.includes('ResizeObserver') ||
           error.message.includes('undelivered notifications')))) {
      console.error('Error caught by ResizeObserverErrorBoundary:', error, errorInfo);
    }
  }

  render() {
    // For ResizeObserver errors, just render children normally
    if (this.state.hasError && this.state.error && 
        !(this.state.error.message && 
          (this.state.error.message.includes('ResizeObserver loop') || 
           this.state.error.message.includes('ResizeObserver') ||
           this.state.error.message.includes('undelivered notifications')))) {
      // Only show error UI for non-ResizeObserver errors
      return (
        <div style={{ 
          padding: '20px', 
          border: '1px solid #ff6b6b', 
          borderRadius: '4px', 
          backgroundColor: '#ffe0e0',
          color: '#d63031'
        }}>
          <h3>Something went wrong</h3>
          <p>An error occurred while rendering this component.</p>
          <details>
            <summary>Error details</summary>
            <pre>{this.state.error?.message}</pre>
          </details>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ResizeObserverErrorBoundary;
