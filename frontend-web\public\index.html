<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#3AA99F" />
    <meta name="msapplication-TileColor" content="#3AA99F" />
    <!-- Fallbacks for browsers that don't support theme-color -->
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="msapplication-navbutton-color" content="#3AA99F" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta
      name="description"
      content="AgriIntel - Smart Farming, Smarter Decisions. Intelligent livestock management for modern agriculture."
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyA9JZ_PcJQ4H7mmXWnUXVsCbDEEBgGEPWs&libraries=places" async defer></script>
    <script src="%PUBLIC_URL%/resize-observer-fix.js"></script>
    <script src="%PUBLIC_URL%/button-fix.js"></script>
    <style>
      /* Direct CSS fix for MUI Button component */
      .MuiButton-root {
        color: #3AA99F !important;
      }

      .MuiButton-contained {
        background-color: #3AA99F !important;
        color: #ffffff !important;
      }

      .MuiButton-contained:hover {
        background-color: #2A8A82 !important;
      }

      .MuiButton-outlined {
        border: 1px solid #3AA99F !important;
        color: #3AA99F !important;
      }

      .MuiButton-text {
        color: #3AA99F !important;
      }
    </style>
    <title>AgriIntel - Smart Farming, Smarter Decisions</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>