/**
 * Unified Layout Component for AgriIntel
 * 
 * Provides consistent layout structure, typography, and visual design
 * across all pages based on the reference design pattern.
 */

import React from 'react';
import { Box, Container, Typography, Paper, useTheme, alpha } from '@mui/material';
import { styled } from '@mui/material/styles';

interface UnifiedLayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  showBackground?: boolean;
  backgroundImage?: string;
  backgroundPosition?: 'left' | 'right' | 'full';
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | false;
  contentPadding?: number;
}

// 2029 Quantum Consciousness Styled components
const LayoutContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  display: 'flex',
  position: 'relative',
  fontFamily: 'var(--font-quantum)',
  background: 'var(--quantum-abyss)',
  color: 'var(--quantum-text)',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'fixed',
    inset: 0,
    background: `
      radial-gradient(ellipse at 15% 85%, rgba(0, 255, 255, 0.15) 0%, transparent 40%),
      radial-gradient(ellipse at 85% 15%, rgba(255, 0, 255, 0.12) 0%, transparent 45%),
      radial-gradient(ellipse at 50% 50%, rgba(255, 255, 0, 0.08) 0%, transparent 35%)
    `,
    pointerEvents: 'none',
    zIndex: -1,
  }
}));

const BackgroundSection = styled(Box)<{ backgroundImage?: string; position: 'left' | 'right' | 'full' }>(
  ({ theme, backgroundImage, position }) => ({
    position: 'absolute',
    top: 0,
    bottom: 0,
    width: position === 'full' ? '100%' : '50%',
    left: position === 'left' ? 0 : position === 'right' ? '50%' : 0,
    right: position === 'right' ? 0 : position === 'left' ? '50%' : 0,
    backgroundImage: backgroundImage ? `url(${backgroundImage})` : 'none',
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
    zIndex: 1,
    borderRadius: position === 'full' ? 0 : position === 'left' ? '0 var(--radius-3xl) var(--radius-3xl) 0' : 'var(--radius-3xl) 0 0 var(--radius-3xl)',
    overflow: 'hidden',
    '&::before': {
      content: '""',
      position: 'absolute',
      inset: 0,
      background: `
        radial-gradient(circle at 30% 70%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(255, 0, 255, 0.08) 0%, transparent 50%)
      `,
      zIndex: 1,
    },
    '&::after': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: position === 'full'
        ? `
          linear-gradient(135deg,
            rgba(0, 0, 0, 0.6) 0%,
            rgba(0, 255, 255, 0.1) 25%,
            rgba(255, 0, 255, 0.1) 50%,
            rgba(0, 0, 0, 0.4) 100%
          )
        `
        : `
          linear-gradient(135deg,
            rgba(0, 0, 0, 0.5) 0%,
            rgba(0, 255, 255, 0.08) 30%,
            rgba(255, 0, 255, 0.08) 70%,
            rgba(0, 0, 0, 0.3) 100%
          )
        `,
      backdropFilter: 'blur(2px) saturate(120%)',
      zIndex: 2,
    }
  })
);

const ContentSection = styled(Box)<{ backgroundPosition: 'left' | 'right' | 'full' }>(
  ({ theme, backgroundPosition }) => ({
    position: 'relative',
    zIndex: 2,
    width: backgroundPosition === 'full' ? '100%' : '50%',
    marginLeft: backgroundPosition === 'left' ? '50%' : 0,
    marginRight: backgroundPosition === 'right' ? '50%' : 0,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing(4),
    [theme.breakpoints.down('md')]: {
      width: '100%',
      marginLeft: 0,
      marginRight: 0,
      padding: theme.spacing(2),
    },
  })
);

const ContentCard = styled(Paper)(({ theme }) => ({
  padding: 'var(--space-3xl)',
  borderRadius: 'var(--radius-consciousness)',
  background: 'var(--bio-glass-strong)',
  backdropFilter: 'var(--bio-backdrop-intense)',
  WebkitBackdropFilter: 'var(--bio-backdrop-intense)',
  border: '1px solid var(--bio-border-medium)',
  boxShadow: 'var(--shadow-consciousness)',
  width: '100%',
  maxWidth: '600px',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    inset: 0,
    background: 'var(--neural-consciousness)',
    opacity: 0.03,
    borderRadius: 'inherit',
    animation: 'holographic-consciousness 12s ease infinite',
  },
  '&:hover': {
    transform: 'translateY(-4px) scale(1.01)',
    boxShadow: 'var(--shadow-floating)',
    borderColor: 'var(--bio-border-strong)',
    transition: 'all var(--timing-consciousness)',
  },
  [theme.breakpoints.down('sm')]: {
    padding: 'var(--space-2xl)',
    margin: 'var(--space-md)',
    maxWidth: '95%',
  },
}));

const StyledTitle = styled(Typography)(({ theme }) => ({
  fontFamily: 'var(--font-quantum)',
  fontWeight: 800,
  fontSize: 'clamp(2rem, 5vw, 3.5rem)',
  lineHeight: 1.1,
  color: 'var(--quantum-text)',
  marginBottom: 'var(--space-lg)',
  textAlign: 'center',
  fontVariationSettings: '"wght" 800',
  letterSpacing: '-0.03em',
  background: 'var(--neural-consciousness)',
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  backgroundClip: 'text',
  backgroundSize: '400% 400%',
  animation: 'holographic-consciousness 8s ease infinite',
  textShadow: '0 0 20px rgba(0, 255, 255, 0.3)',
  filter: 'drop-shadow(0 0 10px rgba(0, 255, 255, 0.2))',
  [theme.breakpoints.down('md')]: {
    fontSize: 'clamp(1.75rem, 4vw, 2.5rem)',
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: 'clamp(1.5rem, 3.5vw, 2rem)',
  },
}));

const StyledSubtitle = styled(Typography)(({ theme }) => ({
  fontFamily: 'var(--font-quantum)',
  fontWeight: 500,
  fontSize: 'clamp(1rem, 2.5vw, 1.25rem)',
  lineHeight: 1.5,
  color: 'var(--quantum-text-secondary)',
  marginBottom: 'var(--space-2xl)',
  textAlign: 'center',
  fontVariationSettings: '"wght" 500',
  letterSpacing: '-0.01em',
  background: `conic-gradient(from 45deg,
    rgba(0, 255, 255, 0.9),
    rgba(255, 0, 255, 0.8),
    rgba(255, 255, 0, 0.9)
  )`,
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  backgroundClip: 'text',
  backgroundSize: '200% 200%',
  animation: 'holographic-consciousness 10s ease infinite',
  [theme.breakpoints.down('sm')]: {
    fontSize: 'clamp(0.9rem, 2vw, 1.1rem)',
  },
}));

const FullWidthContainer = styled(Container)(({ theme }) => ({
  maxWidth: '100% !important',
  padding: theme.spacing(3),
  position: 'relative',
  zIndex: 2,
  [theme.breakpoints.down('md')]: {
    padding: theme.spacing(2),
  },
}));

const UnifiedLayout: React.FC<UnifiedLayoutProps> = ({
  children,
  title,
  subtitle,
  showBackground = true,
  backgroundImage = '/images/modules/animals/cattle-1.jpeg',
  backgroundPosition = 'right',
  maxWidth = 'lg',
  contentPadding = 6,
}) => {
  const theme = useTheme();

  // For full-width layouts (like dashboard)
  if (maxWidth === false) {
    return (
      <LayoutContainer>
        {showBackground && (
          <BackgroundSection
            backgroundImage={backgroundImage}
            position={backgroundPosition}
          />
        )}
        <FullWidthContainer>
          {title && (
            <Box sx={{ mb: 4, textAlign: 'center' }}>
              <StyledTitle variant="h1">{title}</StyledTitle>
              {subtitle && <StyledSubtitle variant="body1">{subtitle}</StyledSubtitle>}
            </Box>
          )}
          {children}
        </FullWidthContainer>
      </LayoutContainer>
    );
  }

  // For card-based layouts (like login)
  return (
    <LayoutContainer>
      {showBackground && (
        <BackgroundSection
          backgroundImage={backgroundImage}
          position={backgroundPosition}
        />
      )}
      <ContentSection backgroundPosition={backgroundPosition}>
        <ContentCard elevation={0}>
          {title && <StyledTitle variant="h1">{title}</StyledTitle>}
          {subtitle && <StyledSubtitle variant="body1">{subtitle}</StyledSubtitle>}
          {children}
        </ContentCard>
      </ContentSection>
    </LayoutContainer>
  );
};

export default UnifiedLayout;
