/**
 * Navigation Component Styles
 * Professional navigation for AgriIntel platform
 */

/* ===== MAIN NAVIGATION ===== */

.agri-navigation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-navigation);
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border-bottom: 1px solid var(--glass-border);
  padding: var(--spacing-md) 0;
  transition: var(--transition-normal);
}

.agri-navigation.scrolled {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: var(--shadow-md);
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

/* ===== BRAND SECTION ===== */

.nav-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  text-decoration: none;
  color: inherit;
}

.nav-brand:hover {
  text-decoration: none;
}

.nav-logo {
  height: 48px;
  width: auto;
  object-fit: contain;
  transition: var(--transition-normal);
}

.nav-logo:hover {
  transform: scale(1.05);
}

.nav-brand-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.nav-brand-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0;
  line-height: 1.2;
}

.nav-brand-subtitle {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin: 0;
  line-height: 1;
}

/* ===== NAVIGATION MENU ===== */

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-menu-item {
  position: relative;
}

.nav-menu-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--color-text-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-base);
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
}

.nav-menu-link:hover {
  background: var(--color-background-hover);
  color: var(--agri-primary);
  text-decoration: none;
}

.nav-menu-link.active {
  background: var(--agri-primary);
  color: var(--color-white);
}

/* ===== NAVIGATION CONTROLS ===== */

.nav-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.nav-control-item {
  display: flex;
  align-items: center;
}

/* Language Selector */
.nav-language-selector {
  min-width: 120px;
}

.nav-language-selector .MuiSelect-root {
  color: var(--color-text-primary);
  border-color: var(--color-border);
  background: var(--color-background-primary);
}

.nav-language-selector .MuiSelect-root:hover {
  border-color: var(--agri-primary);
}

/* Theme Toggle */
.nav-theme-toggle {
  color: var(--color-text-primary);
  background: var(--color-background-primary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm);
  transition: var(--transition-normal);
}

.nav-theme-toggle:hover {
  background: var(--color-background-hover);
  border-color: var(--agri-primary);
  transform: scale(1.05);
}

/* ===== NAVIGATION ACTIONS ===== */

.nav-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.nav-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-lg);
  font-family: inherit;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  text-decoration: none;
  text-transform: none;
  cursor: pointer;
  transition: var(--transition-normal);
  -webkit-user-select: none;
  user-select: none;
  white-space: nowrap;
}

.nav-button:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}

.nav-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* Button Variants */
.nav-button-outline {
  background: transparent;
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.nav-button-outline:hover {
  background: var(--color-background-hover);
  border-color: var(--agri-primary);
  color: var(--agri-primary);
  transform: translateY(-1px);
}

.nav-button-primary {
  background: var(--agri-pro-gradient);
  color: var(--color-white);
  border: none;
  box-shadow: 0 4px 15px rgba(46, 125, 50, 0.3);
}

.nav-button-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(46, 125, 50, 0.4);
}

.nav-button-secondary {
  background: var(--agri-beta-gradient);
  color: var(--color-white);
  border: none;
  box-shadow: 0 4px 15px rgba(245, 124, 0, 0.3);
}

.nav-button-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(245, 124, 0, 0.4);
}

/* ===== MOBILE NAVIGATION ===== */

.nav-mobile-toggle {
  display: none;
  background: none;
  border: none;
  color: var(--color-text-primary);
  font-size: var(--font-size-xl);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
}

.nav-mobile-toggle:hover {
  background: var(--color-background-hover);
}

.nav-mobile-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--color-background-primary);
  border: 1px solid var(--color-border);
  border-top: none;
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-lg);
  display: none;
}

.nav-mobile-menu.open {
  display: block;
}

.nav-mobile-menu .nav-menu {
  flex-direction: column;
  align-items: stretch;
  gap: var(--spacing-sm);
}

.nav-mobile-menu .nav-menu-link {
  justify-content: flex-start;
  padding: var(--spacing-md);
}

.nav-mobile-menu .nav-controls {
  flex-direction: column;
  align-items: stretch;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--color-border);
}

.nav-mobile-menu .nav-actions {
  flex-direction: column;
  align-items: stretch;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
}

.nav-mobile-menu .nav-button {
  justify-content: center;
  width: 100%;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
  .nav-container {
    padding: 0 var(--spacing-md);
  }
  
  .nav-menu,
  .nav-controls,
  .nav-actions {
    display: none;
  }
  
  .nav-mobile-toggle {
    display: block;
  }
  
  .nav-brand-text {
    display: none;
  }
  
  .nav-logo {
    height: 40px;
  }
}

@media (max-width: 480px) {
  .nav-container {
    padding: 0 var(--spacing-sm);
  }
  
  .nav-logo {
    height: 36px;
  }
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  .nav-logo:hover,
  .nav-button:hover,
  .nav-menu-link:hover,
  .nav-theme-toggle:hover {
    transform: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .agri-navigation {
    background: var(--color-background-primary);
    border-bottom: 2px solid var(--color-text-primary);
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
  }
  
  .nav-button-primary,
  .nav-button-secondary {
    background: var(--color-text-primary);
    color: var(--color-background-primary);
  }
}
