import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  TextField,
  Button,
  Typography,
  Alert,
  Chip,
  useTheme,
  alpha,
  InputAdornment,
  IconButton,
  CircularProgress,
  Divider
} from '@mui/material';
import {
  Person,
  Lock,
  Visibility,
  VisibilityOff,
  Agriculture,
  CheckCircle
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import UnifiedLayout from '../components/layout/UnifiedLayout';
import LanguageSelector from '../components/common/LanguageSelector';

const BetaV1Login: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { login } = useAuth();
  const theme = useTheme();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!username.trim() || !password.trim()) {
      setError('Please enter both username and password');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      await login(username, password);
      // Navigate to beta dashboard
      navigate('/dashboard', { replace: true });
    } catch (err: any) {
      setError(err.message || 'Login failed. Please check your credentials.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <UnifiedLayout
      title="Welcome to AgriIntel BETA V1"
      subtitle="Smart Farming, Smarter Decisions - Free Trial Access"
      backgroundImage="/images/modules/animals/cattle-1.jpeg"
      backgroundPosition="left"
      showBackground={true}
    >
      {/* Language Selector */}
      <Box display="flex" justifyContent="flex-end" mb={2}>
        <LanguageSelector variant="compact" showLabel={false} size="small" />
      </Box>

      <Box textAlign="center" mb={4}>
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.5, type: "spring", stiffness: 200 }}
        >
          <Box
            sx={{
              width: 80,
              height: 80,
              borderRadius: '50%',
              background: 'linear-gradient(135deg, #F59E0B, #D97706)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 16px',
              boxShadow: '0 8px 32px rgba(245, 158, 11, 0.3)',
            }}
          >
            <Agriculture sx={{ fontSize: 40, color: 'white' }} />
          </Box>
        </motion.div>

        <Chip
          label="BETA V1 - FREE TRIAL"
          sx={{
            mb: 2,
            fontWeight: 600,
            background: 'linear-gradient(45deg, #F59E0B, #D97706)',
            color: 'white',
            fontSize: '0.875rem'
          }}
        />

        <Typography
          variant="body2"
          sx={{
            color: theme.palette.text.secondary,
            mb: 2,
          }}
        >
          Experience smart livestock management with 5 core modules
        </Typography>

        {/* Test Credentials */}
        <Box display="flex" gap={1} justifyContent="center" flexWrap="wrap" mb={2}>
          <Chip
            label="Demo: Demo/123"
            size="small"
            variant="outlined"
            onClick={() => { setUsername('Demo'); setPassword('123'); }}
            sx={{ cursor: 'pointer', '&:hover': { backgroundColor: alpha(theme.palette.warning.main, 0.1) } }}
          />
        </Box>
      </Box>

      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Alert
              severity="error"
              sx={{
                mb: 3,
                borderRadius: 2,
                '& .MuiAlert-icon': {
                  fontSize: 20,
                }
              }}
            >
              {error}
            </Alert>
          </motion.div>
        )}
      </AnimatePresence>

      <form onSubmit={handleLogin}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.5 }}
        >
          <TextField
            label="Username"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            fullWidth
            margin="normal"
            variant="outlined"
            required
            autoFocus
            placeholder="Enter Demo for beta access"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Person sx={{ color: theme.palette.warning.main }} />
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
                transition: 'all 0.3s ease',
                '&:hover': {
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.palette.warning.main,
                    borderWidth: 2,
                  },
                },
                '&.Mui-focused': {
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.palette.warning.main,
                    borderWidth: 2,
                    boxShadow: `0 0 0 3px ${alpha(theme.palette.warning.main, 0.1)}`,
                  },
                },
              },
            }}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7, duration: 0.5 }}
        >
          <TextField
            label="Password"
            type={showPassword ? 'text' : 'password'}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            fullWidth
            margin="normal"
            variant="outlined"
            required
            placeholder="Enter 123 for beta access"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Lock sx={{ color: theme.palette.warning.main }} />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => setShowPassword(!showPassword)}
                    edge="end"
                    sx={{
                      color: theme.palette.text.secondary,
                      '&:hover': {
                        color: theme.palette.warning.main,
                      }
                    }}
                  >
                    {showPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
                transition: 'all 0.3s ease',
                '&:hover': {
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.palette.warning.main,
                    borderWidth: 2,
                  },
                },
                '&.Mui-focused': {
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.palette.warning.main,
                    borderWidth: 2,
                    boxShadow: `0 0 0 3px ${alpha(theme.palette.warning.main, 0.1)}`,
                  },
                },
              },
            }}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.5 }}
        >
          <Button
            type="submit"
            variant="contained"
            fullWidth
            disabled={isLoading}
            sx={{
              mt: 3,
              mb: 2,
              height: 56,
              borderRadius: 2,
              fontSize: '1rem',
              fontWeight: 600,
              background: 'linear-gradient(135deg, #F59E0B 0%, #D97706 100%)',
              boxShadow: '0 8px 32px rgba(245, 158, 11, 0.3)',
              transition: 'all 0.3s ease',
              '&:hover': {
                background: 'linear-gradient(135deg, #D97706 0%, #B45309 100%)',
                transform: 'translateY(-2px)',
                boxShadow: '0 12px 40px rgba(245, 158, 11, 0.4)',
              },
              '&:disabled': {
                background: 'linear-gradient(135deg, #9E9E9E 0%, #BDBDBD 100%)',
              },
            }}
          >
            {isLoading ? (
              <CircularProgress size={24} sx={{ color: 'white' }} />
            ) : (
              'ACCESS BETA V1 DASHBOARD'
            )}
          </Button>
        </motion.div>
      </form>

      <Divider sx={{ my: 3 }}>
        <Typography variant="caption" sx={{ color: theme.palette.text.secondary }}>
          Beta V1 Access
        </Typography>
      </Divider>

      <Box textAlign="center">
        <Typography variant="caption" sx={{ color: theme.palette.text.secondary, mb: 2, display: 'block' }}>
          5 Core Modules • 50 Animal Limit • Excel Reports • Free Trial
        </Typography>
        <Button
          variant="text"
          onClick={() => navigate('/')}
          sx={{
            color: theme.palette.warning.main,
            '&:hover': { backgroundColor: alpha(theme.palette.warning.main, 0.1) }
          }}
        >
          Back to Home
        </Button>
      </Box>
    </UnifiedLayout>
  );
};

export default BetaV1Login;
