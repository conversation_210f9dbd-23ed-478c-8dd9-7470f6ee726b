import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Container,
  Alert,
  Chip,
  Paper,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Stack,
  Divider,
  useTheme,
  alpha
} from '@mui/material';
import {
  CheckCircle,
  Star,
  Agriculture,
  HealthAndSafety,
  Restaurant,
  Assessment,
  Support,
  PhoneAndroid,
  Lock,
  Upgrade,
  Dashboard,
  TrendingUp
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import '../styles/beta-v1-login.css';

const BetaV1Login: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { login } = useAuth();
  const theme = useTheme();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!username.trim() || !password.trim()) {
      setError('Please enter both username and password');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      await login(username, password);
      // Navigate to beta dashboard
      navigate('/dashboard', { replace: true });
    } catch (err: any) {
      setError(err.message || 'Login failed. Please check your credentials.');
    } finally {
      setIsLoading(false);
    }
  };

  const betaFeatures = [
    { icon: <Dashboard />, text: 'Basic Dashboard Overview', available: true },
    { icon: <Agriculture />, text: 'Animal Management (50 max)', available: true },
    { icon: <HealthAndSafety />, text: 'Basic Health Monitoring', available: true },
    { icon: <Restaurant />, text: 'Simple Feed Management', available: true },
    { icon: <TrendingUp />, text: 'Basic Financial Tracking', available: true },
    { icon: <Assessment />, text: 'Excel-only Reports', available: true },
    { icon: <Support />, text: 'Email Support Only', available: true }
  ];

  const lockedFeatures = [
    'AI-Powered Automation',
    'Marketplace Connections',
    'Mobile App Access',
    'Advanced Analytics',
    'Priority Support',
    'Unlimited Animals'
  ];

  return (
    <Box className="beta-v1-login-container">
      {/* Background with Authentic South African Livestock Photography */}
      <div className="login-background login-background-dynamic">
        <div className="background-overlay-beta" />
      </div>

      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1, py: 4 }}>
        <Grid container spacing={4} alignItems="center" sx={{ minHeight: '100vh' }}>
          {/* Left Side - Login Form */}
          <Grid item xs={12} md={6}>
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Paper className="glass-card-beta" sx={{ p: 4, maxWidth: 480, mx: 'auto' }}>
                {/* BETA V1 Branding */}
                <Box sx={{ textAlign: 'center', mb: 4 }}>
                  <img
                    src={`${process.env.PUBLIC_URL}/images/logo/AgriIntel Logo with Bright Accents and Livestock.png`}
                    alt="AgriIntel Logo"
                    className="agri-logo"
                  />
                  <Chip
                    label="BETA V1"
                    sx={{
                      background: 'var(--agri-beta-gradient)',
                      color: 'white',
                      fontWeight: 700,
                      fontSize: '1rem',
                      mb: 2
                    }}
                  />
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'var(--agri-white)', mb: 1 }}>
                    Welcome to AgriIntel BETA
                  </Typography>
                  <Typography variant="body1" sx={{ color: 'var(--agri-white)', opacity: 0.9 }}>
                    Smart Farming, Smarter Decisions - Free Trial
                  </Typography>
                </Box>

                {/* Login Form */}
                <form onSubmit={handleLogin}>
                  <Stack spacing={3}>
                    {error && (
                      <Alert severity="error" sx={{ borderRadius: 'var(--radius-lg)' }}>
                        {error}
                      </Alert>
                    )}

                    <TextField
                      fullWidth
                      label="Username"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      variant="outlined"
                      required
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'rgba(255, 255, 255, 0.1)',
                          '& fieldset': { borderColor: 'var(--glass-border)' },
                          '&:hover fieldset': { borderColor: 'var(--agri-beta-primary)' },
                          '&.Mui-focused fieldset': { borderColor: 'var(--agri-beta-primary)' }
                        },
                        '& .MuiInputLabel-root': { color: 'var(--agri-white)' },
                        '& .MuiInputBase-input': { color: 'var(--agri-white)' }
                      }}
                    />

                    <TextField
                      fullWidth
                      label="Password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      variant="outlined"
                      required
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'rgba(255, 255, 255, 0.1)',
                          '& fieldset': { borderColor: 'var(--glass-border)' },
                          '&:hover fieldset': { borderColor: 'var(--agri-beta-primary)' },
                          '&.Mui-focused fieldset': { borderColor: 'var(--agri-beta-primary)' }
                        },
                        '& .MuiInputLabel-root': { color: 'var(--agri-white)' },
                        '& .MuiInputBase-input': { color: 'var(--agri-white)' }
                      }}
                    />

                    <Button
                      type="submit"
                      fullWidth
                      variant="contained"
                      size="large"
                      disabled={isLoading}
                      sx={{
                        background: 'var(--agri-beta-gradient)',
                        color: 'white',
                        fontWeight: 600,
                        fontSize: '1.1rem',
                        py: 1.5,
                        borderRadius: 'var(--radius-lg)',
                        textTransform: 'none',
                        '&:hover': {
                          background: 'var(--agri-beta-gradient)',
                          transform: 'translateY(-2px)',
                          boxShadow: '0 8px 25px rgba(245, 124, 0, 0.3)'
                        }
                      }}
                    >
                      {isLoading ? 'Signing In...' : 'Start Free Trial'}
                    </Button>
                  </Stack>
                </form>

                <Divider sx={{ my: 3, borderColor: 'var(--glass-border)' }} />

                {/* Demo Credentials */}
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="body2" sx={{ color: 'var(--agri-white)', opacity: 0.8, mb: 1 }}>
                    Demo Credentials:
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'var(--agri-beta-primary)', fontWeight: 600 }}>
                    Username: Demo | Password: 123
                  </Typography>
                </Box>

                {/* Upgrade Prompt */}
                <Box sx={{ mt: 3, textAlign: 'center' }}>
                  <Typography variant="body2" sx={{ color: 'var(--agri-white)', opacity: 0.8, mb: 2 }}>
                    Need full access?
                  </Typography>
                  <Button
                    variant="outlined"
                    onClick={() => navigate('/login-pro')}
                    sx={{
                      color: 'var(--agri-pro-primary)',
                      borderColor: 'var(--agri-pro-primary)',
                      '&:hover': {
                        backgroundColor: 'rgba(46, 125, 50, 0.1)',
                        borderColor: 'var(--agri-pro-primary)'
                      }
                    }}
                  >
                    Upgrade to Professional V1
                  </Button>
                </Box>
              </Paper>
            </motion.div>
          </Grid>

          {/* Right Side - Features Overview */}
          <Grid item xs={12} md={6}>
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Box sx={{ color: 'var(--agri-white)' }}>
                <Typography variant="h3" sx={{ fontWeight: 700, mb: 2 }}>
                  BETA V1 Features
                </Typography>
                <Typography variant="h6" sx={{ opacity: 0.9, mb: 4, fontSize: 'var(--font-size-base)' }}>
                  Perfect starter kit for small-scale South African farmers
                </Typography>

                {/* Available Features */}
                <Paper className="glass-card-beta" sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" sx={{ color: 'var(--agri-white)', mb: 2, fontWeight: 600 }}>
                    ✅ Included in BETA V1
                  </Typography>
                  <List dense>
                    {betaFeatures.map((feature, index) => (
                      <ListItem key={index} sx={{ px: 0 }}>
                        <ListItemIcon sx={{ color: 'var(--agri-beta-primary)', minWidth: 36 }}>
                          {feature.icon}
                        </ListItemIcon>
                        <ListItemText
                          primary={feature.text}
                          sx={{ '& .MuiListItemText-primary': { color: 'var(--agri-white)', fontSize: 'var(--font-size-base)' } }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Paper>

                {/* Locked Features */}
                <Paper className="glass-card-beta" sx={{ p: 3, opacity: 0.7 }}>
                  <Typography variant="h6" sx={{ color: 'var(--agri-white)', mb: 2, fontWeight: 600 }}>
                    🔒 Upgrade to Professional V1 for:
                  </Typography>
                  <List dense>
                    {lockedFeatures.map((feature, index) => (
                      <ListItem key={index} sx={{ px: 0 }}>
                        <ListItemIcon sx={{ color: 'var(--agri-medium-gray)', minWidth: 36 }}>
                          <Lock />
                        </ListItemIcon>
                        <ListItemText
                          primary={feature}
                          sx={{ '& .MuiListItemText-primary': { color: 'var(--agri-medium-gray)', fontSize: 'var(--font-size-base)' } }}
                        />
                      </ListItem>
                    ))}
                  </List>
                  <Button
                    variant="contained"
                    startIcon={<Upgrade />}
                    onClick={() => navigate('/login-pro')}
                    sx={{
                      mt: 2,
                      background: 'var(--agri-pro-gradient)',
                      color: 'white',
                      fontWeight: 600,
                      textTransform: 'none'
                    }}
                  >
                    Upgrade to Professional V1
                  </Button>
                </Paper>
              </Box>
            </motion.div>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default BetaV1Login;
