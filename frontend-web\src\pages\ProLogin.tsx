import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import {
  <PERSON><PERSON>graphy,
  TextField,
  Button,
  Alert,
  InputAdornment,
  IconButton,
  CircularProgress,
  Box,
  useTheme,
  alpha,
  Chip,
  Divider,
  Card,
  CardContent,
  Grid
} from '@mui/material';
import {
  Person,
  Lock,
  Visibility,
  VisibilityOff,
  Psychology,
  AutoAwesome,
  SmartToy,
  Analytics
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import LanguageSelector from '../components/common/LanguageSelector';
import UnifiedLayout from '../components/layout/UnifiedLayout';

const ProLogin: React.FC = () => {
  const [username, setUsername] = useState('Pro');
  const [password, setPassword] = useState('123');
  const [showPassword, setShowPassword] = useState(false);
  const { login, isLoading, error } = useAuth();
  const { translate } = useLanguage();
  const navigate = useNavigate();
  const location = useLocation();

  // Get the intended destination or default to dashboard
  const from = location.state?.from || '/dashboard';

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!username.trim() || !password.trim()) return;

    try {
      // For Pro login, we'll set a special enterprise role with AI features
      if (username === 'Pro' && password === '123') {
        // Store user info in localStorage with enterprise role
        localStorage.setItem('user', JSON.stringify({
          username: 'Pro',
          role: 'enterprise',
          permissions: ['all'],
          features: ['ai_automation', 'predictive_analysis', 'one_click_tasks', 'auto_ordering', 'auto_appointments']
        }));
        
        // Redirect to dashboard with AI features
        navigate('/dashboard', { replace: true });
      } else {
        await login(username, password);
        navigate('/dashboard', { replace: true });
      }
    } catch (err) {
      console.error('Login failed:', err);
    }
  };

  const theme = useTheme();

  const aiFeatures = [
    {
      icon: <Psychology sx={{ fontSize: 24, color: '#9C27B0' }} />,
      title: 'AI Analysis',
      description: 'Comprehensive module analysis with predictive insights'
    },
    {
      icon: <AutoAwesome sx={{ fontSize: 24, color: '#FF9800' }} />,
      title: 'One-Click Tasks',
      description: 'Automated feed ordering and vet appointment booking'
    },
    {
      icon: <SmartToy sx={{ fontSize: 24, color: '#2196F3' }} />,
      title: 'AI Agents',
      description: 'Intelligent automation for routine farm operations'
    },
    {
      icon: <Analytics sx={{ fontSize: 24, color: '#4CAF50' }} />,
      title: 'Predictive Analytics',
      description: 'Advanced forecasting and preventive recommendations'
    }
  ];

  return (
    <UnifiedLayout
      title="Welcome to AgriIntel PRO"
      subtitle="Enterprise AI-Powered Livestock Management"
      backgroundImage="/images/modules/animals/cattle-3.jpeg"
      backgroundPosition="center"
      showBackground={true}
    >
      {/* Language Selector */}
      <Box display="flex" justifyContent="flex-end" mb={2}>
        <LanguageSelector variant="compact" showLabel={false} size="small" />
      </Box>

      <Box textAlign="center" mb={4}>
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.5, type: "spring", stiffness: 200 }}
        >
          <Box
            sx={{
              width: 80,
              height: 80,
              borderRadius: '50%',
              background: 'linear-gradient(135deg, #9C27B0, #7B1FA2)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 16px',
              boxShadow: '0 8px 32px rgba(156, 39, 176, 0.3)',
            }}
          >
            <Psychology sx={{ fontSize: 40, color: 'white' }} />
          </Box>
        </motion.div>

        {/* Pro Label */}
        <Chip 
          label="ENTERPRISE PRO" 
          size="small" 
          sx={{ 
            mb: 2,
            fontWeight: 'bold',
            background: 'linear-gradient(45deg, #9C27B0, #7B1FA2)',
            color: 'white'
          }} 
        />

        <Typography
          variant="h4"
          sx={{
            fontWeight: 700,
            color: theme.palette.text.primary,
            mb: 1,
          }}
        >
          Sign In to Enterprise Pro
        </Typography>

        <Typography
          variant="body2"
          sx={{
            color: theme.palette.text.secondary,
            mb: 2,
          }}
        >
          Access AI automation, predictive analytics, and one-click task management
        </Typography>

        {/* Pro Credentials */}
        <Box display="flex" gap={1} justifyContent="center" flexWrap="wrap" mb={2}>
          <Chip
            label="Pro Access: Pro/123"
            size="small"
            variant="outlined"
            onClick={() => { setUsername('Pro'); setPassword('123'); }}
            sx={{ 
              cursor: 'pointer', 
              '&:hover': { backgroundColor: alpha('#9C27B0', 0.1) },
              borderColor: '#9C27B0',
              color: '#9C27B0'
            }}
          />
        </Box>
      </Box>

      {/* AI Features Preview */}
      <Box mb={4}>
        <Typography variant="h6" sx={{ mb: 2, textAlign: 'center', color: theme.palette.text.primary }}>
          Enterprise Pro AI Features
        </Typography>
        <Grid container spacing={2}>
          {aiFeatures.map((feature, index) => (
            <Grid item xs={12} sm={6} key={index}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 + index * 0.1 }}
              >
                <Card sx={{ 
                  background: alpha(theme.palette.background.paper, 0.8),
                  backdropFilter: 'blur(10px)',
                  border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
                }}>
                  <CardContent sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      {feature.icon}
                      <Typography variant="subtitle2" fontWeight="bold">
                        {feature.title}
                      </Typography>
                    </Box>
                    <Typography variant="caption" color="text.secondary">
                      {feature.description}
                    </Typography>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          ))}
        </Grid>
      </Box>

      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Alert
              severity="error"
              sx={{
                mb: 3,
                borderRadius: 2,
                '& .MuiAlert-icon': {
                  fontSize: 20,
                }
              }}
            >
              {error}
            </Alert>
          </motion.div>
        )}
      </AnimatePresence>

      <form onSubmit={handleLogin}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.5 }}
        >
          <TextField
            label="Username"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            fullWidth
            margin="normal"
            variant="outlined"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Person sx={{ color: '#9C27B0' }} />
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
                transition: 'all 0.3s ease',
                '&:hover': {
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#9C27B0',
                    borderWidth: 2,
                  },
                },
                '&.Mui-focused': {
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#9C27B0',
                    borderWidth: 2,
                    boxShadow: `0 0 0 3px ${alpha('#9C27B0', 0.1)}`,
                  },
                },
              },
            }}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7, duration: 0.5 }}
        >
          <TextField
            label="Password"
            type={showPassword ? 'text' : 'password'}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            fullWidth
            margin="normal"
            variant="outlined"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Lock sx={{ color: '#9C27B0' }} />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => setShowPassword(!showPassword)}
                    edge="end"
                    sx={{
                      color: theme.palette.text.secondary,
                      '&:hover': {
                        color: '#9C27B0',
                      }
                    }}
                  >
                    {showPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
                transition: 'all 0.3s ease',
                '&:hover': {
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#9C27B0',
                    borderWidth: 2,
                  },
                },
                '&.Mui-focused': {
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#9C27B0',
                    borderWidth: 2,
                    boxShadow: `0 0 0 3px ${alpha('#9C27B0', 0.1)}`,
                  },
                },
              },
            }}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.5 }}
        >
          <Button
            type="submit"
            variant="contained"
            fullWidth
            disabled={isLoading}
            sx={{
              mt: 3,
              mb: 2,
              height: 56,
              borderRadius: 2,
              fontSize: '1rem',
              fontWeight: 600,
              background: 'linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%)',
              boxShadow: '0 8px 32px rgba(156, 39, 176, 0.3)',
              transition: 'all 0.3s ease',
              '&:hover': {
                background: 'linear-gradient(135deg, #7B1FA2 0%, #6A1B9A 100%)',
                transform: 'translateY(-2px)',
                boxShadow: '0 12px 40px rgba(156, 39, 176, 0.4)',
              },
              '&:disabled': {
                background: 'linear-gradient(135deg, #9E9E9E 0%, #BDBDBD 100%)',
              },
            }}
          >
            {isLoading ? (
              <CircularProgress size={24} sx={{ color: 'white' }} />
            ) : (
              '🚀 ACCESS ENTERPRISE PRO'
            )}
          </Button>
        </motion.div>
      </form>

      <Divider sx={{ my: 3 }}>
        <Typography variant="caption" sx={{ color: theme.palette.text.secondary }}>
          Enterprise Pro Access
        </Typography>
      </Divider>

      <Box textAlign="center">
        <Typography variant="caption" sx={{ color: theme.palette.text.secondary }}>
          AI-powered automation with predictive analytics and one-click operations
        </Typography>
      </Box>
    </UnifiedLayout>
  );
};

export default ProLogin;
