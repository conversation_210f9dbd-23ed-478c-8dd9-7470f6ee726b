// Import polyfills first
import './setupPolyfills';

// Import direct patches
import './patches/react-big-calendar-patch';
import './patches/mui-theme-patch';
import './patches/mui-button-patch';
import { applyMuiPatches } from './patches/direct-mui-patch';
import { fixButtonComponent } from './patches/runtime-button-fix';
import './patches/button-monkey-patch';

// Import ResizeObserver fixes
import { patchResizeObserver, applyChartFixes } from './utils/resizeObserverFix';
import { applyEnhancedResizeObserverFix } from './patches/resize-observer-patch';
import './styles/resizeObserverFix.css';
import './utils/resizeObserverTest';

import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import './styles/animations.css';
import './styles/TabFixes.css';
import App from './App';
import reportWebVitals from './reportWebVitals';



// Apply ResizeObserver fixes immediately and aggressively
patchResizeObserver();
applyChartFixes();
applyEnhancedResizeObserverFix();

// Apply additional aggressive ResizeObserver suppression
const suppressResizeObserverErrors = () => {
  // Suppress all ResizeObserver loop errors globally
  const originalError = console.error;
  console.error = (...args) => {
    if (args[0] && typeof args[0] === 'string' && args[0].includes('ResizeObserver loop')) {
      return; // Completely ignore
    }
    originalError.apply(console, args);
  };

  // Suppress ResizeObserver errors in error events
  window.addEventListener('error', (event) => {
    if (event.message && event.message.includes('ResizeObserver loop')) {
      event.preventDefault();
      event.stopPropagation();
      return false;
    }
  }, true);

  // Suppress ResizeObserver errors in unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    if (event.reason &&
        typeof event.reason === 'object' &&
        event.reason.message &&
        event.reason.message.includes('ResizeObserver loop')) {
      event.preventDefault();
    }
  });
};

suppressResizeObserverErrors();

// Additional comprehensive ResizeObserver error suppression
const setupComprehensiveResizeObserverSuppression = () => {
  // Override the ResizeObserver constructor to add error handling
  if (typeof window.ResizeObserver !== 'undefined') {
    const OriginalResizeObserver = window.ResizeObserver;

    window.ResizeObserver = class extends OriginalResizeObserver {
      constructor(callback: ResizeObserverCallback) {
        const wrappedCallback: ResizeObserverCallback = (entries, observer) => {
          try {
            // Use a microtask to prevent loops
            Promise.resolve().then(() => {
              try {
                callback(entries, observer);
              } catch (error: any) {
                // Silently ignore ResizeObserver loop errors
                if (!(error?.message?.includes('ResizeObserver loop'))) {
                  throw error;
                }
              }
            });
          } catch (error: any) {
            // Silently ignore ResizeObserver loop errors
            if (!(error?.message?.includes('ResizeObserver loop'))) {
              throw error;
            }
          }
        };

        super(wrappedCallback);
      }
    };
  }

  // Comprehensive error event suppression
  const suppressError = (event: ErrorEvent) => {
    if (event.message && event.message.includes('ResizeObserver loop')) {
      event.preventDefault();
      event.stopPropagation();
      return false;
    }
  };

  window.addEventListener('error', suppressError, true);

  // Suppress console methods more aggressively
  const originalMethods = {
    error: console.error,
    warn: console.warn,
    log: console.log
  };

  console.error = (...args) => {
    if (args.some(arg => typeof arg === 'string' && arg.includes('ResizeObserver loop'))) {
      return;
    }
    originalMethods.error.apply(console, args);
  };

  console.warn = (...args) => {
    if (args.some(arg => typeof arg === 'string' && arg.includes('ResizeObserver loop'))) {
      return;
    }
    originalMethods.warn.apply(console, args);
  };

  console.log = (...args) => {
    if (args.some(arg => typeof arg === 'string' && arg.includes('ResizeObserver loop'))) {
      return;
    }
    originalMethods.log.apply(console, args);
  };
};

setupComprehensiveResizeObserverSuppression();

// Initialize font size from localStorage or set to large by default
const initFontSize = () => {
  const savedFontSize = localStorage.getItem('fontSize');
  document.documentElement.style.fontSize = `${savedFontSize || 120}%`;
};
initFontSize();

// Apply MUI patches
setTimeout(() => {
  try {
    applyMuiPatches();
    fixButtonComponent();
  } catch (error) {
    console.error('Error applying MUI patches:', error);
  }
}, 1000);

// Apply button fixes again after a longer delay to ensure all components are loaded
setTimeout(() => {
  try {
    fixButtonComponent();
  } catch (error) {
    console.error('Error applying button fixes:', error);
  }
}, 3000);

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();