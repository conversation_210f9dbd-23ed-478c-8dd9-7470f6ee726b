// Import polyfills first
import './setupPolyfills';

// Import direct patches
import './patches/react-big-calendar-patch';
import './patches/mui-theme-patch';
import './patches/mui-button-patch';
import { applyMuiPatches } from './patches/direct-mui-patch';
import { fixButtonComponent } from './patches/runtime-button-fix';
import './patches/button-monkey-patch';

// Import ResizeObserver fixes
import { patchResizeObserver, applyChartFixes } from './utils/resizeObserverFix';
import { applyEnhancedResizeObserverFix } from './patches/resize-observer-patch';
import './styles/resizeObserverFix.css';
import './utils/resizeObserverTest';

// Import 2026 Modern Design System
import './styles/modern-2026.css';
import './styles/components/cards-2026.css';
import './styles/components/navigation-2026.css';
import './styles/components/forms-2026.css';

import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import './styles/animations.css';
import './styles/TabFixes.css';
import App from './App';
import reportWebVitals from './reportWebVitals';



// Apply the most aggressive ResizeObserver fix first
const applyUltimateResizeObserverFix = () => {
  // Completely override the native ResizeObserver
  if (typeof window.ResizeObserver !== 'undefined') {
    const OriginalResizeObserver = window.ResizeObserver;

    window.ResizeObserver = class UltimateResizeObserver {
      private observer: ResizeObserver | null = null;
      private callback: ResizeObserverCallback;
      private timeoutId: number | null = null;

      constructor(callback: ResizeObserverCallback) {
        this.callback = (entries, observer) => {
          // Clear any pending timeout
          if (this.timeoutId) {
            clearTimeout(this.timeoutId);
          }

          // Use a longer timeout to prevent loops
          this.timeoutId = window.setTimeout(() => {
            try {
              callback(entries, observer);
            } catch (error: any) {
              // Completely suppress ResizeObserver errors
              if (!(error?.message?.includes('ResizeObserver'))) {
                throw error;
              }
            }
          }, 16); // One frame delay
        };

        try {
          this.observer = new OriginalResizeObserver(this.callback);
        } catch (error: any) {
          // Suppress any ResizeObserver creation errors
          if (!(error?.message?.includes('ResizeObserver'))) {
            throw error;
          }
        }
      }

      observe(target: Element, options?: ResizeObserverOptions) {
        if (this.observer) {
          try {
            this.observer.observe(target, options);
          } catch (error: any) {
            // Suppress ResizeObserver errors
            if (!(error?.message?.includes('ResizeObserver'))) {
              throw error;
            }
          }
        }
      }

      unobserve(target: Element) {
        if (this.observer) {
          try {
            this.observer.unobserve(target);
          } catch (error: any) {
            // Suppress ResizeObserver errors
            if (!(error?.message?.includes('ResizeObserver'))) {
              throw error;
            }
          }
        }
      }

      disconnect() {
        if (this.timeoutId) {
          clearTimeout(this.timeoutId);
          this.timeoutId = null;
        }
        if (this.observer) {
          try {
            this.observer.disconnect();
          } catch (error: any) {
            // Suppress ResizeObserver errors
            if (!(error?.message?.includes('ResizeObserver'))) {
              throw error;
            }
          }
        }
      }
    };
  }
};

// Apply the ultimate fix first
applyUltimateResizeObserverFix();

// Apply ResizeObserver fixes immediately and aggressively
patchResizeObserver();
applyChartFixes();
applyEnhancedResizeObserverFix();

// Apply the most aggressive error suppression possible
const suppressAllResizeObserverErrors = () => {
  // Override all console methods
  const originalMethods = {
    error: console.error,
    warn: console.warn,
    log: console.log,
    info: console.info,
    debug: console.debug
  };

  // Patch all console methods to filter ResizeObserver errors
  Object.keys(originalMethods).forEach(method => {
    console[method as keyof Console] = (...args: any[]) => {
      const message = args[0];
      if (typeof message === 'string' &&
          (message.includes('ResizeObserver loop') ||
           message.includes('ResizeObserver') ||
           message.includes('undelivered notifications'))) {
        return; // Completely suppress
      }
      if (typeof message === 'object' && message?.message &&
          typeof message.message === 'string' &&
          (message.message.includes('ResizeObserver loop') ||
           message.message.includes('ResizeObserver') ||
           message.message.includes('undelivered notifications'))) {
        return; // Completely suppress
      }
      (originalMethods[method as keyof typeof originalMethods] as any).apply(console, args);
    };
  });

  // Override window.onerror completely
  window.onerror = (message, source, lineno, colno, error) => {
    if (typeof message === 'string' &&
        (message.includes('ResizeObserver') ||
         message.includes('undelivered notifications'))) {
      return true; // Prevent default error handling
    }
    return false;
  };

  // Override error event listeners
  window.addEventListener('error', (event) => {
    if (event.message &&
        (event.message.includes('ResizeObserver') ||
         event.message.includes('undelivered notifications'))) {
      event.preventDefault();
      event.stopPropagation();
      event.stopImmediatePropagation();
      return false;
    }
  }, true);

  // Override unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    const reason = event.reason;
    if (reason &&
        ((typeof reason === 'string' &&
          (reason.includes('ResizeObserver') || reason.includes('undelivered notifications'))) ||
         (typeof reason === 'object' && reason.message &&
          typeof reason.message === 'string' &&
          (reason.message.includes('ResizeObserver') || reason.message.includes('undelivered notifications'))))) {
      event.preventDefault();
      event.stopPropagation();
      event.stopImmediatePropagation();
    }
  }, true);

  // Override React's error handling
  if (typeof window.__REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined') {
    const originalOnError = window.__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberRoot;
    if (originalOnError) {
      window.__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberRoot = (...args) => {
        try {
          return originalOnError.apply(this, args);
        } catch (error: any) {
          if (error?.message?.includes('ResizeObserver') ||
              error?.message?.includes('undelivered notifications')) {
            return; // Suppress React DevTools errors
          }
          throw error;
        }
      };
    }
  }
};

suppressAllResizeObserverErrors();

// Additional comprehensive ResizeObserver error suppression
const setupComprehensiveResizeObserverSuppression = () => {
  // Override the ResizeObserver constructor to add error handling
  if (typeof window.ResizeObserver !== 'undefined') {
    const OriginalResizeObserver = window.ResizeObserver;

    window.ResizeObserver = class extends OriginalResizeObserver {
      constructor(callback: ResizeObserverCallback) {
        const wrappedCallback: ResizeObserverCallback = (entries, observer) => {
          try {
            // Use a microtask to prevent loops
            Promise.resolve().then(() => {
              try {
                callback(entries, observer);
              } catch (error: any) {
                // Silently ignore ResizeObserver loop errors
                if (!(error?.message?.includes('ResizeObserver loop'))) {
                  throw error;
                }
              }
            });
          } catch (error: any) {
            // Silently ignore ResizeObserver loop errors
            if (!(error?.message?.includes('ResizeObserver loop'))) {
              throw error;
            }
          }
        };

        super(wrappedCallback);
      }
    };
  }

  // Comprehensive error event suppression
  const suppressError = (event: ErrorEvent) => {
    if (event.message && event.message.includes('ResizeObserver loop')) {
      event.preventDefault();
      event.stopPropagation();
      return false;
    }
  };

  window.addEventListener('error', suppressError, true);

  // Suppress console methods more aggressively
  const originalMethods = {
    error: console.error,
    warn: console.warn,
    log: console.log
  };

  console.error = (...args) => {
    if (args.some(arg => typeof arg === 'string' && arg.includes('ResizeObserver loop'))) {
      return;
    }
    originalMethods.error.apply(console, args);
  };

  console.warn = (...args) => {
    if (args.some(arg => typeof arg === 'string' && arg.includes('ResizeObserver loop'))) {
      return;
    }
    originalMethods.warn.apply(console, args);
  };

  console.log = (...args) => {
    if (args.some(arg => typeof arg === 'string' && arg.includes('ResizeObserver loop'))) {
      return;
    }
    originalMethods.log.apply(console, args);
  };
};

setupComprehensiveResizeObserverSuppression();

// Override React's error handling at the bundle level
const patchReactErrorHandling = () => {
  // Patch setTimeout and setInterval to catch ResizeObserver errors
  const originalSetTimeout = window.setTimeout;
  const originalSetInterval = window.setInterval;

  window.setTimeout = (callback: any, delay?: number, ...args: any[]) => {
    const wrappedCallback = (...callbackArgs: any[]) => {
      try {
        return callback(...callbackArgs);
      } catch (error: any) {
        if (error?.message?.includes('ResizeObserver') ||
            error?.message?.includes('undelivered notifications')) {
          return; // Suppress ResizeObserver errors in timeouts
        }
        throw error;
      }
    };
    return originalSetTimeout(wrappedCallback, delay, ...args);
  };

  window.setInterval = (callback: any, delay?: number, ...args: any[]) => {
    const wrappedCallback = (...callbackArgs: any[]) => {
      try {
        return callback(...callbackArgs);
      } catch (error: any) {
        if (error?.message?.includes('ResizeObserver') ||
            error?.message?.includes('undelivered notifications')) {
          return; // Suppress ResizeObserver errors in intervals
        }
        throw error;
      }
    };
    return originalSetInterval(wrappedCallback, delay, ...args);
  };

  // Patch requestAnimationFrame
  const originalRAF = window.requestAnimationFrame;
  window.requestAnimationFrame = (callback: FrameRequestCallback) => {
    const wrappedCallback = (time: number) => {
      try {
        return callback(time);
      } catch (error: any) {
        if (error?.message?.includes('ResizeObserver') ||
            error?.message?.includes('undelivered notifications')) {
          return; // Suppress ResizeObserver errors in RAF
        }
        throw error;
      }
    };
    return originalRAF(wrappedCallback);
  };

  // Patch Promise.resolve to catch async ResizeObserver errors
  const originalPromiseResolve = Promise.resolve;
  Promise.resolve = (value?: any) => {
    return originalPromiseResolve(value).catch((error: any) => {
      if (error?.message?.includes('ResizeObserver') ||
          error?.message?.includes('undelivered notifications')) {
        return; // Suppress ResizeObserver errors in promises
      }
      throw error;
    });
  };
};

patchReactErrorHandling();

// Initialize font size from localStorage or set to large by default
const initFontSize = () => {
  const savedFontSize = localStorage.getItem('fontSize');
  document.documentElement.style.fontSize = `${savedFontSize || 120}%`;
};
initFontSize();

// Apply MUI patches
setTimeout(() => {
  try {
    applyMuiPatches();
    fixButtonComponent();
  } catch (error) {
    console.error('Error applying MUI patches:', error);
  }
}, 1000);

// Apply button fixes again after a longer delay to ensure all components are loaded
setTimeout(() => {
  try {
    fixButtonComponent();
  } catch (error) {
    console.error('Error applying button fixes:', error);
  }
}, 3000);

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();