/* Asymmetric Hero Section - Non-Centered Layout */
.asymmetric-hero {
  position: relative;
  min-height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d5016 50%, #4caf50 100%);
}

.hero-bg-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.3;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    45deg,
    rgba(26, 26, 26, 0.9) 0%,
    rgba(45, 80, 22, 0.8) 30%,
    rgba(76, 175, 80, 0.7) 70%,
    transparent 100%
  );
}

.hero-layout {
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: 1fr auto;
  min-height: 100vh;
  align-items: center;
  gap: 4rem;
  padding: 0 2rem;
  max-width: none;
}

.hero-content-left {
  padding-left: 5%;
  max-width: 700px;
}

.hero-badge {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background: rgba(76, 175, 80, 0.2);
  border: 1px solid rgba(76, 175, 80, 0.5);
  border-radius: 50px;
  margin-bottom: 2rem;
  color: #4caf50;
  font-weight: 600;
  font-size: 0.9rem;
  letter-spacing: 1px;
}

.hero-title {
  font-size: clamp(3rem, 8vw, 5rem);
  font-weight: 900;
  line-height: 1.1;
  margin-bottom: 2rem;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: -2px;
}

.title-highlight {
  background: linear-gradient(135deg, #4caf50, #81c784);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: block;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 3rem;
  max-width: 600px;
}

.hero-actions {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 4rem;
  flex-wrap: wrap;
}

.hero-btn {
  padding: 1rem 2.5rem;
  border: none;
  border-radius: 50px;
  font-weight: 700;
  font-size: 1rem;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-transform: uppercase;
  min-width: 200px;
  justify-content: center;
}

.hero-btn-primary {
  background: linear-gradient(135deg, #4caf50, #66bb6a);
  color: white;
  box-shadow: 0 10px 30px rgba(76, 175, 80, 0.3);
}

.hero-btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(76, 175, 80, 0.4);
}

.hero-btn-outline {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.hero-btn-outline:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-3px);
}

/* Right Stats Area */
.hero-stats-right {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding-right: 5%;
  align-items: flex-end;
}

.hero-stat-card {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 1.5rem;
  text-align: center;
  min-width: 150px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.hero-stat-card:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(76, 175, 80, 0.5);
  transform: scale(1.05) translateX(-10px);
}

.stat-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 900;
  color: #4caf50;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .hero-layout {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 2rem;
  }

  .hero-content-left {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .hero-stats-right {
    flex-direction: row;
    justify-content: center;
    padding-right: 2rem;
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .hero-btn {
    width: 100%;
    max-width: 300px;
  }

  .hero-stats-right {
    gap: 1rem;
  }

  .hero-stat-card {
    min-width: 120px;
    padding: 1rem;
  }
}

.flare-hero-badge {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background: rgba(76, 175, 80, 0.2);
  border: 1px solid rgba(76, 175, 80, 0.5);
  border-radius: 50px;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
}

.flare-badge-text {
  color: #4caf50;
  font-weight: 600;
  font-size: 0.9rem;
  letter-spacing: 1px;
}

.flare-hero-title {
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 900;
  line-height: 0.9;
  margin-bottom: 2rem;
  text-transform: uppercase;
  letter-spacing: -2px;
}

.flare-title-line1 {
  display: block;
  color: #ffffff;
  opacity: 0.9;
}

.flare-title-line2 {
  display: block;
  background: linear-gradient(135deg, #4caf50, #81c784);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.flare-title-line3 {
  display: block;
  color: #ffffff;
  opacity: 0.8;
  font-size: 0.8em;
}

.flare-hero-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 3rem;
  max-width: 600px;
}

.flare-hero-actions {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 4rem;
  flex-wrap: wrap;
}

.flare-btn {
  padding: 1rem 2.5rem;
  border: none;
  border-radius: 50px;
  font-weight: 700;
  font-size: 1rem;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-transform: uppercase;
  min-width: 200px;
  justify-content: center;
}

.flare-btn-primary {
  background: linear-gradient(135deg, #4caf50, #66bb6a);
  color: white;
  box-shadow: 0 10px 30px rgba(76, 175, 80, 0.3);
}

.flare-btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(76, 175, 80, 0.4);
}

.flare-btn-outline {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.flare-btn-outline:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-3px);
}

/* Floating Stats */
.flare-floating-stats {
  position: absolute;
  top: 50%;
  right: 5%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.flare-stat-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 1.5rem;
  text-align: center;
  min-width: 150px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.flare-stat-card:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(76, 175, 80, 0.5);
}

.flare-stat-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.flare-stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 900;
  color: #4caf50;
  margin-bottom: 0.25rem;
}

.flare-stat-label {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Scroll Indicator */
.flare-scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  animation: bounce 2s infinite;
}

.flare-scroll-text {
  font-size: 0.8rem;
  letter-spacing: 2px;
  margin-bottom: 0.5rem;
}

.flare-scroll-arrow {
  font-size: 1.5rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0) translateX(-50%);
  }
  40% {
    transform: translateY(-10px) translateX(-50%);
  }
  60% {
    transform: translateY(-5px) translateX(-50%);
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .flare-floating-stats {
    position: static;
    transform: none;
    flex-direction: row;
    justify-content: center;
    margin-top: 3rem;
  }
}

@media (max-width: 768px) {
  .flare-hero-content {
    text-align: center;
  }
  
  .flare-hero-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .flare-btn {
    width: 100%;
    max-width: 300px;
  }
  
  .flare-floating-stats {
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .flare-stat-card {
    min-width: 120px;
    padding: 1rem;
  }
}

/* Davis Portfolio Style Features Section */
.davis-features {
  position: relative;
  padding: 8rem 0;
  overflow: hidden;
}

.davis-features-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.davis-bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.1;
}

.davis-bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(26, 26, 26, 0.95) 0%,
    rgba(45, 80, 22, 0.9) 50%,
    rgba(76, 175, 80, 0.85) 100%
  );
}

.davis-container {
  position: relative;
  z-index: 2;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.davis-features-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

/* Main Feature (Left Side) */
.davis-main-feature {
  padding-right: 2rem;
}

.davis-feature-badge {
  display: inline-block;
  padding: 0.5rem 1rem;
  background: rgba(76, 175, 80, 0.2);
  border: 1px solid rgba(76, 175, 80, 0.5);
  border-radius: 25px;
  margin-bottom: 1.5rem;
  backdrop-filter: blur(10px);
}

.davis-feature-badge span {
  color: #4caf50;
  font-weight: 600;
  font-size: 0.8rem;
  letter-spacing: 1px;
}

.davis-feature-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 900;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: white;
  text-transform: uppercase;
  letter-spacing: -1px;
}

.davis-highlight {
  background: linear-gradient(135deg, #4caf50, #81c784);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.davis-feature-description {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 2rem;
}

.davis-feature-stats {
  display: flex;
  gap: 2rem;
  margin-bottom: 2.5rem;
}

.davis-stat {
  text-align: center;
}

.davis-stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 900;
  color: #4caf50;
  margin-bottom: 0.25rem;
}

.davis-stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.davis-cta-btn {
  background: linear-gradient(135deg, #4caf50, #66bb6a);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 700;
  font-size: 1rem;
  letter-spacing: 1px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(76, 175, 80, 0.3);
}

.davis-cta-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(76, 175, 80, 0.4);
}

/* Feature Grid (Right Side) */
.davis-features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.davis-feature-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.davis-feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(129, 199, 132, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.davis-feature-card:hover::before {
  opacity: 1;
}

.davis-feature-card:hover {
  border-color: rgba(76, 175, 80, 0.3);
  background: rgba(255, 255, 255, 0.08);
}

.davis-card-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.davis-card-title {
  font-size: 1.1rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.davis-card-description {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
  margin-bottom: 1rem;
}

.davis-card-arrow {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  color: #4caf50;
  font-size: 1.2rem;
  opacity: 0;
  transition: all 0.3s ease;
}

.davis-feature-card:hover .davis-card-arrow {
  opacity: 1;
  transform: translateX(5px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .davis-features-layout {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .davis-main-feature {
    padding-right: 0;
    text-align: center;
  }

  .davis-feature-stats {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .davis-features-grid {
    grid-template-columns: 1fr;
  }

  .davis-feature-stats {
    flex-direction: column;
    gap: 1rem;
  }
}

/* Dynamic Gallery Section */
.dynamic-gallery {
  position: relative;
  padding: 8rem 0;
  overflow: hidden;
}

.dynamic-gallery-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.dynamic-bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.15;
}

.dynamic-bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(26, 26, 26, 0.9) 0%,
    rgba(45, 80, 22, 0.85) 50%,
    rgba(76, 175, 80, 0.8) 100%
  );
}

.dynamic-container {
  position: relative;
  z-index: 2;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.dynamic-gallery-header {
  text-align: center;
  margin-bottom: 4rem;
}

.dynamic-gallery-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 900;
  color: white;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: -1px;
}

.dynamic-gallery-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  max-width: 600px;
  margin: 0 auto;
}

.dynamic-gallery-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  height: 600px;
}

.dynamic-gallery-main {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dynamic-main-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.dynamic-main-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 2rem;
  color: white;
}

.dynamic-main-overlay h3 {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.dynamic-main-overlay p {
  font-size: 1rem;
  opacity: 0.9;
}

.dynamic-gallery-side {
  display: grid;
  grid-template-rows: repeat(4, 1fr);
  gap: 1rem;
}

.dynamic-gallery-item {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dynamic-item-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.dynamic-item-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
  padding: 1rem;
  color: white;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.dynamic-gallery-item:hover .dynamic-item-overlay {
  transform: translateY(0);
}

.dynamic-item-overlay h4 {
  font-size: 0.9rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.dynamic-item-overlay p {
  font-size: 0.8rem;
  opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dynamic-gallery-grid {
    grid-template-columns: 1fr;
    height: auto;
    gap: 2rem;
  }

  .dynamic-gallery-main {
    height: 400px;
  }

  .dynamic-gallery-side {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 200px);
  }
}

@media (max-width: 768px) {
  .dynamic-gallery-side {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(4, 200px);
  }

  .dynamic-gallery-main {
    height: 300px;
  }
}
