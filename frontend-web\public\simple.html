<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AgriIntel - Smart Livestock Management</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2E7D32, #4CAF50);
            color: white;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 3rem;
            padding: 1rem;
            background: rgba(0,0,0,0.2);
            border-radius: 10px;
        }
        
        .logo {
            font-size: 2rem;
            font-weight: bold;
        }
        
        .nav-buttons button {
            padding: 0.8rem 1.5rem;
            margin: 0 0.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
        }
        
        .beta-btn {
            background: #FF9800;
            color: white;
        }
        
        .live-btn {
            background: #2196F3;
            color: white;
        }
        
        .hero {
            text-align: center;
            margin-bottom: 4rem;
        }
        
        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .hero p {
            font-size: 1.5rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }
        
        .action-buttons button {
            padding: 1.5rem 3rem;
            margin: 1rem;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1.3rem;
            font-weight: bold;
        }
        
        .primary-btn {
            background: #FF9800;
            color: white;
        }
        
        .secondary-btn {
            background: transparent;
            color: white;
            border: 2px solid white;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 4rem;
        }
        
        .feature-card {
            background: rgba(255,255,255,0.1);
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin-bottom: 4rem;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #FFD700;
        }
        
        footer {
            text-align: center;
            padding: 2rem;
            background: rgba(0,0,0,0.2);
            border-radius: 10px;
            margin-top: 3rem;
        }
        
        .success-message {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 1rem;
            border-radius: 5px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            z-index: 1000;
        }

        .footer-contact {
            margin-top: 1rem;
        }

        .contact-item {
            margin: 0 1rem;
        }

        .footer-copyright {
            margin-top: 1rem;
            opacity: 0.7;
        }
        
        @media (max-width: 768px) {
            header {
                flex-direction: column;
                gap: 1rem;
            }
            
            .hero h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="success-message" id="successMessage">
        ✅ AgriIntel Loaded Successfully - No Errors!
    </div>

    <div class="container">
        <!-- Header -->
        <header>
            <div class="logo">🌾 AgriIntel</div>
            <div class="nav-buttons">
                <button type="button" class="beta-btn" onclick="goToBeta()">BETA ACCESS</button>
                <button type="button" class="live-btn" onclick="goToLive()">LIVE ACCESS</button>
            </div>
        </header>

        <!-- Hero Section -->
        <section class="hero">
            <h1>Smart Livestock Management for South Africa</h1>
            <p>Join 15,000+ farmers who've transformed their operations with AI technology</p>
            
            <div class="action-buttons">
                <button type="button" class="primary-btn" onclick="goToBeta()">
                    🚀 START FREE BETA
                </button>
                <button type="button" class="secondary-btn" onclick="goToLive()">
                    ⚡ GO LIVE NOW
                </button>
            </div>
        </section>

        <!-- Features -->
        <section class="features">
            <div class="feature-card">
                <div class="feature-icon">🐄</div>
                <h3>Smart Livestock Tracking</h3>
                <p>Real-time monitoring of cattle, sheep, and goats with RFID technology</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🩺</div>
                <h3>AI Health Management</h3>
                <p>Predictive health analytics to prevent diseases and reduce costs</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <h3>Advanced Analytics</h3>
                <p>Comprehensive insights and reporting for better farm management</p>
            </div>
        </section>

        <!-- Stats -->
        <section class="stats">
            <div>
                <div class="stat-number">15K+</div>
                <div>South African Farmers</div>
            </div>
            <div>
                <div class="stat-number">750K+</div>
                <div>Livestock Tracked</div>
            </div>
            <div>
                <div class="stat-number">99.9%</div>
                <div>System Uptime</div>
            </div>
            <div>
                <div class="stat-number">40%</div>
                <div>Cost Reduction</div>
            </div>
        </section>

        <!-- Footer -->
        <footer>
            <h3>🌾 AgriIntel</h3>
            <p>Smart Livestock Management for South African Farmers</p>
            <div class="footer-contact">
                <span class="contact-item">📞 +27 11 123 4567</span>
                <span class="contact-item">📧 <EMAIL></span>
                <span class="contact-item">📍 Cape Town, South Africa</span>
            </div>
            <p class="footer-copyright">
                © 2025 AgriIntel. All rights reserved. Made with ❤️ by May and Caiphusfor South African farmers.
            </p>
        </footer>
    </div>

    <script>
        function goToBeta() {
            alert('🚀 BETA ACCESS\n\nWelcome to AgriIntel BETA!\n\nFeatures:\n✅ Up to 50 animals\n✅ Basic health monitoring\n✅ Simple reports\n\nAPI Backend: http://localhost:3004\n\nClick OK to continue...');
            // Backend API is running on port 3004
            console.log('Backend API: http://localhost:3004/api/db-status');
        }

        function goToLive() {
            alert('⚡ LIVE ACCESS\n\nWelcome to AgriIntel LIVE!\n\nFull Features:\n✅ Unlimited animals\n✅ AI health analytics\n✅ Advanced reports\n✅ Financial management\n\nAPI Backend: http://localhost:3004\n\nClick OK to continue...');
            // Backend API is running on port 3004
            console.log('Backend API: http://localhost:3004/api/db-status');
        }

        // Hide success message after 3 seconds
        setTimeout(function() {
            document.getElementById('successMessage').style.display = 'none';
        }, 3000);

        console.log('🎉 AgriIntel Landing Page Loaded Successfully!');
        console.log('✅ No React errors');
        console.log('✅ No infinite reloading');
        console.log('✅ Simple and stable');
        console.log('🌐 Available at: http://localhost:3003/simple.html');
    </script>
</body>
</html>
