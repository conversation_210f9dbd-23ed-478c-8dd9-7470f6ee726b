/**
 * Sidebar Layout Styles
 * Professional sidebar navigation for AgriIntel platform
 */

/* ===== SIDEBAR CONTAINER ===== */

.agri-sidebar-container {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 280px;
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border-right: 1px solid var(--glass-border);
  z-index: var(--z-sidebar);
  transition: var(--transition-normal);
  overflow-y: auto;
  overflow-x: hidden;
}

.agri-sidebar-container.collapsed {
  width: 80px;
}

.agri-sidebar-container.mobile-hidden {
  transform: translateX(-100%);
}

.agri-sidebar-container.mobile-open {
  transform: translateX(0);
}

/* ===== SIDEBAR HEADER ===== */

.agri-sidebar-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  min-height: 80px;
}

.agri-sidebar-logo {
  height: 40px;
  width: auto;
  object-fit: contain;
  flex-shrink: 0;
}

.agri-sidebar-brand {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: var(--transition-normal);
}

.agri-sidebar-container.collapsed .agri-sidebar-brand {
  opacity: 0;
  width: 0;
}

.agri-sidebar-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0;
  white-space: nowrap;
  line-height: 1.2;
}

.agri-sidebar-subtitle {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin: 0;
  white-space: nowrap;
  line-height: 1.2;
}

/* ===== SIDEBAR NAVIGATION ===== */

.agri-sidebar-nav {
  padding: var(--spacing-md) 0;
  flex: 1;
}

.agri-nav-section {
  margin-bottom: var(--spacing-lg);
}

.agri-nav-section:last-child {
  margin-bottom: 0;
}

.agri-nav-section-title {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
  transition: var(--transition-normal);
  white-space: nowrap;
  overflow: hidden;
}

.agri-sidebar-container.collapsed .agri-nav-section-title {
  opacity: 0;
  height: 0;
  margin: 0;
  padding: 0;
}

/* ===== NAVIGATION ITEMS ===== */

.agri-nav-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  color: var(--color-text-primary);
  text-decoration: none;
  transition: var(--transition-normal);
  position: relative;
  border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
  margin-right: var(--spacing-md);
  min-height: 48px;
}

.agri-nav-item:hover {
  background: var(--color-background-hover);
  color: var(--agri-primary);
  transform: translateX(4px);
}

.agri-nav-item.active {
  background: var(--agri-primary);
  color: var(--color-white);
  transform: translateX(4px);
}

.agri-nav-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--agri-accent);
}

.agri-nav-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Navigation Item Content */
.agri-nav-icon {
  width: 24px;
  height: 24px;
  margin-right: var(--spacing-md);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.agri-nav-text {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
  overflow: hidden;
  transition: var(--transition-normal);
  flex: 1;
}

.agri-sidebar-container.collapsed .agri-nav-text {
  opacity: 0;
  width: 0;
  margin: 0;
}

.agri-nav-badge {
  margin-left: auto;
  background: var(--agri-accent);
  color: var(--color-white);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  min-width: 20px;
  text-align: center;
  line-height: 1;
  transition: var(--transition-normal);
}

.agri-sidebar-container.collapsed .agri-nav-badge {
  opacity: 0;
  width: 0;
  margin: 0;
  padding: 0;
}

/* ===== SUBMENU ===== */

.agri-nav-submenu {
  max-height: 0;
  overflow: hidden;
  transition: var(--transition-normal);
  background: rgba(0, 0, 0, 0.05);
}

.agri-nav-item.expanded + .agri-nav-submenu {
  max-height: 500px;
}

.agri-nav-submenu-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-lg) var(--spacing-sm) calc(var(--spacing-lg) + 40px);
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: var(--transition-normal);
  font-size: var(--font-size-sm);
  min-height: 40px;
}

.agri-nav-submenu-item:hover {
  background: var(--color-background-hover);
  color: var(--agri-primary);
}

.agri-nav-submenu-item.active {
  background: rgba(21, 101, 192, 0.1);
  color: var(--agri-primary);
  font-weight: var(--font-weight-semibold);
}

.agri-nav-submenu-item::before {
  content: '';
  width: 6px;
  height: 6px;
  border-radius: var(--radius-full);
  background: var(--color-text-disabled);
  margin-right: var(--spacing-sm);
  transition: var(--transition-normal);
}

.agri-nav-submenu-item.active::before {
  background: var(--agri-primary);
}

/* ===== SIDEBAR FOOTER ===== */

.agri-sidebar-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--color-border);
  margin-top: auto;
}

.agri-sidebar-user {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: var(--transition-normal);
}

.agri-sidebar-user:hover {
  background: var(--color-background-hover);
}

.agri-user-avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  background: var(--agri-primary);
  color: var(--color-white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-base);
  flex-shrink: 0;
}

.agri-user-info {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: var(--transition-normal);
}

.agri-sidebar-container.collapsed .agri-user-info {
  opacity: 0;
  width: 0;
}

.agri-user-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.agri-user-role {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* ===== SIDEBAR TOGGLE ===== */

.agri-sidebar-toggle {
  position: absolute;
  top: var(--spacing-lg);
  right: calc(var(--spacing-md) * -1);
  width: 32px;
  height: 32px;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-full);
  background: var(--color-background-primary);
  color: var(--color-text-primary);
  cursor: pointer;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.agri-sidebar-toggle:hover {
  background: var(--color-background-hover);
  border-color: var(--agri-primary);
  color: var(--agri-primary);
}

/* ===== MOBILE OVERLAY ===== */

.agri-sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: calc(var(--z-sidebar) - 1);
  opacity: 0;
  visibility: hidden;
  transition: var(--transition-normal);
}

.agri-sidebar-overlay.visible {
  opacity: 1;
  visibility: visible;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 1024px) {
  .agri-sidebar-container {
    transform: translateX(-100%);
  }
  
  .agri-sidebar-container.mobile-open {
    transform: translateX(0);
  }
  
  .agri-sidebar-toggle {
    display: none;
  }
}

@media (max-width: 768px) {
  .agri-sidebar-container {
    width: 100%;
    max-width: 320px;
  }
  
  .agri-nav-item {
    padding: var(--spacing-md) var(--spacing-lg);
  }
  
  .agri-nav-submenu-item {
    padding: var(--spacing-sm) var(--spacing-lg) var(--spacing-sm) calc(var(--spacing-lg) + 32px);
  }
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  .agri-sidebar-container,
  .agri-nav-item,
  .agri-nav-text,
  .agri-nav-badge,
  .agri-nav-submenu,
  .agri-user-info {
    transition: none;
  }
  
  .agri-nav-item:hover {
    transform: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .agri-sidebar-container {
    background: var(--color-background-primary);
    border-right: 2px solid var(--color-text-primary);
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
  }
  
  .agri-nav-item.active {
    background: var(--color-text-primary);
    color: var(--color-background-primary);
  }
  
  .agri-nav-item.active::before {
    background: var(--color-background-primary);
  }
  
  .agri-sidebar-header,
  .agri-sidebar-footer {
    border-color: var(--color-text-primary);
  }
}

/* ===== SCROLLBAR STYLING ===== */

.agri-sidebar-container::-webkit-scrollbar {
  width: 6px;
}

.agri-sidebar-container::-webkit-scrollbar-track {
  background: transparent;
}

.agri-sidebar-container::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: var(--radius-full);
}

.agri-sidebar-container::-webkit-scrollbar-thumb:hover {
  background: var(--agri-primary);
}
