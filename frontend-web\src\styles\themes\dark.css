/**
 * Dark Theme - AgriIntel
 * Dark color scheme for the application
 */

:root[data-theme="dark"] {
  /* Primary Colors */
  --color-primary: #4CAF50;
  --color-primary-dark: #2E7D32;
  --color-primary-light: #81C784;
  --color-primary-lighter: #A5D6A7;

  /* Secondary Colors */
  --color-secondary: #2196F3;
  --color-secondary-dark: #1565C0;
  --color-secondary-light: #64B5F6;

  /* Accent Colors */
  --color-accent: #FF9800;
  --color-accent-dark: #F57C00;
  --color-accent-light: #FFB74D;

  /* Background Colors */
  --color-background-primary: #121212;
  --color-background-secondary: #1E1E1E;
  --color-background-tertiary: #2D2D2D;
  --color-background-hover: #333333;

  /* Text Colors */
  --color-text-primary: #FFFFFF;
  --color-text-secondary: #B3B3B3;
  --color-text-tertiary: #808080;
  --color-text-inverse: #000000;

  /* Border Colors */
  --color-border: #404040;
  --color-border-light: #333333;
  --color-border-dark: #555555;

  /* Status Colors */
  --color-success: #4CAF50;
  --color-warning: #FF9800;
  --color-error: #F44336;
  --color-info: #2196F3;

  /* Utility Colors */
  --color-white: #FFFFFF;
  --color-black: #000000;
  --color-focus: #2196F3;

  /* Glass Morphism */
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-backdrop: blur(20px);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);

  /* Gradients */
  --agri-pro-gradient: linear-gradient(135deg, #4CAF50 0%, #81C784 100%);
  --agri-beta-gradient: linear-gradient(135deg, #FF9800 0%, #FFB74D 100%);
  --agri-premium-gradient: linear-gradient(135deg, #2196F3 0%, #64B5F6 100%);

  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.6);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.7);

  /* Card Backgrounds */
  --card-bg: #1E1E1E;
  --card-border: #404040;
  --card-shadow: var(--shadow-lg);

  /* Navigation */
  --nav-bg: rgba(30, 30, 30, 0.95);
  --nav-border: rgba(255, 255, 255, 0.1);

  /* Sidebar */
  --sidebar-bg: #1E1E1E;
  --sidebar-border: #404040;
  --sidebar-item-hover: #333333;
  --sidebar-item-active: #2D4A2D;

  /* Form Elements */
  --input-bg: #2D2D2D;
  --input-border: #404040;
  --input-border-focus: #4CAF50;
  --input-placeholder: #808080;

  /* Buttons */
  --btn-primary-bg: var(--color-primary);
  --btn-primary-hover: var(--color-primary-light);
  --btn-secondary-bg: var(--color-secondary);
  --btn-secondary-hover: var(--color-secondary-light);

  /* Tables */
  --table-header-bg: #2D2D2D;
  --table-border: #404040;
  --table-row-hover: #333333;
  --table-row-selected: #2D4A2D;

  /* Charts */
  --chart-grid: #404040;
  --chart-text: #B3B3B3;
  --chart-primary: #4CAF50;
  --chart-secondary: #2196F3;
  --chart-accent: #FF9800;

  /* Scrollbar */
  --scrollbar-track: #2D2D2D;
  --scrollbar-thumb: #555555;
  --scrollbar-thumb-hover: #666666;
}

/* Dark theme specific overrides */
.dark-theme {
  background-color: var(--color-background-primary);
  color: var(--color-text-primary);
}

.dark-theme .card {
  background-color: var(--card-bg);
  border-color: var(--card-border);
  box-shadow: var(--card-shadow);
}

.dark-theme .nav {
  background-color: var(--nav-bg);
  border-color: var(--nav-border);
}

.dark-theme .sidebar {
  background-color: var(--sidebar-bg);
  border-color: var(--sidebar-border);
}

.dark-theme .btn-primary {
  background-color: var(--btn-primary-bg);
}

.dark-theme .btn-primary:hover {
  background-color: var(--btn-primary-hover);
}

.dark-theme .table {
  border-color: var(--table-border);
}

.dark-theme .table thead {
  background-color: var(--table-header-bg);
}

.dark-theme .table tbody tr:hover {
  background-color: var(--table-row-hover);
}

/* Scrollbar styling for dark theme */
.dark-theme ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.dark-theme ::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  border-radius: 4px;
}

.dark-theme ::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 4px;
}

.dark-theme ::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

/* Dark theme specific adjustments */
.dark-theme img {
  opacity: 0.9;
}

.dark-theme .glass-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark-theme .text-muted {
  color: var(--color-text-secondary) !important;
}
