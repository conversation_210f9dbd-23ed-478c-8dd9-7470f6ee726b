import React from 'react';
import { Chip, Box, Typography, useTheme } from '@mui/material';
import { motion } from 'framer-motion';
import { Star, Warning } from '@mui/icons-material';

interface BetaBadgeProps {
  variant?: 'small' | 'medium' | 'large';
  showIcon?: boolean;
  showText?: boolean;
  animated?: boolean;
  position?: 'top-right' | 'top-left' | 'inline';
}

const BetaBadge: React.FC<BetaBadgeProps> = ({
  variant = 'medium',
  showIcon = true,
  showText = true,
  animated = true,
  position = 'inline'
}) => {
  const theme = useTheme();

  const badgeStyles = {
    small: {
      fontSize: '0.75rem',
      height: '20px',
      padding: '0 6px'
    },
    medium: {
      fontSize: '0.875rem',
      height: '24px',
      padding: '0 8px'
    },
    large: {
      fontSize: '1rem',
      height: '32px',
      padding: '0 12px'
    }
  };

  const positionStyles = {
    'top-right': {
      position: 'absolute' as const,
      top: 8,
      right: 8,
      zIndex: 10
    },
    'top-left': {
      position: 'absolute' as const,
      top: 8,
      left: 8,
      zIndex: 10
    },
    'inline': {}
  };

  const BadgeComponent = (
    <Chip
      icon={showIcon ? <Star sx={{ fontSize: '1rem !important' }} /> : undefined}
      label={
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          {showText && (
            <Typography
              variant="caption"
              sx={{
                fontWeight: 700,
                fontSize: badgeStyles[variant].fontSize,
                textTransform: 'uppercase',
                letterSpacing: '0.5px'
              }}
            >
              BETA
            </Typography>
          )}
        </Box>
      }
      sx={{
        ...badgeStyles[variant],
        ...positionStyles[position],
        background: 'linear-gradient(135deg, #FFA726 0%, #FFB74D 50%, #FFCC02 100%)',
        color: '#1A1A1A',
        fontWeight: 700,
        border: '2px solid rgba(255, 193, 7, 0.3)',
        boxShadow: '0 4px 12px rgba(255, 193, 7, 0.4)',
        '& .MuiChip-icon': {
          color: '#1A1A1A',
          fontSize: '1rem'
        },
        '&:hover': {
          background: 'linear-gradient(135deg, #FFB74D 0%, #FFCC02 50%, #FFD54F 100%)',
          boxShadow: '0 6px 16px rgba(255, 193, 7, 0.5)',
          transform: 'translateY(-1px)'
        },
        transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)'
      }}
    />
  );

  if (animated) {
    return (
      <motion.div
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{
          type: "spring",
          stiffness: 260,
          damping: 20,
          delay: 0.1
        }}
        whileHover={{ scale: 1.05 }}
        style={position !== 'inline' ? positionStyles[position] : {}}
      >
        {BadgeComponent}
      </motion.div>
    );
  }

  return BadgeComponent;
};

export default BetaBadge;
