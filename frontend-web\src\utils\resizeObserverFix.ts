/**
 * Utility to fix ResizeObserver loop errors
 * This is a common issue in React applications with complex layouts and animations
 */

// Create a debounced resize observer that limits the frequency of resize callbacks
export const createDebouncedResizeObserver = (
  callback: ResizeObserverCallback,
  delay: number = 100
): ResizeObserver => {
  let timeoutId: ReturnType<typeof setTimeout> | null = null;
  let pendingEntries: ResizeObserverEntry[] = [];

  const debouncedCallback: ResizeObserverCallback = (entries, observer) => {
    // Store the entries
    pendingEntries = [...pendingEntries, ...entries];

    // Clear the previous timeout
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    // Set a new timeout
    timeoutId = setTimeout(() => {
      // Call the callback with the accumulated entries
      callback(pendingEntries, observer);
      // Reset the pending entries
      pendingEntries = [];
      timeoutId = null;
    }, delay);
  };

  return new ResizeObserver(debouncedCallback);
};

// Patch the global ResizeObserver to prevent loop errors
export const patchResizeObserver = (): void => {
  // Store the original error handler
  const originalHandler = window.onerror;

  // Create a new error handler that ignores ResizeObserver loop errors
  window.onerror = (message, source, lineno, colno, error) => {
    // Check if the error is a ResizeObserver loop error
    if (message && message.toString().includes('ResizeObserver loop')) {
      // Ignore the error completely
      return true;
    }

    // Call the original error handler for other errors
    if (originalHandler) {
      return originalHandler(message, source, lineno, colno, error);
    }

    return false;
  };

  // Also patch the unhandledrejection event for Promise-based ResizeObserver errors
  window.addEventListener('unhandledrejection', (event) => {
    if (event.reason &&
        typeof event.reason === 'object' &&
        event.reason.message &&
        event.reason.message.includes('ResizeObserver loop')) {
      event.preventDefault();
      return;
    }
  });

  // Patch the console.error method to suppress ResizeObserver loop errors
  const originalConsoleError = console.error;
  console.error = (...args) => {
    // Check if the error is a ResizeObserver loop error
    if (
      args.length > 0 &&
      (
        (typeof args[0] === 'string' && args[0].includes('ResizeObserver loop')) ||
        (args[0] && typeof args[0] === 'object' && args[0].message && args[0].message.includes('ResizeObserver loop'))
      )
    ) {
      // Completely ignore the error - don't even log a warning
      return;
    }

    // Call the original console.error for other errors
    originalConsoleError.apply(console, args);
  };

  // Also patch console.warn for ResizeObserver warnings
  const originalConsoleWarn = console.warn;
  console.warn = (...args) => {
    // Check if the warning is a ResizeObserver loop warning
    if (
      args.length > 0 &&
      typeof args[0] === 'string' &&
      args[0].includes('ResizeObserver loop')
    ) {
      // Ignore the warning
      return;
    }

    // Call the original console.warn for other warnings
    originalConsoleWarn.apply(console, args);
  };
};

// Apply the ResizeObserver fix
export const applyResizeObserverFix = (): void => {
  patchResizeObserver();

  // Replace the native ResizeObserver with a safer version
  if (typeof window.ResizeObserver !== 'undefined') {
    const OriginalResizeObserver = window.ResizeObserver;

    window.ResizeObserver = class SafeResizeObserver extends OriginalResizeObserver {
      constructor(callback: ResizeObserverCallback) {
        // Wrap the callback to prevent loops
        const safeCallback: ResizeObserverCallback = (entries, observer) => {
          // Use requestAnimationFrame to break potential loops
          window.requestAnimationFrame(() => {
            try {
              callback(entries, observer);
            } catch (error: any) {
              // Silently ignore ResizeObserver loop errors
              if (!(error && typeof error === 'object' && error.message && error.message.includes('ResizeObserver loop'))) {
                throw error;
              }
            }
          });
        };

        super(safeCallback);
      }
    };
  }

  // Add a more aggressive fix for React 18's strict mode and concurrent rendering
  const requestAnimationFrameOriginal = window.requestAnimationFrame;
  window.requestAnimationFrame = (callback: FrameRequestCallback): number => {
    return requestAnimationFrameOriginal((time: number) => {
      try {
        callback(time);
      } catch (error: any) {
        if (error && typeof error === 'object' && error.message && typeof error.message === 'string' && error.message.includes('ResizeObserver loop')) {
          // Silently ignore ResizeObserver loop errors
        } else {
          throw error;
        }
      }
    });
  };

  // Add a MutationObserver to detect when charts are added to the DOM
  // and apply a small delay to their rendering to prevent ResizeObserver loops
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.addedNodes.length) {
        mutation.addedNodes.forEach((node) => {
          if (node instanceof HTMLElement) {
            const charts = node.querySelectorAll(
              '.recharts-wrapper, .recharts-responsive-container, .MuiDataGrid-root, .MuiDataGrid-virtualScroller'
            );
            if (charts.length) {
              charts.forEach((chart) => {
                if (chart instanceof HTMLElement) {
                  // Add a small delay to chart rendering
                  chart.style.opacity = '0';
                  setTimeout(() => {
                    chart.style.opacity = '1';
                    chart.style.transition = 'opacity 0.2s ease-in-out';
                  }, 50);
                }
              });
            }
          }
        });
      }
    });
  });

  // Start observing the document body for chart additions
  observer.observe(document.body, { childList: true, subtree: true });

  // Add CSS fixes for ResizeObserver issues
  const styleTag = document.createElement('style');
  styleTag.innerHTML = `
    /* Apply to all chart containers to prevent ResizeObserver loops */
    .recharts-wrapper,
    .recharts-responsive-container,
    .recharts-surface,
    .recharts-layer,
    .recharts-area,
    .recharts-bar,
    .recharts-line,
    .recharts-pie,
    .recharts-radar,
    .recharts-scatter,
    .recharts-treemap,
    .recharts-sankey,
    .recharts-polygon,
    .recharts-dot,
    .recharts-circle {
      transition: width 0.1s ease-in-out, height 0.1s ease-in-out, transform 0.1s ease-in-out !important;
      will-change: width, height, transform !important;
      contain: layout !important;
    }

    /* Apply to MUI components that might cause ResizeObserver loops */
    .MuiDataGrid-root,
    .MuiDataGrid-virtualScroller,
    .MuiDataGrid-virtualScrollerContent,
    .MuiDataGrid-virtualScrollerRenderZone {
      transition: width 0.1s ease-in-out, height 0.1s ease-in-out !important;
      will-change: width, height !important;
      contain: layout !important;
    }

    /* Fix for navigation tabs to ensure they're clickable */
    .MuiTabs-root,
    .MuiTab-root,
    .MuiButtonBase-root,
    .MuiBox-root,
    .MuiPaper-root {
      position: relative;
      z-index: 10;
      pointer-events: auto !important;
    }

    /* Ensure all clickable elements are accessible */
    button, a, [role="button"], [role="tab"], [role="link"] {
      position: relative;
      z-index: 20;
      pointer-events: auto !important;
    }

    /* Fix for any overlay issues */
    .MuiBackdrop-root,
    .MuiModal-backdrop {
      z-index: 1300 !important;
    }
  `;
  document.head.appendChild(styleTag);

  // ResizeObserver fix applied silently
};

// Apply chart-specific fixes
export const applyChartFixes = (): void => {
  // Add specific fixes for chart components
  const chartStyleTag = document.createElement('style');
  chartStyleTag.innerHTML = `
    /* Recharts specific fixes */
    .recharts-responsive-container {
      position: relative !important;
      overflow: hidden !important;
      contain: layout style paint !important;
    }

    .recharts-wrapper {
      contain: layout style paint !important;
      overflow: hidden !important;
    }

    /* Prevent rapid re-renders in charts */
    .recharts-surface {
      transition: none !important;
      contain: layout style paint !important;
    }
  `;

  if (!document.head.querySelector('#chart-resize-fix-styles')) {
    chartStyleTag.id = 'chart-resize-fix-styles';
    document.head.appendChild(chartStyleTag);
  }
};

export default {
  createDebouncedResizeObserver,
  patchResizeObserver,
  applyResizeObserverFix,
  applyChartFixes
};
