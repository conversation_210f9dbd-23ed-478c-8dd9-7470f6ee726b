/**
 * <PERSON><PERSON>t to populate MongoDB with real data for all modules
 * This replaces all mock data with actual database records
 */

const { MongoClient, ObjectId } = require('mongodb');
require('dotenv').config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017';
const DATABASE_NAME = process.env.DATABASE_NAME || 'ampd_livestock';

class DataPopulator {
  constructor() {
    this.client = null;
    this.db = null;
  }

  async connect() {
    try {
      this.client = new MongoClient(MONGODB_URI);
      await this.client.connect();
      this.db = this.client.db(DATABASE_NAME);
      console.log('✅ Connected to MongoDB');
    } catch (error) {
      console.error('❌ MongoDB connection failed:', error);
      throw error;
    }
  }

  async disconnect() {
    if (this.client) {
      await this.client.close();
      console.log('✅ Disconnected from MongoDB');
    }
  }

  // Generate realistic South African livestock data
  generateAnimals() {
    const species = ['Cattle', 'Sheep', 'Goats', 'Pigs'];
    const breeds = {
      'Cattle': ['Bonsmara', 'Brahman', 'Nguni', 'Afrikaner', 'Simmentaler'],
      'Sheep': ['Dorper', 'Merino', 'Damara', 'Van Rooy', 'Blackhead Persian'],
      'Goats': ['Boer', 'Angora', 'Savanna', 'Kalahari Red', 'Indigenous'],
      'Pigs': ['Large White', 'Landrace', 'Duroc', 'Hampshire', 'Pietrain']
    };
    const locations = ['Paddock A', 'Paddock B', 'Paddock C', 'Barn 1', 'Barn 2'];
    const names = ['Tumelo', 'Lerato', 'Thabo', 'Nomsa', 'Sipho', 'Zanele', 'Mandla', 'Precious'];

    return Array.from({ length: 25 }, (_, i) => {
      const selectedSpecies = species[Math.floor(Math.random() * species.length)];
      const selectedBreed = breeds[selectedSpecies][Math.floor(Math.random() * breeds[selectedSpecies].length)];
      const birthDate = new Date(2020 + Math.floor(Math.random() * 4), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28));
      
      return {
        tagNumber: `${selectedSpecies.substring(0, 3).toUpperCase()}-${new Date().getFullYear()}-${String(i + 1).padStart(3, '0')}`,
        name: names[Math.floor(Math.random() * names.length)],
        species: selectedSpecies,
        breed: selectedBreed,
        gender: Math.random() > 0.5 ? 'female' : 'male',
        birthDate: birthDate,
        acquisitionDate: new Date(birthDate.getTime() + Math.random() * 365 * 24 * 60 * 60 * 1000),
        weight: Math.floor(Math.random() * 500) + 100,
        status: Math.random() > 0.1 ? 'active' : 'sold',
        healthStatus: Math.random() > 0.2 ? 'healthy' : 'sick',
        location: locations[Math.floor(Math.random() * locations.length)],
        rfidTag: `RFID-${String(i + 1).padStart(6, '0')}`,
        notes: `Imported from ${Math.random() > 0.5 ? 'Limpopo' : 'Free State'} province`,
        createdAt: new Date(),
        updatedAt: new Date()
      };
    });
  }

  // Generate health records
  generateHealthRecords(animals) {
    const recordTypes = ['Vaccination', 'Treatment', 'Checkup', 'Surgery', 'Medication'];
    const treatments = ['Deworming', 'Vitamin B12', 'Antibiotic', 'Foot Rot Treatment', 'Pregnancy Check'];
    const veterinarians = ['Dr. Smith', 'Dr. Patel', 'Dr. Van Der Merwe', 'Dr. Mthembu', 'Dr. Johnson'];

    return Array.from({ length: 50 }, (_, i) => {
      const animal = animals[Math.floor(Math.random() * animals.length)];
      const recordType = recordTypes[Math.floor(Math.random() * recordTypes.length)];
      const date = new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28));
      
      return {
        animalId: animal.tagNumber,
        animalTagNumber: animal.tagNumber,
        type: recordType,
        date: date,
        treatment: treatments[Math.floor(Math.random() * treatments.length)],
        veterinarian: veterinarians[Math.floor(Math.random() * veterinarians.length)],
        cost: Math.floor(Math.random() * 500) + 50,
        notes: `${recordType} administered successfully. Animal responded well.`,
        nextDueDate: recordType === 'Vaccination' ? new Date(date.getTime() + 365 * 24 * 60 * 60 * 1000) : null,
        createdAt: new Date(),
        updatedAt: new Date()
      };
    });
  }

  // Generate breeding records
  generateBreedingRecords(animals) {
    const femaleAnimals = animals.filter(a => a.gender === 'female');
    const maleAnimals = animals.filter(a => a.gender === 'male');

    return Array.from({ length: 15 }, (_, i) => {
      const dam = femaleAnimals[Math.floor(Math.random() * femaleAnimals.length)];
      const sire = maleAnimals[Math.floor(Math.random() * maleAnimals.length)];
      const breedingDate = new Date(2024, Math.floor(Math.random() * 8), Math.floor(Math.random() * 28));
      const expectedDate = new Date(breedingDate.getTime() + 280 * 24 * 60 * 60 * 1000); // ~9 months

      return {
        damId: dam.tagNumber,
        sireId: sire.tagNumber,
        breedingDate: breedingDate,
        expectedBirthDate: expectedDate,
        actualBirthDate: Math.random() > 0.5 ? new Date(expectedDate.getTime() + (Math.random() - 0.5) * 14 * 24 * 60 * 60 * 1000) : null,
        status: Math.random() > 0.3 ? 'pregnant' : 'completed',
        method: Math.random() > 0.5 ? 'natural' : 'artificial_insemination',
        notes: 'Breeding program as per genetic improvement plan',
        offspring: Math.random() > 0.7 ? Math.floor(Math.random() * 3) + 1 : 0,
        createdAt: new Date(),
        updatedAt: new Date()
      };
    });
  }

  // Generate financial records
  generateFinancialRecords(animals) {
    const transactionTypes = ['income', 'expense'];
    const categories = {
      income: ['Animal Sale', 'Milk Sale', 'Wool Sale', 'Breeding Fee', 'Government Subsidy'],
      expense: ['Feed Purchase', 'Veterinary', 'Equipment', 'Labor', 'Transport', 'Insurance']
    };

    return Array.from({ length: 40 }, (_, i) => {
      const type = transactionTypes[Math.floor(Math.random() * transactionTypes.length)];
      const category = categories[type][Math.floor(Math.random() * categories[type].length)];
      const date = new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28));
      
      return {
        type: type,
        category: category,
        amount: type === 'income' ? Math.floor(Math.random() * 10000) + 1000 : Math.floor(Math.random() * 5000) + 100,
        date: date,
        description: `${category} - ${type === 'income' ? 'Revenue' : 'Operational expense'}`,
        animalId: Math.random() > 0.5 ? animals[Math.floor(Math.random() * animals.length)].tagNumber : null,
        paymentMethod: Math.random() > 0.5 ? 'bank_transfer' : 'cash',
        reference: `TXN-${String(i + 1).padStart(6, '0')}`,
        createdAt: new Date(),
        updatedAt: new Date()
      };
    });
  }

  // Generate feeding records
  generateFeedingRecords(animals) {
    const feedTypes = ['Hay', 'Silage', 'Concentrate', 'Pasture', 'Supplements'];
    const suppliers = ['AgriCorp', 'FarmFeeds SA', 'Nutri-Stock', 'Premium Feeds', 'Local Co-op'];

    return Array.from({ length: 35 }, (_, i) => {
      const feedType = feedTypes[Math.floor(Math.random() * feedTypes.length)];
      const date = new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28));
      
      return {
        animalId: animals[Math.floor(Math.random() * animals.length)].tagNumber,
        feedType: feedType,
        quantity: Math.floor(Math.random() * 50) + 5,
        unit: 'kg',
        date: date,
        cost: Math.floor(Math.random() * 200) + 20,
        supplier: suppliers[Math.floor(Math.random() * suppliers.length)],
        nutritionalValue: {
          protein: Math.floor(Math.random() * 20) + 5,
          energy: Math.floor(Math.random() * 15) + 10,
          fiber: Math.floor(Math.random() * 25) + 15
        },
        notes: `Quality ${feedType.toLowerCase()} for optimal nutrition`,
        createdAt: new Date(),
        updatedAt: new Date()
      };
    });
  }

  // Generate inventory records
  generateInventoryRecords() {
    const categories = ['Feed', 'Medicine', 'Equipment', 'Supplies', 'Tools'];
    const items = {
      'Feed': ['Lucerne Hay', 'Maize Meal', 'Protein Lick', 'Mineral Supplement'],
      'Medicine': ['Dewormer', 'Antibiotics', 'Vitamins', 'Vaccines'],
      'Equipment': ['Water Troughs', 'Feeders', 'Gates', 'Fencing'],
      'Supplies': ['Ear Tags', 'Marking Paint', 'Syringes', 'Gloves'],
      'Tools': ['Hoof Trimmer', 'Weighing Scale', 'Thermometer', 'Stethoscope']
    };

    return Array.from({ length: 20 }, (_, i) => {
      const category = categories[Math.floor(Math.random() * categories.length)];
      const item = items[category][Math.floor(Math.random() * items[category].length)];
      
      return {
        itemName: item,
        category: category,
        quantity: Math.floor(Math.random() * 100) + 10,
        unit: category === 'Feed' ? 'kg' : 'units',
        unitPrice: Math.floor(Math.random() * 500) + 50,
        totalValue: 0, // Will be calculated
        supplier: 'AgriSupplies SA',
        location: 'Main Store',
        expiryDate: category === 'Medicine' || category === 'Feed' ? 
          new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000) : null,
        minimumStock: Math.floor(Math.random() * 20) + 5,
        status: 'in_stock',
        createdAt: new Date(),
        updatedAt: new Date()
      };
    });
  }

  async populateCollection(collectionName, data) {
    try {
      const collection = this.db.collection(collectionName);
      
      // Clear existing data
      await collection.deleteMany({});
      console.log(`🗑️  Cleared existing ${collectionName} data`);
      
      // Insert new data
      const result = await collection.insertMany(data);
      console.log(`✅ Inserted ${result.insertedCount} records into ${collectionName}`);
      
      return result;
    } catch (error) {
      console.error(`❌ Error populating ${collectionName}:`, error);
      throw error;
    }
  }

  async populateAllData() {
    try {
      console.log('🚀 Starting data population...');
      
      // Generate all data
      const animals = this.generateAnimals();
      const healthRecords = this.generateHealthRecords(animals);
      const breedingRecords = this.generateBreedingRecords(animals);
      const financialRecords = this.generateFinancialRecords(animals);
      const feedingRecords = this.generateFeedingRecords(animals);
      const inventoryRecords = this.generateInventoryRecords();

      // Calculate total values for inventory
      inventoryRecords.forEach(item => {
        item.totalValue = item.quantity * item.unitPrice;
      });

      // Populate collections
      await this.populateCollection('animals', animals);
      await this.populateCollection('health_records', healthRecords);
      await this.populateCollection('breeding_records', breedingRecords);
      await this.populateCollection('financial_transactions', financialRecords);
      await this.populateCollection('feeding_records', feedingRecords);
      await this.populateCollection('inventory_items', inventoryRecords);

      console.log('🎉 All data populated successfully!');
      
      // Print summary
      console.log('\n📊 Data Summary:');
      console.log(`- Animals: ${animals.length}`);
      console.log(`- Health Records: ${healthRecords.length}`);
      console.log(`- Breeding Records: ${breedingRecords.length}`);
      console.log(`- Financial Transactions: ${financialRecords.length}`);
      console.log(`- Feeding Records: ${feedingRecords.length}`);
      console.log(`- Inventory Items: ${inventoryRecords.length}`);
      
    } catch (error) {
      console.error('❌ Error during data population:', error);
      throw error;
    }
  }
}

// Run the script
async function main() {
  const populator = new DataPopulator();
  
  try {
    await populator.connect();
    await populator.populateAllData();
  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  } finally {
    await populator.disconnect();
  }
}

// Execute if run directly
if (require.main === module) {
  main();
}

module.exports = DataPopulator;
