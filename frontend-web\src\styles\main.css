/**
 * AgriIntel Main CSS Architecture
 * Comprehensive external stylesheet system with zero inline CSS violations
 */

/* ===== IMPORTS ===== */
@import url('./base/reset.css');
@import url('./base/variables.css');
@import url('./base/typography.css');
@import url('./base/accessibility.css');

/* Component Styles */
@import url('./components/navigation.css');
@import url('./components/buttons.css');
@import url('./components/cards.css');
@import url('./components/forms.css');
@import url('./components/modals.css');
@import url('./components/tables.css');
@import url('./components/charts.css');
@import url('./components/glassmorphism.css');
@import url('./components/modern-animations.css');

/* Layout Styles */
@import url('./layouts/dashboard.css');
@import url('./layouts/sidebar.css');
@import url('./layouts/header.css');
@import url('./layouts/footer.css');

/* Page Styles */
@import url('./pages/landing.css');
@import url('./auth-pages.css');
@import url('./pages/dashboard.css');
@import url('./pages/modules.css');

/* Theme Styles */
@import url('./themes/light.css');
@import url('./themes/dark.css');
@import url('./themes/agriintel.css');

/* Utility Styles */
@import url('./utilities/spacing.css');
@import url('./utilities/colors.css');
@import url('./utilities/animations.css');
@import url('./utilities/responsive.css');

/* ===== GLOBAL STYLES ===== */

/* Root Element */
html {
  font-size: 16px;
  scroll-behavior: smooth;
  /*
   * Text size adjustment for mobile browsers
   * -webkit-text-size-adjust: iOS Safari, Chrome Mobile
   * -moz-text-size-adjust: Firefox Mobile (limited support)
   * -ms-text-size-adjust: IE Mobile (legacy)
   * text-size-adjust: Standard property (limited support)
   * Graceful degradation: Browsers without support use default behavior
   */
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

/* Body */
body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  color: var(--color-text-primary);
  background-color: var(--color-background-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Root Container */
#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Main Content */
main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* ===== COMPONENT CLASSES ===== */

/* Navigation */
.agri-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-navigation);
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border-bottom: 1px solid var(--glass-border);
  padding: var(--spacing-md) 0;
}

.agri-nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.agri-nav-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.agri-nav-logo {
  height: 48px;
  width: auto;
  object-fit: contain;
}

.agri-nav-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.agri-nav-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* Buttons */
.agri-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-lg);
  font-family: inherit;
  font-size: var(--font-size-base);
  font-weight: 600;
  text-decoration: none;
  text-transform: none;
  cursor: pointer;
  transition: var(--transition-normal);
  -webkit-user-select: none;
  user-select: none;
  white-space: nowrap;
}

.agri-btn:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}

.agri-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.agri-btn-primary {
  background: var(--agri-pro-gradient);
  color: var(--color-white);
  box-shadow: 0 4px 15px rgba(46, 125, 50, 0.3);
}

.agri-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(46, 125, 50, 0.4);
}

.agri-btn-secondary {
  background: var(--agri-beta-gradient);
  color: var(--color-white);
  box-shadow: 0 4px 15px rgba(245, 124, 0, 0.3);
}

.agri-btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(245, 124, 0, 0.4);
}

.agri-btn-outline {
  background: transparent;
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.agri-btn-outline:hover {
  background: var(--color-background-hover);
  border-color: var(--color-primary);
}

.agri-btn-ghost {
  background: transparent;
  color: var(--color-text-primary);
  border: none;
}

.agri-btn-ghost:hover {
  background: var(--color-background-hover);
}

/* Cards */
.agri-card {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--glass-shadow);
  transition: var(--transition-normal);
  overflow: hidden;
}

.agri-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.agri-card-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--glass-border);
}

.agri-card-body {
  padding: var(--spacing-lg);
}

.agri-card-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--glass-border);
  background: rgba(255, 255, 255, 0.05);
}

/* Forms */
.agri-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.agri-form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.agri-form-label {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--color-text-primary);
}

.agri-form-input {
  padding: var(--spacing-md);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  font-family: inherit;
  font-size: var(--font-size-base);
  background: rgba(255, 255, 255, 0.1);
  color: var(--color-text-primary);
  transition: var(--transition-normal);
}

.agri-form-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(46, 125, 50, 0.2);
}

.agri-form-input::placeholder {
  color: var(--color-text-secondary);
}

/* Layout */
.agri-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.agri-section {
  padding: var(--spacing-4xl) 0;
}

.agri-grid {
  display: grid;
  gap: var(--spacing-lg);
}

.agri-grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.agri-grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.agri-grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* Responsive Grid */
@media (max-width: 768px) {
  .agri-grid-2,
  .agri-grid-3,
  .agri-grid-4 {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .agri-grid-3,
  .agri-grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Flex Utilities */
.agri-flex {
  display: flex;
}

.agri-flex-col {
  flex-direction: column;
}

.agri-flex-center {
  align-items: center;
  justify-content: center;
}

.agri-flex-between {
  justify-content: space-between;
}

.agri-flex-wrap {
  flex-wrap: wrap;
}

.agri-flex-1 {
  flex: 1;
}

/* Text Utilities */
.agri-text-center {
  text-align: center;
}

.agri-text-left {
  text-align: left;
}

.agri-text-right {
  text-align: right;
}

.agri-text-gradient {
  background: var(--agri-pro-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Visibility Utilities */
.agri-hidden {
  display: none;
}

.agri-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* ===== ERROR BOUNDARY STYLES ===== */
.error-boundary-container {
  padding: 20px;
  border: 1px solid #ff6b6b;
  border-radius: 4px;
  background-color: #ffe0e0;
  color: #d63031;
  margin: 16px;
  font-family: var(--font-family);
}
