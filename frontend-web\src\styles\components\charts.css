/**
 * Chart Component Styles
 * Professional chart designs for AgriIntel platform
 */

/* ===== CHART CONTAINER ===== */

.agri-chart-container {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  box-shadow: var(--glass-shadow);
  margin-bottom: var(--spacing-lg);
}

.agri-chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
}

.agri-chart-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.agri-chart-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: var(--spacing-xs) 0 0 0;
}

.agri-chart-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* ===== CHART WRAPPER ===== */

.agri-chart-wrapper {
  position: relative;
  width: 100%;
  height: 400px;
  overflow: hidden;
}

.agri-chart-wrapper.sm {
  height: 250px;
}

.agri-chart-wrapper.md {
  height: 350px;
}

.agri-chart-wrapper.lg {
  height: 500px;
}

.agri-chart-wrapper.xl {
  height: 600px;
}

/* ===== CHART TYPES ===== */

/* Line Chart */
.agri-line-chart {
  width: 100%;
  height: 100%;
}

.agri-line-chart .recharts-line {
  stroke-width: 3px;
}

.agri-line-chart .recharts-dot {
  fill: var(--agri-primary);
  stroke: var(--color-white);
  stroke-width: 2px;
  r: 4;
}

.agri-line-chart .recharts-dot:hover {
  r: 6;
  fill: var(--agri-primary-dark);
}

/* Bar Chart */
.agri-bar-chart {
  width: 100%;
  height: 100%;
}

.agri-bar-chart .recharts-bar {
  fill: var(--agri-primary);
}

.agri-bar-chart .recharts-bar:hover {
  fill: var(--agri-primary-dark);
}

/* Area Chart */
.agri-area-chart {
  width: 100%;
  height: 100%;
}

.agri-area-chart .recharts-area {
  fill: url(#agriGradient);
  stroke: var(--agri-primary);
  stroke-width: 2px;
}

/* Pie Chart */
.agri-pie-chart {
  width: 100%;
  height: 100%;
}

.agri-pie-chart .recharts-pie-sector {
  stroke: var(--color-white);
  stroke-width: 2px;
}

.agri-pie-chart .recharts-pie-sector:hover {
  filter: brightness(1.1);
}

/* ===== CHART LEGEND ===== */

.agri-chart-legend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--color-border);
  flex-wrap: wrap;
}

.agri-legend-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
}

.agri-legend-color {
  width: 12px;
  height: 12px;
  border-radius: var(--radius-sm);
  flex-shrink: 0;
}

/* ===== CHART TOOLTIP ===== */

.agri-chart-tooltip {
  background: var(--color-background-primary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-lg);
  font-size: var(--font-size-sm);
}

.agri-tooltip-label {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

.agri-tooltip-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xs);
}

.agri-tooltip-item:last-child {
  margin-bottom: 0;
}

.agri-tooltip-color {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  flex-shrink: 0;
}

.agri-tooltip-name {
  color: var(--color-text-secondary);
}

.agri-tooltip-value {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

/* ===== CHART GRID ===== */

.agri-chart-grid {
  stroke: var(--color-border);
  stroke-dasharray: 3 3;
  opacity: 0.5;
}

/* ===== CHART AXES ===== */

.agri-chart-axis {
  font-size: var(--font-size-xs);
  fill: var(--color-text-secondary);
}

.agri-chart-axis-line {
  stroke: var(--color-border);
}

.agri-chart-tick-line {
  stroke: var(--color-border);
}

/* ===== CHART COLORS ===== */

.agri-chart-color-primary {
  fill: var(--agri-primary);
}

.agri-chart-color-secondary {
  fill: var(--agri-secondary);
}

.agri-chart-color-accent {
  fill: var(--agri-accent);
}

.agri-chart-color-success {
  fill: var(--color-success);
}

.agri-chart-color-warning {
  fill: var(--color-warning);
}

.agri-chart-color-error {
  fill: var(--color-error);
}

.agri-chart-color-info {
  fill: var(--color-info);
}

/* ===== CHART GRADIENTS ===== */

.agri-chart-gradient-primary {
  stop-color: var(--agri-primary);
}

.agri-chart-gradient-secondary {
  stop-color: var(--agri-secondary);
}

.agri-chart-gradient-accent {
  stop-color: var(--agri-accent);
}

/* ===== CHART LOADING ===== */

.agri-chart-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--color-text-secondary);
  flex-direction: column;
  gap: var(--spacing-md);
}

.agri-chart-loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-border);
  border-top: 3px solid var(--agri-primary);
  border-radius: var(--radius-full);
  animation: agri-chart-spin 1s linear infinite;
}

@keyframes agri-chart-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.agri-chart-loading-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* ===== CHART EMPTY STATE ===== */

.agri-chart-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--color-text-secondary);
  flex-direction: column;
  gap: var(--spacing-md);
}

.agri-chart-empty-icon {
  font-size: var(--font-size-4xl);
  opacity: 0.5;
}

.agri-chart-empty-message {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
}

.agri-chart-empty-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-disabled);
  text-align: center;
  max-width: 300px;
}

/* ===== CHART CONTROLS ===== */

.agri-chart-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.agri-chart-filter {
  min-width: 120px;
}

.agri-chart-export {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  background: var(--color-background-primary);
  color: var(--color-text-primary);
  cursor: pointer;
  transition: var(--transition-normal);
  font-size: var(--font-size-xs);
}

.agri-chart-export:hover {
  background: var(--color-background-hover);
  border-color: var(--agri-primary);
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
  .agri-chart-container {
    padding: var(--spacing-md);
  }
  
  .agri-chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
  
  .agri-chart-wrapper {
    height: 300px;
  }
  
  .agri-chart-wrapper.lg,
  .agri-chart-wrapper.xl {
    height: 350px;
  }
  
  .agri-chart-legend {
    gap: var(--spacing-md);
  }
  
  .agri-chart-controls {
    flex-direction: column;
    align-items: stretch;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .agri-chart-wrapper {
    height: 250px;
  }
  
  .agri-chart-wrapper.lg,
  .agri-chart-wrapper.xl {
    height: 300px;
  }
  
  .agri-chart-legend {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  .agri-chart-loading-spinner {
    animation: none;
  }
  
  .agri-line-chart .recharts-dot:hover,
  .agri-bar-chart .recharts-bar:hover,
  .agri-pie-chart .recharts-pie-sector:hover {
    transition: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .agri-chart-container {
    background: var(--color-background-primary);
    border: 2px solid var(--color-text-primary);
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
  }
  
  .agri-chart-grid {
    stroke: var(--color-text-primary);
    opacity: 0.3;
  }
  
  .agri-chart-axis,
  .agri-chart-axis-line,
  .agri-chart-tick-line {
    stroke: var(--color-text-primary);
    fill: var(--color-text-primary);
  }
}
