import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Grid,
  Typography,
  <PERSON>ton,
  Card,
  CardContent,
  Box,
  Chip,
  Rating,
  CircularProgress,
  Avatar,
  Divider,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Tooltip,
  Paper,
  Tab,
  Tabs,
  Stack,
  useTheme,
  alpha,
  TextField
} from '@mui/material';
import {
  PlayArrow,
  ArrowForward,
  Star,
  Pets,
  LocalHospital,
  TrendingUp,
  Agriculture,
  Security,
  Analytics,
  Support,
  Language,
  Brightness4,
  Brightness7,
  CheckCircle,
  Verified,
  TrendingUpOutlined,
  PeopleOutline,
  BusinessCenter,
  Phone,
  Email,
  LocationOn,
  Dashboard,
  HealthAndSafety,
  Restaurant,
  Assessment,
  Inventory,
  Store,
  Gavel,
  Settings,
  Lock,
  Upgrade
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';

// Import styles and utilities
import '../styles/modern-saas-landing.css';
import '../styles/components/glassmorphism.css';
import '../styles/components/modern-animations.css';
import '../styles/agricultural-sections.css';
import {
  getRandomBackgroundImage,
  generateBackgroundStyle,
  preloadCriticalImages,
  getHeroImages
} from '../utils/imageOptimization';

// Types and Interfaces
interface SubscriptionPlan {
  id: string;
  name: string;
  price: string;
  period: string;
  description: string;
  theme: 'beta' | 'professional' | 'enterprise';
  popular: boolean;
  badge: string;
  badgeColor: string;
  features: string[];
  ctaText: string;
  route: string;
  limitations?: string[];
  highlights?: string[];
  isEnterprise?: boolean;
  isWhatsApp?: boolean;
  whatsappNumber?: string;
  whatsappMessage?: string;
}

interface FeatureHighlight {
  icon: React.ReactElement;
  title: string;
  description: string;
  gradient: string;
}

interface TestimonialData {
  name: string;
  role: string;
  company: string;
  rating: number;
  comment: string;
  avatar: string;
  location: string;
}



const AgriIntelLanding: React.FC = () => {
  const navigate = useNavigate();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingPlan, setLoadingPlan] = useState<string | null>(null);
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const [isDarkMode, setIsDarkMode] = useState(false);

  // South African Languages
  const languages = [
    { code: 'en', name: 'English' },
    { code: 'af', name: 'Afrikaans' },
    { code: 'zu', name: 'isiZulu' },
    { code: 'xh', name: 'isiXhosa' },
    { code: 'st', name: 'Sesotho' },
    { code: 'tn', name: 'Setswana' },
    { code: 'ss', name: 'siSwati' },
    { code: 've', name: 'Tshivenda' },
    { code: 'ts', name: 'Xitsonga' },
    { code: 'nr', name: 'isiNdebele' },
    { code: 'nso', name: 'Sepedi' }
  ];

  // Livestock images for rotating background
  const livestockImages = [
    `${process.env.PUBLIC_URL}/images/animals/cattle-1.jpeg`,
    `${process.env.PUBLIC_URL}/images/animals/cattle-2.avif`,
    `${process.env.PUBLIC_URL}/images/modules/animals/cattle-3.jpeg`,
    `${process.env.PUBLIC_URL}/images/modules/animals/cattle-4.jpeg`,
    `${process.env.PUBLIC_URL}/images/animals/cattle-5.avif`,
    `${process.env.PUBLIC_URL}/images/animals/cattle-6.jpeg`
  ];

  // Rotate background images every 6 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prev) => (prev + 1) % livestockImages.length);
    }, 6000);
    return () => clearInterval(interval);
  }, [livestockImages.length]);

  // Two-tier subscription structure (BETA V1 and Professional V1)
  const subscriptionPlans: SubscriptionPlan[] = [
    {
      id: 'beta',
      name: 'BETA V1',
      price: 'Free',
      period: 'Exhibition Trial',
      description: 'Perfect starter kit for small-scale South African farmers with exhibition-only features',
      theme: 'beta',
      popular: false,
      badge: 'FREE TRIAL',
      badgeColor: '#FFA726', // Yellow accent
      features: [
        '🐄 Up to 50 animals (exhibition limit)',
        '📊 Basic dashboard overview',
        '💰 Simple financial tracking',
        '🍃 Basic feed management',
        '📚 Resources & government programs',
        '📧 Email support only',
        '📱 No mobile app access',
        '⏰ 30-day trial period'
      ],
      limitations: [
        'Limited to 50 animals maximum',
        'Excel-only reports',
        'No AI automation features',
        'No marketplace access'
      ],
      ctaText: 'Start Free Trial',
      route: '/login-beta'
    },
    {
      id: 'professional',
      name: 'Professional V1',
      price: 'R699',
      period: 'per month',
      description: 'Complete livestock management solution with AI automation and marketplace connections',
      theme: 'professional',
      popular: true,
      badge: 'MOST POPULAR',
      badgeColor: '#2E7D32', // Emerald green accent
      features: [
        '🐄 Unlimited animals',
        '🤖 AI-powered insights & automation',
        '📱 Full mobile app access',
        '🏪 Marketplace connections (Uber-style)',
        '👨‍⚕️ Veterinarian network access',
        '🚚 Supplier & auctioneer connections',
        '🔒 Security service partnerships',
        '💳 In-app payment system',
        '📊 Advanced analytics & reports',
        '☎️ Priority phone support',
        '🌍 Multi-language support'
      ],
      highlights: [
        'AI automation saves 10+ hours weekly',
        'Marketplace increases revenue by 25%',
        'Priority support with 2-hour response'
      ],
      ctaText: 'Go Professional',
      route: '/login-pro'
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      price: 'Custom',
      period: 'Contact Us',
      description: 'Large-scale farming operations with custom solutions and dedicated support',
      theme: 'enterprise',
      popular: false,
      badge: 'CUSTOM SOLUTION',
      badgeColor: '#1565C0', // Deep blue accent
      features: [
        '🏢 Multi-farm management',
        '👥 Unlimited user accounts',
        '🔧 Custom integrations',
        '📈 Advanced business intelligence',
        '🎯 Dedicated account manager',
        '⚡ 24/7 priority support',
        '🔐 Enterprise security',
        '📋 Custom reporting',
        '🌐 API access',
        '🎓 Training & onboarding'
      ],
      highlights: [
        'Dedicated account manager',
        'Custom integrations available',
        '24/7 enterprise support'
      ],
      ctaText: 'Contact Sales',
      route: '/enterprise-contact',
      isWhatsApp: true,
      whatsappNumber: '**********',
      whatsappMessage: 'Hi! I\'m interested in AgriIntel Enterprise solutions for large-scale farming operations. Please provide more information about custom pricing and features.'
    }
  ];

  // Module tabs for tabbed interface
  const moduleTabs = [
    {
      id: 'overview',
      label: 'Overview',
      icon: <Dashboard />,
      description: 'Complete farm management dashboard'
    },
    {
      id: 'animals',
      label: 'Animals',
      icon: <Pets />,
      description: 'Smart livestock tracking & management'
    },
    {
      id: 'health',
      label: 'Health',
      icon: <HealthAndSafety />,
      description: 'Proactive health monitoring & alerts'
    },
    {
      id: 'feeding',
      label: 'Feeding',
      icon: <Restaurant />,
      description: 'Optimized nutrition & feed management'
    },
    {
      id: 'financial',
      label: 'Financial',
      icon: <TrendingUp />,
      description: 'Profit tracking & financial insights'
    },
    {
      id: 'reports',
      label: 'Reports',
      icon: <Assessment />,
      description: 'Advanced analytics & reporting'
    }
  ];

  // Tab state
  const [activeTab, setActiveTab] = useState('overview');

  // Background image rotation
  const [currentBackground, setCurrentBackground] = useState(() =>
    getRandomBackgroundImage('landing')
  );

  // Rotate background images every 10 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentBackground(getRandomBackgroundImage('landing'));
    }, 10000);

    return () => clearInterval(interval);
  }, []);

  // Preload critical images on component mount
  useEffect(() => {
    preloadCriticalImages();
  }, []);

  // Feature highlights
  const features: FeatureHighlight[] = [
    {
      icon: <Pets className="feature-icon" />,
      title: 'Smart Animal Management',
      description: 'AI-powered tracking of health, breeding, and performance with predictive insights',
      gradient: 'linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%)'
    },
    {
      icon: <LocalHospital className="feature-icon" />,
      title: 'Health Monitoring',
      description: 'Proactive health alerts with veterinary network integration',
      gradient: 'linear-gradient(135deg, #2196F3 0%, #1565C0 100%)'
    },
    {
      icon: <TrendingUp className="feature-icon" />,
      title: 'Financial Analytics',
      description: 'Maximize profits with detailed financial tracking and market insights',
      gradient: 'linear-gradient(135deg, #FF9800 0%, #F57C00 100%)'
    },
    {
      icon: <Agriculture className="feature-icon" />,
      title: 'Feed Optimization',
      description: 'Optimize feed costs and nutrition for better growth and health',
      gradient: 'linear-gradient(135deg, #8BC34A 0%, #689F38 100%)'
    },
    {
      icon: <BusinessCenter className="feature-icon" />,
      title: 'Marketplace Integration',
      description: 'Connect with veterinarians, suppliers, and buyers through our platform',
      gradient: 'linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%)'
    },
    {
      icon: <Security className="feature-icon" />,
      title: 'Security & Compliance',
      description: 'Ensure compliance with South African agricultural regulations',
      gradient: 'linear-gradient(135deg, #F44336 0%, #D32F2F 100%)'
    }
  ];

  // Testimonials
  const testimonials: TestimonialData[] = [
    {
      name: 'Sipho Mthembu',
      role: 'Cattle Farmer',
      company: 'Mthembu Livestock Farm',
      rating: 5,
      comment: 'AgriIntel transformed my farming operation. The AI insights helped me increase my herd productivity by 30%.',
      avatar: `${process.env.PUBLIC_URL}/images/testimonials/farmer1.jpg`,
      location: 'KwaZulu-Natal'
    },
    {
      name: 'Maria van der Merwe',
      role: 'Dairy Farm Owner',
      company: 'Sunshine Dairy',
      rating: 5,
      comment: 'The marketplace feature connected me with reliable suppliers. My feed costs dropped by 20%.',
      avatar: `${process.env.PUBLIC_URL}/images/testimonials/farmer2.jpg`,
      location: 'Western Cape'
    },
    {
      name: 'Thabo Molefe',
      role: 'Mixed Farming',
      company: 'Molefe Agricultural Enterprise',
      rating: 5,
      comment: 'Professional support and AI automation saved me hours of manual work every week.',
      avatar: `${process.env.PUBLIC_URL}/images/testimonials/farmer3.jpg`,
      location: 'Gauteng'
    }
  ];

  const handlePlanSelect = async (planId: string) => {
    setLoadingPlan(planId);
    setIsLoading(true);

    // Brief loading animation for better UX
    await new Promise(resolve => setTimeout(resolve, 800));

    // Handle different plan types
    if (planId === 'beta') {
      navigate('/login-beta');
    } else if (planId === 'professional') {
      navigate('/login-pro');
    } else if (planId === 'enterprise') {
      // Open WhatsApp for Enterprise contact
      const plan = subscriptionPlans.find(p => p.id === 'enterprise');
      if (plan && plan.whatsappNumber && plan.whatsappMessage) {
        const whatsappUrl = `https://wa.me/${plan.whatsappNumber.replace(/\D/g, '')}?text=${encodeURIComponent(plan.whatsappMessage)}`;
        window.open(whatsappUrl, '_blank');
      }
    }

    setIsLoading(false);
    setLoadingPlan(null);
  };

  const handleLanguageChange = (event: any) => {
    setSelectedLanguage(event.target.value);
  };

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
  };



  return (
    <main className={`modern-saas-landing ${isDarkMode ? 'dark-mode' : 'light-mode'}`} role="main">
      {/* Dynamic Background with Authentic South African Livestock Photography */}
      <div className="landing-background-container">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentBackground.id}
            className="landing-background"
            style={generateBackgroundStyle(currentBackground.id)}
            initial={{ opacity: 0, scale: 1.1 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 2, ease: "easeInOut" }}
          />
        </AnimatePresence>
        <div className="background-overlay" />
      </div>

      {/* Navigation Header */}
      <motion.nav
        className="landing-navigation"
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        role="navigation"
        aria-label="Main navigation"
      >
        <Container maxWidth="xl">
          <Box className="nav-container">
            <div className="nav-brand">
              <div className="agriintel-logo-container">
                <div className="logo-wrapper">
                  <img
                    src={`${process.env.PUBLIC_URL}/images/logo/AgriIntel Logo with Bright Accents and Livestock.png`}
                    alt="AgriIntel - Smart Livestock Management"
                    className="agriintel-logo-image"
                  />
                  <Typography variant="caption" className="logo-tagline">
                    Smart Livestock Management Platform
                  </Typography>
                </div>
              </div>
              <Chip
                label="Production Ready"
                className="nav-badge"
                size="small"
              />
            </div>

            <div className="nav-controls">
              {/* Language Selector */}
              <FormControl size="small" className="language-selector">
                <Select
                  value={selectedLanguage}
                  onChange={handleLanguageChange}
                  startAdornment={<Language />}
                >
                  {languages.map((lang) => (
                    <MenuItem key={lang.code} value={lang.code}>
                      {lang.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              {/* Theme Toggle */}
              <Tooltip title={isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}>
                <IconButton
                  onClick={toggleTheme}
                  className="theme-toggle"
                  aria-label={isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
                  type="button"
                >
                  {isDarkMode ? <Brightness7 /> : <Brightness4 />}
                </IconButton>
              </Tooltip>
            </div>

            {/* Professional Navigation Menu */}
            <div className="nav-menu">
              <Button
                className="nav-link"
                onClick={() => document.getElementById('features')?.scrollIntoView({ behavior: 'smooth' })}
              >
                Features
              </Button>
              <Button
                className="nav-link"
                onClick={() => document.getElementById('pricing')?.scrollIntoView({ behavior: 'smooth' })}
              >
                Pricing
              </Button>
              <Button
                className="nav-link"
                onClick={() => document.getElementById('testimonials')?.scrollIntoView({ behavior: 'smooth' })}
              >
                Testimonials
              </Button>
              <Button
                className="nav-link"
                onClick={() => document.getElementById('about')?.scrollIntoView({ behavior: 'smooth' })}
              >
                About
              </Button>
            </div>

            <div className="nav-actions">
              <Button
                variant="outlined"
                className="nav-button nav-button-outline"
                onClick={() => navigate('/login-beta')}
                type="button"
                aria-label="Start BETA V1 free trial"
              >
                Try BETA V1
              </Button>
              <Button
                variant="contained"
                className="nav-button nav-button-primary"
                onClick={() => navigate('/login-pro')}
                type="button"
                aria-label="Access Professional V1 features"
              >
                Go Professional
              </Button>
            </div>
          </Box>
        </Container>
      </motion.nav>

      {/* Hero Section with Tabbed Interface */}
      <section className="landing-section hero-section" aria-labelledby="hero-title">
        <Container maxWidth="xl">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="hero-content"
          >
            <Typography variant="h1" className="hero-title" id="hero-title">
              Transform Your Livestock Management
              <br />
              <span className="hero-title-gradient">
                with AI-Powered Intelligence
              </span>
            </Typography>

            <Typography variant="h5" className="hero-subtitle">
              Join 2,500+ South African farmers using AgriIntel to increase productivity,
              reduce costs, and connect with industry professionals through our
              comprehensive livestock management platform.
            </Typography>

            {/* Module Tabs Interface */}
            <Box sx={{ mt: 4, mb: 4 }}>
              <Paper className="glass-card" sx={{ p: 0, overflow: 'hidden' }}>
                <Tabs
                  value={activeTab}
                  onChange={(e, newValue) => setActiveTab(newValue)}
                  variant="scrollable"
                  scrollButtons="auto"
                  sx={{
                    borderBottom: '1px solid var(--glass-border)',
                    '& .MuiTab-root': {
                      color: 'var(--agri-white)',
                      fontSize: 'var(--font-size-base)',
                      fontWeight: 600,
                      textTransform: 'none',
                      minHeight: 64,
                      '&.Mui-selected': {
                        color: 'var(--agri-accent)',
                      }
                    },
                    '& .MuiTabs-indicator': {
                      backgroundColor: 'var(--agri-accent)',
                      height: 3
                    }
                  }}
                >
                  {moduleTabs.map((tab) => (
                    <Tab
                      key={tab.id}
                      value={tab.id}
                      label={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {tab.icon}
                          <Box sx={{ textAlign: 'left' }}>
                            <Typography variant="body1" sx={{ fontWeight: 600 }}>
                              {tab.label}
                            </Typography>
                            <Typography variant="caption" sx={{ opacity: 0.8 }}>
                              {tab.description}
                            </Typography>
                          </Box>
                        </Box>
                      }
                      sx={{ alignItems: 'flex-start', textAlign: 'left' }}
                    />
                  ))}
                </Tabs>

                {/* Tab Content */}
                <Box sx={{ p: 3 }}>
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={activeTab}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ duration: 0.3 }}
                    >
                      {activeTab === 'overview' && (
                        <Grid container spacing={3}>
                          <Grid item xs={12} md={6}>
                            <Typography variant="h4" sx={{ color: 'var(--agri-white)', mb: 2, fontWeight: 700 }}>
                              Complete Farm Management Dashboard
                            </Typography>
                            <Typography variant="body1" sx={{ color: 'var(--agri-white)', opacity: 0.9, mb: 3, fontSize: 'var(--font-size-base)' }}>
                              Get a comprehensive view of your entire livestock operation with real-time insights,
                              predictive analytics, and automated alerts that help you make informed decisions.
                            </Typography>
                            <Stack direction="row" spacing={2}>
                              <Chip icon={<CheckCircle />} label="Real-time Monitoring" sx={{ color: 'var(--agri-white)' }} />
                              <Chip icon={<CheckCircle />} label="Predictive Analytics" sx={{ color: 'var(--agri-white)' }} />
                              <Chip icon={<CheckCircle />} label="Automated Alerts" sx={{ color: 'var(--agri-white)' }} />
                            </Stack>
                          </Grid>
                          <Grid item xs={12} md={6}>
                            <Box sx={{
                              background: 'rgba(255,255,255,0.1)',
                              borderRadius: 'var(--radius-lg)',
                              p: 3,
                              border: '1px solid var(--glass-border)'
                            }}>
                              <Typography variant="h6" sx={{ color: 'var(--agri-white)', mb: 2 }}>
                                Key Metrics
                              </Typography>
                              <Grid container spacing={2}>
                                <Grid item xs={4}>
                                  <Typography variant="h4" sx={{ color: 'var(--agri-accent)', fontWeight: 700 }}>30%</Typography>
                                  <Typography variant="caption" sx={{ color: 'var(--agri-white)' }}>Productivity Increase</Typography>
                                </Grid>
                                <Grid item xs={4}>
                                  <Typography variant="h4" sx={{ color: 'var(--agri-accent)', fontWeight: 700 }}>R2.5M</Typography>
                                  <Typography variant="caption" sx={{ color: 'var(--agri-white)' }}>Revenue Generated</Typography>
                                </Grid>
                                <Grid item xs={4}>
                                  <Typography variant="h4" sx={{ color: 'var(--agri-accent)', fontWeight: 700 }}>2,500+</Typography>
                                  <Typography variant="caption" sx={{ color: 'var(--agri-white)' }}>Active Farmers</Typography>
                                </Grid>
                              </Grid>
                            </Box>
                          </Grid>
                        </Grid>
                      )}

                      {activeTab === 'animals' && (
                        <Grid container spacing={3}>
                          <Grid item xs={12} md={6}>
                            <Typography variant="h4" sx={{ color: 'var(--agri-white)', mb: 2, fontWeight: 700 }}>
                              Smart Livestock Tracking & Management
                            </Typography>
                            <Typography variant="body1" sx={{ color: 'var(--agri-white)', opacity: 0.9, mb: 3, fontSize: 'var(--font-size-base)' }}>
                              Track individual animals with RFID technology, monitor growth patterns, breeding cycles,
                              and health status with AI-powered insights that predict optimal breeding and feeding times.
                            </Typography>
                            <Stack direction="row" spacing={2} flexWrap="wrap">
                              <Chip icon={<CheckCircle />} label="RFID Tracking" sx={{ color: 'var(--agri-white)' }} />
                              <Chip icon={<CheckCircle />} label="Growth Monitoring" sx={{ color: 'var(--agri-white)' }} />
                              <Chip icon={<CheckCircle />} label="Breeding Management" sx={{ color: 'var(--agri-white)' }} />
                            </Stack>
                          </Grid>
                          <Grid item xs={12} md={6}>
                            <Box sx={{
                              background: 'rgba(255,255,255,0.1)',
                              borderRadius: 'var(--radius-lg)',
                              p: 3,
                              border: '1px solid var(--glass-border)'
                            }}>
                              <Typography variant="h6" sx={{ color: 'var(--agri-white)', mb: 2 }}>
                                Animal Management Features
                              </Typography>
                              <Stack spacing={1}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                  <Typography sx={{ color: 'var(--agri-white)' }}>Individual Profiles</Typography>
                                  <CheckCircle sx={{ color: 'var(--agri-pro-primary)' }} />
                                </Box>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                  <Typography sx={{ color: 'var(--agri-white)' }}>Genealogy Tracking</Typography>
                                  <CheckCircle sx={{ color: 'var(--agri-pro-primary)' }} />
                                </Box>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                  <Typography sx={{ color: 'var(--agri-white)' }}>Performance Analytics</Typography>
                                  <CheckCircle sx={{ color: 'var(--agri-pro-primary)' }} />
                                </Box>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                  <Typography sx={{ color: 'var(--agri-white)' }}>Retirement Planning</Typography>
                                  <CheckCircle sx={{ color: 'var(--agri-pro-primary)' }} />
                                </Box>
                              </Stack>
                            </Box>
                          </Grid>
                        </Grid>
                      )}

                      {/* Add similar content for other tabs */}
                      {activeTab !== 'overview' && activeTab !== 'animals' && (
                        <Box sx={{ textAlign: 'center', py: 4 }}>
                          <Typography variant="h5" sx={{ color: 'var(--agri-white)', mb: 2 }}>
                            {moduleTabs.find(tab => tab.id === activeTab)?.label} Module
                          </Typography>
                          <Typography variant="body1" sx={{ color: 'var(--agri-white)', opacity: 0.8 }}>
                            {moduleTabs.find(tab => tab.id === activeTab)?.description}
                          </Typography>
                        </Box>
                      )}
                    </motion.div>
                  </AnimatePresence>
                </Box>
              </Paper>
            </Box>

              <Grid container spacing={6} alignItems="center">
                <Grid item xs={12} lg={6}>
                  <motion.div
                    className="hero-content-text"
                    initial={{ opacity: 0, x: -30 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.8, ease: "easeOut", delay: 0.2 }}
                  >
                    <div className="hero-actions">
                      <Button
                        variant="contained"
                        size="large"
                        className="cta-primary"
                        onClick={() => handlePlanSelect('beta')}
                        disabled={isLoading}
                        startIcon={loadingPlan === 'beta' ? <CircularProgress size={20} /> : <PlayArrow />}
                        type="button"
                        aria-label="Start your free 30-day trial with AgriIntel BETA"
                      >
                        {loadingPlan === 'beta' ? 'Loading...' : 'Start Free Trial'}
                      </Button>
                      <Button
                        variant="outlined"
                        size="large"
                        className="cta-secondary"
                        onClick={() => handlePlanSelect('professional')}
                        disabled={isLoading}
                        endIcon={loadingPlan === 'professional' ? <CircularProgress size={20} /> : <ArrowForward />}
                        type="button"
                        aria-label="Upgrade to AgriIntel Professional for R699 per month"
                      >
                        {loadingPlan === 'professional' ? 'Loading...' : 'Go Professional'}
                      </Button>
                    </div>

                    <div className="hero-rating">
                      <Rating value={5} readOnly size="large" />
                      <Typography variant="h6">
                        4.9/5 from 2,500+ verified farmers
                      </Typography>
                    </div>
                  </motion.div>
                </Grid>

                <Grid item xs={12} lg={6}>
                  <motion.div
                    className="hero-visual"
                    initial={{ opacity: 0, x: 60 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 1, ease: "easeOut", delay: 0.3 }}
                  >
                    <div className="hero-dashboard-preview">
                      <img
                        src={`${process.env.PUBLIC_URL}/images/dashboard/main-dashboard.jpg`}
                        alt="AgriIntel Dashboard Preview"
                        className="hero-dashboard-image"
                      />
                      <div className="dashboard-overlay">
                        <div className="overlay-badge">
                          <Star />
                          <span>Live Dashboard</span>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                </Grid>
              </Grid>
          </motion.div>
        </Container>
      </section>

      {/* Features Section */}
      <section className="features-section" id="features">
        <Container maxWidth="xl" sx={{ py: { xs: 8, md: 12 } }}>
              <motion.div
                className="section-header"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <Typography variant="h2" className="section-title">
                  Comprehensive Livestock Management Features
                </Typography>
                <Typography variant="h6" className="section-subtitle">
                  Everything you need to manage your livestock operation efficiently
                </Typography>
              </motion.div>

              <Grid container spacing={4}>
                {features.map((feature, index) => (
                  <Grid item xs={12} md={6} lg={4} key={index}>
                    <motion.div
                      initial={{ opacity: 0, y: 30 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: index * 0.1 }}
                      viewport={{ once: true }}
                    >
                      <Card className="feature-card">
                        <CardContent>
                          <div className="feature-icon-container">
                            {feature.icon}
                          </div>
                          <Typography variant="h5" className="feature-title">
                            {feature.title}
                          </Typography>
                          <Typography variant="body1" className="feature-description">
                            {feature.description}
                          </Typography>
                        </CardContent>
                      </Card>
                    </motion.div>
                  </Grid>
                ))}
              </Grid>
        </Container>
      </section>

      {/* Livestock Services Section */}
      <section className="livestock-services-section" id="livestock-services">
        <Container maxWidth="xl" sx={{ py: { xs: 8, md: 12 } }}>
          <motion.div
            className="section-header"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Typography variant="h2" className="section-title">
              Comprehensive Livestock Services
            </Typography>
            <Typography variant="h6" className="section-subtitle">
              Specialized management solutions for all types of livestock
            </Typography>
          </motion.div>

          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <Card className="service-card cattle-service">
                  <div className="service-image-container">
                    <img
                      src={`${process.env.PUBLIC_URL}/images/modules/animals/cattle-1.jpeg`}
                      alt="Cattle Management Services"
                      className="service-image"
                    />
                    <div className="service-overlay">
                      <Typography variant="h4" className="service-title">
                        Cattle Management
                      </Typography>
                    </div>
                  </div>
                  <CardContent className="service-content">
                    <Typography variant="h6" className="service-subtitle">
                      Comprehensive Cattle Solutions
                    </Typography>
                    <Typography variant="body1" className="service-description">
                      Advanced breeding programs, health monitoring, feed optimization, and market-ready cattle preparation.
                    </Typography>
                    <div className="service-features">
                      <div className="feature-item">🐄 Breeding Management</div>
                      <div className="feature-item">💉 Health Monitoring</div>
                      <div className="feature-item">🌾 Feed Optimization</div>
                      <div className="feature-item">📊 Performance Analytics</div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={6}>
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <Card className="service-card poultry-service">
                  <div className="service-image-container">
                    <img
                      src={`${process.env.PUBLIC_URL}/images/modules/animals/poultry-1.jpeg`}
                      alt="Poultry Management Services"
                      className="service-image"
                    />
                    <div className="service-overlay">
                      <Typography variant="h4" className="service-title">
                        Poultry Management
                      </Typography>
                    </div>
                  </div>
                  <CardContent className="service-content">
                    <Typography variant="h6" className="service-subtitle">
                      Modern Poultry Solutions
                    </Typography>
                    <Typography variant="body1" className="service-description">
                      Egg production tracking, broiler management, disease prevention, and automated feeding systems.
                    </Typography>
                    <div className="service-features">
                      <div className="feature-item">🥚 Egg Production Tracking</div>
                      <div className="feature-item">🐔 Broiler Management</div>
                      <div className="feature-item">🏥 Disease Prevention</div>
                      <div className="feature-item">🤖 Automated Systems</div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          </Grid>
        </Container>
      </section>

      {/* Agricultural Products Section */}
      <section className="agricultural-products-section" id="agricultural-products">
        <Container maxWidth="xl" sx={{ py: { xs: 8, md: 12 } }}>
          <motion.div
            className="section-header"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Typography variant="h2" className="section-title">
              Premium Agricultural Products
            </Typography>
            <Typography variant="h6" className="section-subtitle">
              Quality crops and produce with competitive pricing
            </Typography>
          </motion.div>

          <Grid container spacing={4}>
            <Grid item xs={12} md={4}>
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="product-card">
                  <div className="product-image-container">
                    <img
                      src={`${process.env.PUBLIC_URL}/images/products/maize-crop.jpeg`}
                      alt="Maize Crops"
                      className="product-image"
                    />
                    <div className="product-price-tag">
                      <Typography variant="h6">R4,500/ton</Typography>
                    </div>
                  </div>
                  <CardContent>
                    <Typography variant="h6" className="product-title">
                      Premium Maize
                    </Typography>
                    <Typography variant="body2" className="product-description">
                      High-quality yellow maize, perfect for livestock feed and human consumption.
                    </Typography>
                    <div className="product-specs">
                      <span>🌽 Grade A Quality</span>
                      <span>📦 Bulk Available</span>
                      <span>🚚 Free Delivery</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={4}>
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <Card className="product-card">
                  <div className="product-image-container">
                    <img
                      src={`${process.env.PUBLIC_URL}/images/products/soybean-crop.jpeg`}
                      alt="Soybean Crops"
                      className="product-image"
                    />
                    <div className="product-price-tag">
                      <Typography variant="h6">R8,200/ton</Typography>
                    </div>
                  </div>
                  <CardContent>
                    <Typography variant="h6" className="product-title">
                      Organic Soybeans
                    </Typography>
                    <Typography variant="body2" className="product-description">
                      Protein-rich soybeans for premium livestock feed and oil production.
                    </Typography>
                    <div className="product-specs">
                      <span>🌱 Organic Certified</span>
                      <span>💪 High Protein</span>
                      <span>🏆 Premium Grade</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={4}>
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                viewport={{ once: true }}
              >
                <Card className="product-card">
                  <div className="product-image-container">
                    <img
                      src={`${process.env.PUBLIC_URL}/images/products/lucerne-hay.jpeg`}
                      alt="Lucerne Hay"
                      className="product-image"
                    />
                    <div className="product-price-tag">
                      <Typography variant="h6">R180/bale</Typography>
                    </div>
                  </div>
                  <CardContent>
                    <Typography variant="h6" className="product-title">
                      Premium Lucerne Hay
                    </Typography>
                    <Typography variant="body2" className="product-description">
                      Nutrient-rich lucerne hay for optimal livestock nutrition and health.
                    </Typography>
                    <div className="product-specs">
                      <span>🌿 High Nutrition</span>
                      <span>📏 Standard Bales</span>
                      <span>☀️ Sun Dried</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          </Grid>
        </Container>
      </section>

      {/* Pricing Section */}
      <section className="pricing-section" id="pricing">
        <Container maxWidth="xl" sx={{ py: { xs: 8, md: 12 } }}>
              <motion.div
                className="section-header"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <Typography variant="h2" className="section-title">
                  Choose Your AgriIntel Plan
                </Typography>
                <Typography variant="h6" className="section-subtitle">
                  Two-tier system designed for South African farmers
                </Typography>
              </motion.div>

              <Grid container spacing={4} justifyContent="center">
                {subscriptionPlans.map((plan, index) => (
                  <Grid item xs={12} md={6} lg={4} key={plan.id}>
                    <motion.div
                      initial={{ opacity: 0, y: 30 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: index * 0.2 }}
                      viewport={{ once: true }}
                    >
                      <Card
                        className={`pricing-card pricing-card-${plan.theme} ${plan.popular ? 'popular' : ''}`}
                        onClick={() => handlePlanSelect(plan.id)}
                        role="button"
                        tabIndex={0}
                        aria-label={`Select ${plan.name} plan for ${plan.price} ${plan.period}`}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            handlePlanSelect(plan.id);
                          }
                        }}
                      >
                        <div className="plan-badge-container">
                          <Chip
                            label={plan.badge}
                            className="plan-badge"
                            sx={{
                              backgroundColor: plan.badgeColor,
                              color: 'white',
                              fontWeight: 'bold'
                            }}
                          />
                        </div>

                        <CardContent className="pricing-content">
                          <Typography variant="h4" className="plan-name">
                            {plan.name}
                          </Typography>
                          <Typography variant="body1" className="plan-description">
                            {plan.description}
                          </Typography>

                          <div className="plan-pricing">
                            <Typography variant="h2" className="plan-price">
                              {plan.price}
                            </Typography>
                            <Typography variant="body2" className="plan-period">
                              {plan.period}
                            </Typography>
                          </div>

                          <div className="plan-features">
                            {plan.features.map((feature, idx) => (
                              <div key={idx} className="feature-item">
                                <CheckCircle className="feature-check" />
                                <Typography variant="body2">{feature}</Typography>
                              </div>
                            ))}
                          </div>

                          {plan.limitations && (
                            <div className="plan-limitations">
                              <Typography variant="subtitle2" className="limitations-title">
                                Limitations:
                              </Typography>
                              {plan.limitations.map((limitation, idx) => (
                                <Typography key={idx} variant="caption" className="limitation-item">
                                  • {limitation}
                                </Typography>
                              ))}
                            </div>
                          )}

                          {plan.highlights && (
                            <div className="plan-highlights">
                              {plan.highlights.map((highlight, idx) => (
                                <div key={idx} className="highlight-item">
                                  <Verified className="highlight-icon" />
                                  <Typography variant="caption">{highlight}</Typography>
                                </div>
                              ))}
                            </div>
                          )}

                          <Button
                            variant="contained"
                            size="large"
                            className={`plan-cta plan-cta-${plan.theme}`}
                            fullWidth
                            disabled={loadingPlan === plan.id}
                            startIcon={loadingPlan === plan.id ? <CircularProgress size={20} /> : null}
                            type="button"
                            aria-label={`${plan.ctaText} - ${plan.name} plan`}
                          >
                            {loadingPlan === plan.id ? 'Loading...' : plan.ctaText}
                          </Button>
                        </CardContent>
                      </Card>
                    </motion.div>
                  </Grid>
                ))}
              </Grid>
        </Container>
      </section>

      {/* Innovation/Technology Section */}
      <section className="innovation-technology-section" id="innovation">
        <Container maxWidth="xl" sx={{ py: { xs: 8, md: 12 } }}>
          <motion.div
            className="section-header"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Typography variant="h2" className="section-title">
              Smart Farming Technology
            </Typography>
            <Typography variant="h6" className="section-subtitle">
              Cutting-edge tools and AI automation for modern agriculture
            </Typography>
          </motion.div>

          <Grid container spacing={4}>
            <Grid item xs={12} md={6} lg={3}>
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="tech-card">
                  <div className="tech-icon-container">
                    <div className="tech-icon">🤖</div>
                  </div>
                  <CardContent>
                    <Typography variant="h6" className="tech-title">
                      AI Automation
                    </Typography>
                    <Typography variant="body2" className="tech-description">
                      Intelligent systems that automate feeding schedules, health monitoring, and breeding programs.
                    </Typography>
                    <div className="tech-features">
                      <span>• Automated alerts</span>
                      <span>• Predictive analytics</span>
                      <span>• Smart scheduling</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={6} lg={3}>
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <Card className="tech-card">
                  <div className="tech-icon-container">
                    <div className="tech-icon">🚁</div>
                  </div>
                  <CardContent>
                    <Typography variant="h6" className="tech-title">
                      Drone Monitoring
                    </Typography>
                    <Typography variant="body2" className="tech-description">
                      Aerial surveillance for pasture management, livestock tracking, and crop monitoring.
                    </Typography>
                    <div className="tech-features">
                      <span>• Pasture analysis</span>
                      <span>• Livestock counting</span>
                      <span>• Crop health assessment</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={6} lg={3}>
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                viewport={{ once: true }}
              >
                <Card className="tech-card">
                  <div className="tech-icon-container">
                    <div className="tech-icon">📱</div>
                  </div>
                  <CardContent>
                    <Typography variant="h6" className="tech-title">
                      Mobile App
                    </Typography>
                    <Typography variant="body2" className="tech-description">
                      Manage your farm on-the-go with our comprehensive mobile application.
                    </Typography>
                    <div className="tech-features">
                      <span>• Real-time monitoring</span>
                      <span>• Offline capability</span>
                      <span>• Push notifications</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={6} lg={3}>
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                viewport={{ once: true }}
              >
                <Card className="tech-card">
                  <div className="tech-icon-container">
                    <div className="tech-icon">🌐</div>
                  </div>
                  <CardContent>
                    <Typography variant="h6" className="tech-title">
                      IoT Sensors
                    </Typography>
                    <Typography variant="body2" className="tech-description">
                      Smart sensors for environmental monitoring, feed levels, and water quality.
                    </Typography>
                    <div className="tech-features">
                      <span>• Environmental data</span>
                      <span>• Feed monitoring</span>
                      <span>• Water quality</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          </Grid>

          {/* Technology Showcase */}
          <motion.div
            className="tech-showcase"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            viewport={{ once: true }}
            style={{ marginTop: '4rem' }}
          >
            <Grid container spacing={4} alignItems="center">
              <Grid item xs={12} lg={6}>
                <div className="tech-showcase-content">
                  <Typography variant="h4" className="showcase-title">
                    Revolutionizing South African Agriculture
                  </Typography>
                  <Typography variant="body1" className="showcase-description">
                    Our AI-powered platform combines traditional farming wisdom with cutting-edge technology,
                    helping South African farmers increase productivity by up to 35% while reducing operational costs.
                  </Typography>
                  <div className="tech-stats">
                    <div className="stat-item">
                      <Typography variant="h3" className="stat-number">35%</Typography>
                      <Typography variant="body2" className="stat-label">Productivity Increase</Typography>
                    </div>
                    <div className="stat-item">
                      <Typography variant="h3" className="stat-number">25%</Typography>
                      <Typography variant="body2" className="stat-label">Cost Reduction</Typography>
                    </div>
                    <div className="stat-item">
                      <Typography variant="h3" className="stat-number">50+</Typography>
                      <Typography variant="body2" className="stat-label">Farms Connected</Typography>
                    </div>
                  </div>
                </div>
              </Grid>
              <Grid item xs={12} lg={6}>
                <div className="tech-showcase-image">
                  <img
                    src={`${process.env.PUBLIC_URL}/images/technology/smart-farming-dashboard.jpeg`}
                    alt="Smart Farming Technology Dashboard"
                    className="showcase-image"
                  />
                </div>
              </Grid>
            </Grid>
          </motion.div>
        </Container>
      </section>

      {/* Testimonials Section */}
      <section className="testimonials-section" id="testimonials">
        <Container maxWidth="xl" sx={{ py: { xs: 8, md: 12 } }}>
              <motion.div
                className="section-header"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <Typography variant="h2" className="section-title">
                  Trusted by South African Farmers
                </Typography>
                <Typography variant="h6" className="section-subtitle">
                  Real stories from farmers who transformed their operations with AgriIntel
                </Typography>
              </motion.div>

              <Grid container spacing={4}>
                {testimonials.map((testimonial, index) => (
                  <Grid item xs={12} md={4} key={index}>
                    <motion.div
                      initial={{ opacity: 0, y: 30 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: index * 0.1 }}
                      viewport={{ once: true }}
                    >
                      <Card className="testimonial-card">
                        <CardContent>
                          <div className="testimonial-header">
                            <Avatar
                              src={testimonial.avatar}
                              alt={testimonial.name}
                              className="testimonial-avatar"
                            />
                            <div className="testimonial-info">
                              <Typography variant="h6" className="testimonial-name">
                                {testimonial.name}
                              </Typography>
                              <Typography variant="body2" className="testimonial-role">
                                {testimonial.role}
                              </Typography>
                              <Typography variant="caption" className="testimonial-company">
                                {testimonial.company}
                              </Typography>
                              <div className="testimonial-location">
                                <LocationOn fontSize="small" />
                                <Typography variant="caption">{testimonial.location}</Typography>
                              </div>
                            </div>
                          </div>

                          <Rating value={testimonial.rating} readOnly size="small" />

                          <Typography variant="body1" className="testimonial-comment">
                            "{testimonial.comment}"
                          </Typography>
                        </CardContent>
                      </Card>
                    </motion.div>
                  </Grid>
                ))}
              </Grid>
        </Container>
      </section>

      {/* Gallery Section */}
      <section className="gallery-section" id="gallery">
        <Container maxWidth="xl" sx={{ py: { xs: 8, md: 12 } }}>
          <motion.div
            className="section-header"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Typography variant="h2" className="section-title">
              Farm Gallery
            </Typography>
            <Typography variant="h6" className="section-subtitle">
              High-resolution images showcasing modern South African agriculture
            </Typography>
          </motion.div>

          <Grid container spacing={2}>
            <Grid item xs={12} md={6} lg={4}>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.1 }}
                viewport={{ once: true }}
              >
                <div className="gallery-item large">
                  <img
                    src={`${process.env.PUBLIC_URL}/images/gallery/cattle-grazing-sunset.jpeg`}
                    alt="Cattle grazing at sunset in South African pasture"
                    className="gallery-image"
                  />
                  <div className="gallery-overlay">
                    <Typography variant="h6" className="gallery-title">
                      Cattle Grazing at Sunset
                    </Typography>
                    <Typography variant="body2" className="gallery-description">
                      Premium cattle enjoying natural pastures in the Western Cape
                    </Typography>
                  </div>
                </div>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={6} lg={4}>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <div className="gallery-item">
                  <img
                    src={`${process.env.PUBLIC_URL}/images/gallery/modern-poultry-farm.jpeg`}
                    alt="Modern poultry farming facility"
                    className="gallery-image"
                  />
                  <div className="gallery-overlay">
                    <Typography variant="h6" className="gallery-title">
                      Modern Poultry Facility
                    </Typography>
                    <Typography variant="body2" className="gallery-description">
                      State-of-the-art poultry management systems
                    </Typography>
                  </div>
                </div>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={6} lg={4}>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                viewport={{ once: true }}
              >
                <div className="gallery-item">
                  <img
                    src={`${process.env.PUBLIC_URL}/images/gallery/farming-equipment.jpeg`}
                    alt="Advanced farming equipment and machinery"
                    className="gallery-image"
                  />
                  <div className="gallery-overlay">
                    <Typography variant="h6" className="gallery-title">
                      Advanced Equipment
                    </Typography>
                    <Typography variant="body2" className="gallery-description">
                      Latest farming machinery and technology
                    </Typography>
                  </div>
                </div>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={6} lg={4}>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                viewport={{ once: true }}
              >
                <div className="gallery-item">
                  <img
                    src={`${process.env.PUBLIC_URL}/images/gallery/crop-fields.jpeg`}
                    alt="Lush crop fields in South Africa"
                    className="gallery-image"
                  />
                  <div className="gallery-overlay">
                    <Typography variant="h6" className="gallery-title">
                      Crop Fields
                    </Typography>
                    <Typography variant="body2" className="gallery-description">
                      Thriving maize and soybean crops
                    </Typography>
                  </div>
                </div>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={6} lg={4}>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.5 }}
                viewport={{ once: true }}
              >
                <div className="gallery-item">
                  <img
                    src={`${process.env.PUBLIC_URL}/images/gallery/farmer-technology.jpeg`}
                    alt="Farmer using AgriIntel technology"
                    className="gallery-image"
                  />
                  <div className="gallery-overlay">
                    <Typography variant="h6" className="gallery-title">
                      Smart Farming
                    </Typography>
                    <Typography variant="body2" className="gallery-description">
                      Farmers embracing digital agriculture
                    </Typography>
                  </div>
                </div>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={6} lg={4}>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                viewport={{ once: true }}
              >
                <div className="gallery-item">
                  <img
                    src={`${process.env.PUBLIC_URL}/images/gallery/livestock-health.jpeg`}
                    alt="Veterinarian checking livestock health"
                    className="gallery-image"
                  />
                  <div className="gallery-overlay">
                    <Typography variant="h6" className="gallery-title">
                      Health Monitoring
                    </Typography>
                    <Typography variant="body2" className="gallery-description">
                      Professional veterinary care and monitoring
                    </Typography>
                  </div>
                </div>
              </motion.div>
            </Grid>
          </Grid>
        </Container>
      </section>

      {/* Contact Section */}
      <section className="contact-section" id="contact">
        <Container maxWidth="xl" sx={{ py: { xs: 8, md: 12 } }}>
          <motion.div
            className="section-header"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Typography variant="h2" className="section-title">
              Get In Touch
            </Typography>
            <Typography variant="h6" className="section-subtitle">
              Ready to transform your farming operation? Contact us today
            </Typography>
          </motion.div>

          <Grid container spacing={6}>
            <Grid item xs={12} lg={8}>
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <Card className="contact-form-card">
                  <CardContent>
                    <Typography variant="h5" className="form-title">
                      Send us a Message
                    </Typography>
                    <form className="contact-form">
                      <Grid container spacing={3}>
                        <Grid item xs={12} md={6}>
                          <TextField
                            fullWidth
                            label="Full Name"
                            variant="outlined"
                            required
                            className="contact-input"
                          />
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <TextField
                            fullWidth
                            label="Email Address"
                            type="email"
                            variant="outlined"
                            required
                            className="contact-input"
                          />
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <TextField
                            fullWidth
                            label="Phone Number"
                            variant="outlined"
                            className="contact-input"
                          />
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <TextField
                            fullWidth
                            label="Farm Size (hectares)"
                            variant="outlined"
                            className="contact-input"
                          />
                        </Grid>
                        <Grid item xs={12}>
                          <TextField
                            fullWidth
                            label="Message"
                            multiline
                            rows={4}
                            variant="outlined"
                            required
                            className="contact-input"
                          />
                        </Grid>
                        <Grid item xs={12}>
                          <Button
                            type="submit"
                            variant="contained"
                            size="large"
                            className="contact-submit-btn"
                            fullWidth
                          >
                            Send Message
                          </Button>
                        </Grid>
                      </Grid>
                    </form>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>

            <Grid item xs={12} lg={4}>
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <div className="contact-info">
                  <div className="contact-item">
                    <div className="contact-icon">📱</div>
                    <div className="contact-details">
                      <Typography variant="h6" className="contact-title">
                        WhatsApp
                      </Typography>
                      <Typography variant="body1" className="contact-value">
                        **********
                      </Typography>
                      <Button
                        variant="outlined"
                        size="small"
                        className="whatsapp-btn"
                        onClick={() => {
                          const message = "Hi! I'm interested in AgriIntel livestock management solutions. Please provide more information.";
                          window.open(`https://wa.me/27794484159?text=${encodeURIComponent(message)}`, '_blank');
                        }}
                      >
                        Chat on WhatsApp
                      </Button>
                    </div>
                  </div>

                  <div className="contact-item">
                    <div className="contact-icon">📞</div>
                    <div className="contact-details">
                      <Typography variant="h6" className="contact-title">
                        Phone
                      </Typography>
                      <Typography variant="body1" className="contact-value">
                        +27 79 448 4159
                      </Typography>
                    </div>
                  </div>

                  <div className="contact-item">
                    <div className="contact-icon">📧</div>
                    <div className="contact-details">
                      <Typography variant="h6" className="contact-title">
                        Email
                      </Typography>
                      <Typography variant="body1" className="contact-value">
                        <EMAIL>
                      </Typography>
                    </div>
                  </div>

                  <div className="contact-item">
                    <div className="contact-icon">📍</div>
                    <div className="contact-details">
                      <Typography variant="h6" className="contact-title">
                        Address
                      </Typography>
                      <Typography variant="body1" className="contact-value">
                        123 Agricultural Drive<br />
                        Pretoria, Gauteng 0001<br />
                        South Africa
                      </Typography>
                    </div>
                  </div>

                  <div className="contact-item">
                    <div className="contact-icon">🕒</div>
                    <div className="contact-details">
                      <Typography variant="h6" className="contact-title">
                        Business Hours
                      </Typography>
                      <Typography variant="body1" className="contact-value">
                        Monday - Friday: 8:00 AM - 6:00 PM<br />
                        Saturday: 9:00 AM - 2:00 PM<br />
                        Sunday: Closed
                      </Typography>
                    </div>
                  </div>
                </div>
              </motion.div>
            </Grid>
          </Grid>
        </Container>
      </section>

      {/* About Section */}
      <section className="about-section" id="about">
        <Container maxWidth="xl" sx={{ py: { xs: 8, md: 12 } }}>
              <Grid container spacing={6} alignItems="center">
                <Grid item xs={12} lg={6}>
                  <motion.div
                    className="about-content"
                    initial={{ opacity: 0, x: -30 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.8 }}
                    viewport={{ once: true }}
                  >
                    <Typography variant="h2" className="about-title">
                      About AgriIntel
                    </Typography>
                    <Typography variant="h5" className="about-subtitle">
                      Empowering South African Agriculture Through Technology
                    </Typography>

                    <Typography variant="body1" className="about-description">
                      Founded by <strong>May and Caiphus</strong>, visionary leaders dedicated to solving
                      critical challenges in global agriculture. Our mission is to introduce modern technological
                      practices to the agricultural sector, empowering farmers, veterinarians, and industry partners
                      with the tools they need to thrive in a changing world.
                    </Typography>

                    <Typography variant="body1" className="about-description">
                      We believe in leveraging data for precision agriculture, enhancing food security through
                      technology, optimizing supply chains with predictive analytics, and promoting sustainable
                      farming practices for a better future.
                    </Typography>

                    <div className="about-features">
                      <div className="about-feature">
                        <TrendingUpOutlined className="about-feature-icon" />
                        <div>
                          <Typography variant="h6">AI-Powered Insights</Typography>
                          <Typography variant="body2">Advanced algorithms for predictive analytics</Typography>
                        </div>
                      </div>
                      <div className="about-feature">
                        <PeopleOutline className="about-feature-icon" />
                        <div>
                          <Typography variant="h6">Community Network</Typography>
                          <Typography variant="body2">Connect with farmers, vets, and suppliers</Typography>
                        </div>
                      </div>
                      <div className="about-feature">
                        <Security className="about-feature-icon" />
                        <div>
                          <Typography variant="h6">Secure & Compliant</Typography>
                          <Typography variant="body2">Meets South African agricultural standards</Typography>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                </Grid>

                <Grid item xs={12} lg={6}>
                  <motion.div
                    className="about-visual"
                    initial={{ opacity: 0, x: 30 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.8 }}
                    viewport={{ once: true }}
                  >
                    <div className="about-image-container">
                      <img
                        src={`${process.env.PUBLIC_URL}/images/gallery/cattle-farm-1.jpg`}
                        alt="Modern South African Farm"
                        className="about-image"
                      />
                      <div className="about-overlay">
                        <div className="overlay-content">
                          <Typography variant="h4">2,500+</Typography>
                          <Typography variant="body2">Farmers Trust AgriIntel</Typography>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                </Grid>
              </Grid>

              {/* Sponsors Section within About Tab */}
              <motion.div
                className="sponsors-section-inline"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                style={{ marginTop: '4rem' }}
              >
                <Typography variant="h4" className="sponsors-title">
                  Supported by Leading Agricultural Organizations
                </Typography>

                <div className="sponsors-grid">
                  <motion.div
                    className="sponsor-item"
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5 }}
                    viewport={{ once: true }}
                  >
                    <img
                      src={`${process.env.PUBLIC_URL}/images/sponsors/Agricultural research council.png`}
                      alt="Agricultural Research Council"
                      className="sponsor-logo"
                    />
                  </motion.div>
                  <motion.div
                    className="sponsor-item"
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.1 }}
                    viewport={{ once: true }}
                  >
                    <img
                      src={`${process.env.PUBLIC_URL}/images/sponsors/Agriculture , land reform & rural development.png`}
                      alt="Department of Agriculture, Land Reform and Rural Development"
                      className="sponsor-logo"
                    />
                  </motion.div>
                  <motion.div
                    className="sponsor-item"
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                    viewport={{ once: true }}
                  >
                    <img
                      src={`${process.env.PUBLIC_URL}/images/sponsors/National Agricultural Marketing Council.png`}
                      alt="National Agricultural Marketing Council"
                      className="sponsor-logo"
                    />
                  </motion.div>
                  <motion.div
                    className="sponsor-item"
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                    viewport={{ once: true }}
                  >
                    <img
                      src={`${process.env.PUBLIC_URL}/images/sponsors/land bank.png`}
                      alt="Land Bank"
                      className="sponsor-logo"
                    />
                  </motion.div>
                </div>
              </motion.div>
        </Container>
      </section>



      {/* Footer */}
      <footer className="landing-footer">
        <Container maxWidth="xl">
          <Grid container spacing={6}>
            <Grid item xs={12} md={4}>
              <div className="footer-brand">
                <img
                  src={`${process.env.PUBLIC_URL}/images/logo/AgriIntel Logo with Bright Accents and Livestock.png`}
                  alt="AgriIntel"
                  className="footer-logo"
                />
                <Typography variant="body1" className="footer-description">
                  Transforming South African agriculture through intelligent livestock management solutions.
                </Typography>
                <div className="footer-contact">
                  <div className="contact-item">
                    <Email fontSize="small" />
                    <Typography variant="body2"><EMAIL></Typography>
                  </div>
                  <div className="contact-item">
                    <Phone fontSize="small" />
                    <Typography variant="body2">+27 11 123 4567</Typography>
                  </div>
                  <div className="contact-item">
                    <LocationOn fontSize="small" />
                    <Typography variant="body2">Johannesburg, South Africa</Typography>
                  </div>
                </div>
              </div>
            </Grid>

            <Grid item xs={12} md={2}>
              <div className="footer-section">
                <Typography variant="h6" className="footer-title">
                  Platform
                </Typography>
                <ul className="footer-links">
                  <li><a href="#features">Features</a></li>
                  <li><a href="#pricing">Pricing</a></li>
                  <li><a href="#testimonials">Testimonials</a></li>
                  <li><a href="#about">About</a></li>
                </ul>
              </div>
            </Grid>

            <Grid item xs={12} md={2}>
              <div className="footer-section">
                <Typography variant="h6" className="footer-title">
                  Support
                </Typography>
                <ul className="footer-links">
                  <li><a href="/help">Help Center</a></li>
                  <li><a href="/contact">Contact Us</a></li>
                  <li><a href="/documentation">Documentation</a></li>
                  <li><a href="/training">Training</a></li>
                </ul>
              </div>
            </Grid>

            <Grid item xs={12} md={2}>
              <div className="footer-section">
                <Typography variant="h6" className="footer-title">
                  Legal
                </Typography>
                <ul className="footer-links">
                  <li><a href="/privacy">Privacy Policy</a></li>
                  <li><a href="/terms">Terms of Service</a></li>
                  <li><a href="/compliance">Compliance</a></li>
                  <li><a href="/security">Security</a></li>
                </ul>
              </div>
            </Grid>

            <Grid item xs={12} md={2}>
              <div className="footer-section">
                <Typography variant="h6" className="footer-title">
                  Connect
                </Typography>
                <div className="footer-cta">
                  <Button
                    variant="contained"
                    size="small"
                    className="footer-cta-button"
                    onClick={() => handlePlanSelect('beta')}
                    startIcon={<PlayArrow />}
                    type="button"
                    aria-label="Start your free trial from footer"
                  >
                    Start Free Trial
                  </Button>
                </div>
              </div>
            </Grid>
          </Grid>

          <Divider className="footer-divider" />

          <div className="footer-bottom">
            <Typography variant="body2" className="footer-copyright">
              © 2024 AgriIntel. All rights reserved. Proudly South African.
            </Typography>
            <div className="footer-badges">
              <Chip
                label="ISO 27001 Certified"
                size="small"
                className="footer-badge"
              />
              <Chip
                label="GDPR Compliant"
                size="small"
                className="footer-badge"
              />
            </div>
          </div>
        </Container>
      </footer>
    </main>
  );
};

export default AgriIntelLanding;
