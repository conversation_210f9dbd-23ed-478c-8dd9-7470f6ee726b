import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Grid,
  Typo<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Box,
  Chip,
  Rating,
  CircularProgress,
  Divider
} from '@mui/material';
import {
  PlayArrow,
  ArrowForward,
  Pets,
  LocalHospital,
  TrendingUp,
  CheckCircle,
  Phone,
  Email
} from '@mui/icons-material';
import { motion } from 'framer-motion';

// Import styles and utilities
import '../styles/modern-saas-landing.css';
import { getRandomBackgroundImage } from '../utils/backgroundUtils';

const AgriIntelLanding: React.FC = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [loadingPlan, setLoadingPlan] = useState<string | null>(null);

  // Background image rotation
  const [currentBackground, setCurrentBackground] = useState(() =>
    getRandomBackgroundImage('landing')
  );

  // Rotate background images every 10 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentBackground(getRandomBackgroundImage('landing'));
    }, 10000);

    return () => clearInterval(interval);
  }, []);

  const handlePlanSelect = async (plan: string) => {
    setIsLoading(true);
    setLoadingPlan(plan);
    
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      if (plan === 'beta') {
        navigate('/login');
      } else if (plan === 'professional') {
        navigate('/login');
      }
    } catch (error) {
      console.error('Navigation error:', error);
    } finally {
      setIsLoading(false);
      setLoadingPlan(null);
    }
  };

  return (
    <main className="landing-main landing-background">
      {/* Navigation */}
      <motion.nav 
        className="landing-nav"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Container maxWidth="xl">
          <Box className="nav-container">
            <div className="nav-left">
              <Typography variant="h4" className="nav-title">
                AgriIntel Platform
              </Typography>
            </div>
            
            <div className="nav-center">
              <Typography variant="h6" className="nav-tagline">
                Smart Livestock Management for South African Farmers
              </Typography>
            </div>
            
            <div className="nav-brand">
              <div className="agriintel-logo-container">
                <img
                  src={`${process.env.PUBLIC_URL}/images/logo/AgriIntel Logo with Bright Accents and Livestock.png`}
                  alt="AgriIntel - Smart Livestock Management"
                  className="agriintel-logo-image"
                />
                <div className="logo-text-container">
                  <Typography variant="h6" className="logo-text-primary">
                    AgriIntel
                  </Typography>
                  <Typography variant="caption" className="logo-text-secondary">
                    Smart Farming Solutions
                  </Typography>
                </div>
                <Chip
                  label="Production Ready"
                  className="nav-badge"
                  size="small"
                />
              </div>
            </div>
          </Box>
        </Container>
      </motion.nav>

      {/* Clean Hero Section */}
      <section className="landing-section hero-section" aria-labelledby="hero-title">
        <Container maxWidth="xl">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="hero-content"
          >
            <Typography variant="h1" className="hero-title" id="hero-title">
              Transform Your Livestock Management
              <br />
              <span className="hero-title-gradient">
                with AI-Powered Intelligence
              </span>
            </Typography>

            <Typography variant="h5" className="hero-subtitle">
              Join 2,500+ South African farmers using AgriIntel to increase productivity,
              reduce costs, and connect with industry professionals.
            </Typography>

            {/* Simple Action Buttons */}
            <div className="hero-actions">
              <Button
                variant="contained"
                size="large"
                className="cta-primary"
                onClick={() => handlePlanSelect('beta')}
                disabled={isLoading}
                startIcon={loadingPlan === 'beta' ? <CircularProgress size={20} /> : <PlayArrow />}
                type="button"
                aria-label="Start your free 30-day trial with AgriIntel BETA"
              >
                {loadingPlan === 'beta' ? 'Loading...' : 'Start Free Trial'}
              </Button>
              <Button
                variant="outlined"
                size="large"
                className="cta-secondary"
                onClick={() => handlePlanSelect('professional')}
                disabled={isLoading}
                endIcon={loadingPlan === 'professional' ? <CircularProgress size={20} /> : <ArrowForward />}
                type="button"
                aria-label="Upgrade to AgriIntel Professional for R699 per month"
              >
                {loadingPlan === 'professional' ? 'Loading...' : 'Go Professional - R699/month'}
              </Button>
            </div>

            {/* Trust Indicators */}
            <div className="hero-trust">
              <Rating value={5} readOnly size="large" />
              <Typography variant="h6" className="trust-text">
                4.9/5 from 2,500+ verified South African farmers
              </Typography>
            </div>
          </motion.div>
        </Container>
      </section>

      {/* Simple Features Overview */}
      <section className="features-overview" id="features">
        <Container maxWidth="xl" sx={{ py: { xs: 6, md: 8 } }}>
          <motion.div
            className="section-header"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Typography variant="h2" className="section-title">
              Everything You Need for Smart Livestock Management
            </Typography>
            <Typography variant="h6" className="section-subtitle">
              Comprehensive tools designed for South African farmers
            </Typography>
          </motion.div>

          <Grid container spacing={4} sx={{ mt: 4 }}>
            <Grid item xs={12} md={4}>
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <Card className="simple-feature-card">
                  <CardContent sx={{ textAlign: 'center', p: 4 }}>
                    <Pets sx={{ fontSize: 60, color: 'var(--agri-accent)', mb: 2 }} />
                    <Typography variant="h5" className="feature-title" sx={{ mb: 2 }}>
                      Animal Management
                    </Typography>
                    <Typography variant="body1" className="feature-description">
                      Track individual animals, monitor health, and manage breeding cycles with RFID technology.
                    </Typography>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={4}>
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="simple-feature-card">
                  <CardContent sx={{ textAlign: 'center', p: 4 }}>
                    <LocalHospital sx={{ fontSize: 60, color: 'var(--agri-accent)', mb: 2 }} />
                    <Typography variant="h5" className="feature-title" sx={{ mb: 2 }}>
                      Health Monitoring
                    </Typography>
                    <Typography variant="body1" className="feature-description">
                      AI-powered health analytics with early disease detection and automated alerts.
                    </Typography>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={4}>
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <Card className="simple-feature-card">
                  <CardContent sx={{ textAlign: 'center', p: 4 }}>
                    <TrendingUp sx={{ fontSize: 60, color: 'var(--agri-accent)', mb: 2 }} />
                    <Typography variant="h5" className="feature-title" sx={{ mb: 2 }}>
                      Analytics & Reports
                    </Typography>
                    <Typography variant="body1" className="feature-description">
                      Comprehensive reporting and predictive analytics to optimize your farm operations.
                    </Typography>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          </Grid>
        </Container>
      </section>

      {/* Simple Pricing Section */}
      <section className="pricing-section" id="pricing">
        <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
          <motion.div
            className="section-header"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Typography variant="h2" className="section-title">
              Choose Your Plan
            </Typography>
            <Typography variant="h6" className="section-subtitle">
              Start free, upgrade when you're ready
            </Typography>
          </motion.div>

          <Grid container spacing={4} justifyContent="center" sx={{ mt: 4 }}>
            {/* BETA Plan */}
            <Grid item xs={12} md={5}>
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <Card className="pricing-card beta-card">
                  <CardContent sx={{ p: 4, textAlign: 'center' }}>
                    <Chip
                      label="FREE TRIAL"
                      sx={{
                        background: 'var(--agri-beta-primary)',
                        color: 'white',
                        fontWeight: 700,
                        mb: 2
                      }}
                    />
                    <Typography variant="h4" sx={{ color: 'var(--agri-white)', fontWeight: 800, mb: 1 }}>
                      BETA V1
                    </Typography>
                    <Typography variant="h2" sx={{ color: 'var(--agri-beta-primary)', fontWeight: 900, mb: 1 }}>
                      FREE
                    </Typography>
                    <Typography variant="body1" sx={{ color: 'var(--agri-white)', opacity: 0.8, mb: 3 }}>
                      30-day free trial • Perfect for small farms
                    </Typography>
                    <Box sx={{ textAlign: 'left', mb: 3 }}>
                      <Typography variant="body2" sx={{ color: 'var(--agri-white)', mb: 1, display: 'flex', alignItems: 'center' }}>
                        <CheckCircle sx={{ color: 'var(--agri-beta-primary)', mr: 1, fontSize: '1rem' }} />
                        Up to 50 animals
                      </Typography>
                      <Typography variant="body2" sx={{ color: 'var(--agri-white)', mb: 1, display: 'flex', alignItems: 'center' }}>
                        <CheckCircle sx={{ color: 'var(--agri-beta-primary)', mr: 1, fontSize: '1rem' }} />
                        5 core modules
                      </Typography>
                      <Typography variant="body2" sx={{ color: 'var(--agri-white)', mb: 1, display: 'flex', alignItems: 'center' }}>
                        <CheckCircle sx={{ color: 'var(--agri-beta-primary)', mr: 1, fontSize: '1rem' }} />
                        Basic reporting
                      </Typography>
                    </Box>
                    <Button
                      variant="contained"
                      fullWidth
                      size="large"
                      onClick={() => handlePlanSelect('beta')}
                      sx={{
                        background: 'var(--agri-beta-primary)',
                        '&:hover': { background: 'var(--agri-beta-secondary)' },
                        fontWeight: 700,
                        py: 1.5
                      }}
                    >
                      Start Free Trial
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>

            {/* Professional Plan */}
            <Grid item xs={12} md={5}>
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="pricing-card pro-card">
                  <CardContent sx={{ p: 4, textAlign: 'center' }}>
                    <Chip
                      label="MOST POPULAR"
                      sx={{
                        background: 'var(--agri-pro-primary)',
                        color: 'white',
                        fontWeight: 700,
                        mb: 2
                      }}
                    />
                    <Typography variant="h4" sx={{ color: 'var(--agri-white)', fontWeight: 800, mb: 1 }}>
                      Professional V1
                    </Typography>
                    <Typography variant="h2" sx={{ color: 'var(--agri-pro-primary)', fontWeight: 900, mb: 1 }}>
                      R699
                    </Typography>
                    <Typography variant="body1" sx={{ color: 'var(--agri-white)', opacity: 0.8, mb: 3 }}>
                      per month • Full access to all features
                    </Typography>
                    <Box sx={{ textAlign: 'left', mb: 3 }}>
                      <Typography variant="body2" sx={{ color: 'var(--agri-white)', mb: 1, display: 'flex', alignItems: 'center' }}>
                        <CheckCircle sx={{ color: 'var(--agri-pro-primary)', mr: 1, fontSize: '1rem' }} />
                        Unlimited animals
                      </Typography>
                      <Typography variant="body2" sx={{ color: 'var(--agri-white)', mb: 1, display: 'flex', alignItems: 'center' }}>
                        <CheckCircle sx={{ color: 'var(--agri-pro-primary)', mr: 1, fontSize: '1rem' }} />
                        All 12 modules
                      </Typography>
                      <Typography variant="body2" sx={{ color: 'var(--agri-white)', mb: 1, display: 'flex', alignItems: 'center' }}>
                        <CheckCircle sx={{ color: 'var(--agri-pro-primary)', mr: 1, fontSize: '1rem' }} />
                        AI automation
                      </Typography>
                    </Box>
                    <Button
                      variant="contained"
                      fullWidth
                      size="large"
                      onClick={() => handlePlanSelect('professional')}
                      sx={{
                        background: 'var(--agri-pro-primary)',
                        '&:hover': { background: 'var(--agri-pro-secondary)' },
                        fontWeight: 700,
                        py: 1.5
                      }}
                    >
                      Go Professional
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          </Grid>
        </Container>
      </section>

      {/* Simple Footer */}
      <footer className="landing-footer">
        <Container maxWidth="xl">
          <Grid container spacing={6}>
            <Grid item xs={12} md={4}>
              <div className="footer-brand">
                <Typography variant="h5" className="footer-brand-title">
                  AgriIntel
                </Typography>
                <Typography variant="body1" className="footer-description">
                  Transforming South African agriculture through intelligent livestock management solutions.
                </Typography>
                <div className="footer-contact">
                  <div className="contact-item">
                    <Email fontSize="small" />
                    <Typography variant="body2"><EMAIL></Typography>
                  </div>
                  <div className="contact-item">
                    <Phone fontSize="small" />
                    <Typography variant="body2">+27 79 448 4159</Typography>
                  </div>
                </div>
              </div>
            </Grid>

            <Grid item xs={12} md={4}>
              <div className="footer-section">
                <Typography variant="h6" className="footer-title">
                  Platform
                </Typography>
                <ul className="footer-links">
                  <li><a href="#features">Features</a></li>
                  <li><a href="#pricing">Pricing</a></li>
                  <li><a href="/help">Help Center</a></li>
                  <li><a href="/contact">Contact Us</a></li>
                </ul>
              </div>
            </Grid>

            <Grid item xs={12} md={4}>
              <div className="footer-section">
                <Typography variant="h6" className="footer-title">
                  Connect
                </Typography>
                <div className="footer-cta">
                  <Button
                    variant="contained"
                    size="small"
                    className="footer-cta-button"
                    onClick={() => handlePlanSelect('beta')}
                    startIcon={<PlayArrow />}
                    type="button"
                    aria-label="Start your free trial from footer"
                  >
                    Start Free Trial
                  </Button>
                </div>
              </div>
            </Grid>
          </Grid>

          <Divider className="footer-divider" />

          <div className="footer-bottom">
            <Typography variant="body2" className="footer-copyright">
              © 2024 AgriIntel. All rights reserved. Proudly South African.
            </Typography>
            <div className="footer-badges">
              <Chip
                label="ISO 27001 Certified"
                size="small"
                className="footer-badge"
              />
              <Chip
                label="GDPR Compliant"
                size="small"
                className="footer-badge"
              />
            </div>
          </div>
        </Container>
      </footer>
    </main>
  );
};

export default AgriIntelLanding;
