/**
 * Layout Utilities - AgriIntel
 * Layout and positioning utility classes
 */

/* Display Utilities */
.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-grid { display: grid !important; }
.d-inline-grid { display: inline-grid !important; }

/* Flexbox Utilities */
.flex-row { flex-direction: row !important; }
.flex-row-reverse { flex-direction: row-reverse !important; }
.flex-column { flex-direction: column !important; }
.flex-column-reverse { flex-direction: column-reverse !important; }

.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.flex-wrap-reverse { flex-wrap: wrap-reverse !important; }

.justify-start { justify-content: flex-start !important; }
.justify-end { justify-content: flex-end !important; }
.justify-center { justify-content: center !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }
.justify-evenly { justify-content: space-evenly !important; }

.align-start { align-items: flex-start !important; }
.align-end { align-items: flex-end !important; }
.align-center { align-items: center !important; }
.align-baseline { align-items: baseline !important; }
.align-stretch { align-items: stretch !important; }

.align-content-start { align-content: flex-start !important; }
.align-content-end { align-content: flex-end !important; }
.align-content-center { align-content: center !important; }
.align-content-between { align-content: space-between !important; }
.align-content-around { align-content: space-around !important; }
.align-content-stretch { align-content: stretch !important; }

.align-self-auto { align-self: auto !important; }
.align-self-start { align-self: flex-start !important; }
.align-self-end { align-self: flex-end !important; }
.align-self-center { align-self: center !important; }
.align-self-baseline { align-self: baseline !important; }
.align-self-stretch { align-self: stretch !important; }

/* Flex Grow/Shrink */
.flex-grow-0 { flex-grow: 0 !important; }
.flex-grow-1 { flex-grow: 1 !important; }
.flex-shrink-0 { flex-shrink: 0 !important; }
.flex-shrink-1 { flex-shrink: 1 !important; }

/* Grid Utilities */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)) !important; }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)) !important; }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)) !important; }

.grid-rows-1 { grid-template-rows: repeat(1, minmax(0, 1fr)) !important; }
.grid-rows-2 { grid-template-rows: repeat(2, minmax(0, 1fr)) !important; }
.grid-rows-3 { grid-template-rows: repeat(3, minmax(0, 1fr)) !important; }
.grid-rows-4 { grid-template-rows: repeat(4, minmax(0, 1fr)) !important; }
.grid-rows-5 { grid-template-rows: repeat(5, minmax(0, 1fr)) !important; }
.grid-rows-6 { grid-template-rows: repeat(6, minmax(0, 1fr)) !important; }

.col-span-1 { grid-column: span 1 / span 1 !important; }
.col-span-2 { grid-column: span 2 / span 2 !important; }
.col-span-3 { grid-column: span 3 / span 3 !important; }
.col-span-4 { grid-column: span 4 / span 4 !important; }
.col-span-5 { grid-column: span 5 / span 5 !important; }
.col-span-6 { grid-column: span 6 / span 6 !important; }
.col-span-full { grid-column: 1 / -1 !important; }

.row-span-1 { grid-row: span 1 / span 1 !important; }
.row-span-2 { grid-row: span 2 / span 2 !important; }
.row-span-3 { grid-row: span 3 / span 3 !important; }
.row-span-4 { grid-row: span 4 / span 4 !important; }
.row-span-5 { grid-row: span 5 / span 5 !important; }
.row-span-6 { grid-row: span 6 / span 6 !important; }
.row-span-full { grid-row: 1 / -1 !important; }

/* Position Utilities */
.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

/* Top/Right/Bottom/Left */
.top-0 { top: 0 !important; }
.right-0 { right: 0 !important; }
.bottom-0 { bottom: 0 !important; }
.left-0 { left: 0 !important; }

.top-auto { top: auto !important; }
.right-auto { right: auto !important; }
.bottom-auto { bottom: auto !important; }
.left-auto { left: auto !important; }

/* Z-Index */
.z-0 { z-index: 0 !important; }
.z-10 { z-index: 10 !important; }
.z-20 { z-index: 20 !important; }
.z-30 { z-index: 30 !important; }
.z-40 { z-index: 40 !important; }
.z-50 { z-index: 50 !important; }
.z-auto { z-index: auto !important; }

/* Float */
.float-left { float: left !important; }
.float-right { float: right !important; }
.float-none { float: none !important; }

/* Clear */
.clear-left { clear: left !important; }
.clear-right { clear: right !important; }
.clear-both { clear: both !important; }
.clear-none { clear: none !important; }

/* Overflow */
.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-visible { overflow: visible !important; }
.overflow-scroll { overflow: scroll !important; }

.overflow-x-auto { overflow-x: auto !important; }
.overflow-x-hidden { overflow-x: hidden !important; }
.overflow-x-visible { overflow-x: visible !important; }
.overflow-x-scroll { overflow-x: scroll !important; }

.overflow-y-auto { overflow-y: auto !important; }
.overflow-y-hidden { overflow-y: hidden !important; }
.overflow-y-visible { overflow-y: visible !important; }
.overflow-y-scroll { overflow-y: scroll !important; }

/* Width */
.w-auto { width: auto !important; }
.w-full { width: 100% !important; }
.w-screen { width: 100vw !important; }
.w-min { width: min-content !important; }
.w-max { width: max-content !important; }
.w-fit {
  width: -webkit-fill-available !important;
  width: fit-content !important;
}

.w-1\/2 { width: 50% !important; }
.w-1\/3 { width: 33.333333% !important; }
.w-2\/3 { width: 66.666667% !important; }
.w-1\/4 { width: 25% !important; }
.w-3\/4 { width: 75% !important; }
.w-1\/5 { width: 20% !important; }
.w-2\/5 { width: 40% !important; }
.w-3\/5 { width: 60% !important; }
.w-4\/5 { width: 80% !important; }

/* Height */
.h-auto { height: auto !important; }
.h-full { height: 100% !important; }
.h-screen { height: 100vh !important; }
.h-min { height: min-content !important; }
.h-max { height: max-content !important; }
.h-fit {
  height: -webkit-fill-available !important;
  height: fit-content !important;
}

.h-1\/2 { height: 50% !important; }
.h-1\/3 { height: 33.333333% !important; }
.h-2\/3 { height: 66.666667% !important; }
.h-1\/4 { height: 25% !important; }
.h-3\/4 { height: 75% !important; }
.h-1\/5 { height: 20% !important; }
.h-2\/5 { height: 40% !important; }
.h-3\/5 { height: 60% !important; }
.h-4\/5 { height: 80% !important; }

/* Min/Max Width */
.min-w-0 { min-width: 0px !important; }
.min-w-full { min-width: 100% !important; }
.min-w-min { min-width: min-content !important; }
.min-w-max { min-width: max-content !important; }
.min-w-fit {
  min-width: -webkit-fill-available !important;
  min-width: fit-content !important;
}

.max-w-none { max-width: none !important; }
.max-w-xs { max-width: 20rem !important; }
.max-w-sm { max-width: 24rem !important; }
.max-w-md { max-width: 28rem !important; }
.max-w-lg { max-width: 32rem !important; }
.max-w-xl { max-width: 36rem !important; }
.max-w-2xl { max-width: 42rem !important; }
.max-w-3xl { max-width: 48rem !important; }
.max-w-4xl { max-width: 56rem !important; }
.max-w-5xl { max-width: 64rem !important; }
.max-w-6xl { max-width: 72rem !important; }
.max-w-7xl { max-width: 80rem !important; }
.max-w-full { max-width: 100% !important; }
.max-w-min { max-width: min-content !important; }
.max-w-max { max-width: max-content !important; }
.max-w-fit {
  max-width: -webkit-fill-available !important;
  max-width: fit-content !important;
}

/* Min/Max Height */
.min-h-0 { min-height: 0px !important; }
.min-h-full { min-height: 100% !important; }
.min-h-screen { min-height: 100vh !important; }
.min-h-min { min-height: min-content !important; }
.min-h-max { min-height: max-content !important; }
.min-h-fit {
  min-height: -webkit-fill-available !important;
  min-height: fit-content !important;
}

.max-h-none { max-height: none !important; }
.max-h-full { max-height: 100% !important; }
.max-h-screen { max-height: 100vh !important; }
.max-h-min { max-height: min-content !important; }
.max-h-max { max-height: max-content !important; }
.max-h-fit {
  max-height: -webkit-fill-available !important;
  max-height: fit-content !important;
}

/* Text Alignment */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

/* Vertical Alignment */
.align-baseline { vertical-align: baseline !important; }
.align-top { vertical-align: top !important; }
.align-middle { vertical-align: middle !important; }
.align-bottom { vertical-align: bottom !important; }
.align-text-top { vertical-align: text-top !important; }
.align-text-bottom { vertical-align: text-bottom !important; }
