/* Simple Login Styles */

.simple-login {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: Arial, sans-serif;
  color: white;
  text-align: center;
}

.simple-login-container {
  background: rgba(255, 255, 255, 0.1);
  padding: 3rem;
  border-radius: 15px;
  text-align: center;
  max-width: 400px;
  width: 100%;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.simple-login-title {
  margin-bottom: 2rem;
  font-size: 2rem;
  font-weight: bold;
}

.simple-login-success {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 10px;
  margin-bottom: 2rem;
}

.simple-login-success-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.simple-login-success h2 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
}

.simple-login-success p {
  margin: 0;
  opacity: 0.9;
}

.simple-login-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.simple-login-dot {
  width: 10px;
  height: 10px;
  background: #FFD700;
  border-radius: 50%;
  animation: pulse 1s infinite;
}

.simple-login-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.simple-login-dot:nth-child(3) {
  animation-delay: 0.4s;
}

.simple-login-button {
  padding: 1rem 2rem;
  background: #FF9800;
  color: white;
  border: none;
  border-radius: 5px;
  text-decoration: none;
  font-size: 1.1rem;
  font-weight: bold;
  display: inline-block;
  transition: all 0.3s ease;
}

.simple-login-button:hover {
  background: #F57C00;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.4);
}

.simple-login-welcome {
  margin-top: 2rem;
  opacity: 0.8;
}

.simple-login-welcome p {
  margin: 0.5rem 0;
}

@keyframes pulse {
  0%, 100% { 
    opacity: 0.3; 
  }
  50% { 
    opacity: 1; 
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .simple-login-container {
    margin: 1rem;
    padding: 2rem;
  }
  
  .simple-login-title {
    font-size: 1.5rem;
  }
}
