import React from 'react';
import { <PERSON>, Typography, Button, Paper, Alert, Chip } from '@mui/material';
import { motion } from 'framer-motion';
import { Lock, Upgrade, Star, ArrowForward } from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

interface BetaAccessControlProps {
  module: string;
  feature?: string;
  children: React.ReactNode;
  showUpgradePrompt?: boolean;
  customMessage?: string;
}

const BetaAccessControl: React.FC<BetaAccessControlProps> = ({
  module,
  feature,
  children,
  showUpgradePrompt = true,
  customMessage
}) => {
  const { user } = useAuth();
  const navigate = useNavigate();

  // Define BETA allowed modules
  const betaAllowedModules = [
    'dashboard',
    'animals',
    'health',
    'feeding',
    'financial',
    'resources'
  ];

  // Check if user has access to this module
  const hasAccess = () => {
    if (!user) return false;
    
    // Admin users have full access
    if (user.role === 'admin') {
      return true;
    }

    // Professional users (determined by username or subscription) have full access
    if (user.username === 'Pro' || user.subscriptionTier === 'Professional') {
      return true;
    }
    
    // BETA users (determined by username) only have access to specific modules
    if (user.username === 'Demo') {
      return betaAllowedModules.includes(module);
    }
    
    return false;
  };

  const handleUpgrade = () => {
    navigate('/pricing');
  };

  // If user has access, render children
  if (hasAccess()) {
    return <>{children}</>;
  }

  // If no access, show upgrade prompt
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '60vh',
        p: 4
      }}
    >
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Paper
          elevation={8}
          sx={{
            p: 6,
            textAlign: 'center',
            maxWidth: 500,
            background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            borderRadius: 3
          }}
        >
          {/* Lock Icon */}
          <Box
            sx={{
              width: 80,
              height: 80,
              borderRadius: '50%',
              background: 'linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mx: 'auto',
              mb: 3,
              boxShadow: '0 8px 32px rgba(255, 107, 107, 0.3)'
            }}
          >
            <Lock sx={{ fontSize: 40, color: 'white' }} />
          </Box>

          {/* BETA Badge */}
          <Box sx={{ mb: 2 }}>
            <Chip
              icon={<Star />}
              label="BETA USER"
              sx={{
                background: 'linear-gradient(135deg, #FFA726 0%, #FFCC02 100%)',
                color: '#1A1A1A',
                fontWeight: 700,
                fontSize: '0.875rem'
              }}
            />
          </Box>

          {/* Title */}
          <Typography
            variant="h4"
            sx={{
              fontWeight: 700,
              mb: 2,
              background: 'linear-gradient(135deg, #1A1A1A 0%, #4A4A4A 100%)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}
          >
            Premium Feature
          </Typography>

          {/* Message */}
          <Typography
            variant="body1"
            sx={{ mb: 3, color: 'text.secondary', lineHeight: 1.6 }}
          >
            {customMessage || `The ${module} module is available in our Professional plan. Upgrade to unlock advanced features and unlimited access.`}
          </Typography>

          {/* Features List */}
          <Alert
            severity="info"
            sx={{
              mb: 3,
              textAlign: 'left',
              '& .MuiAlert-message': {
                width: '100%'
              }
            }}
          >
            <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
              Your BETA access includes:
            </Typography>
            <Typography variant="body2" component="ul" sx={{ m: 0, pl: 2 }}>
              <li>Animal Management (up to 50 animals)</li>
              <li>Health Management</li>
              <li>Feed Management</li>
              <li>Financial Management</li>
              <li>Government Resources</li>
            </Typography>
          </Alert>

          {/* Upgrade Button */}
          {showUpgradePrompt && (
            <Button
              variant="contained"
              size="large"
              onClick={handleUpgrade}
              endIcon={<ArrowForward />}
              sx={{
                background: 'linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%)',
                color: 'white',
                fontWeight: 600,
                py: 1.5,
                px: 4,
                borderRadius: 2,
                textTransform: 'none',
                fontSize: '1.1rem',
                boxShadow: '0 4px 20px rgba(76, 175, 80, 0.4)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #66BB6A 0%, #81C784 100%)',
                  boxShadow: '0 6px 24px rgba(76, 175, 80, 0.5)',
                  transform: 'translateY(-2px)'
                },
                transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)'
              }}
            >
              Upgrade to Professional
            </Button>
          )}
        </Paper>
      </motion.div>
    </Box>
  );
};

export default BetaAccessControl;
