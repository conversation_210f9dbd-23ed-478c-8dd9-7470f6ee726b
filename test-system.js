const http = require('http');

// Test function to check if a URL is accessible
function testURL(url, name) {
  return new Promise((resolve) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname,
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      console.log(`✅ ${name}: ${url} - Status: ${res.statusCode}`);
      resolve(true);
    });

    req.on('error', (err) => {
      console.log(`❌ ${name}: ${url} - Error: ${err.message}`);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log(`⏰ ${name}: ${url} - Timeout`);
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// Main test function
async function testSystem() {
  console.log('🚀 Testing AgriIntel System...\n');

  const tests = [
    { url: 'http://localhost:3002', name: 'Backend Server' },
    { url: 'http://localhost:3002/api', name: 'Backend API' },
    { url: 'http://localhost:3003', name: 'Frontend Landing Page' },
    { url: 'http://localhost:3003/beta-login', name: 'Beta Login Page' },
    { url: 'http://localhost:3003/login', name: 'Live Login Page' },
    { url: 'http://localhost:3003/enhanced-beta', name: 'Enhanced Beta Page' }
  ];

  let passedTests = 0;
  const totalTests = tests.length;

  for (const test of tests) {
    const result = await testURL(test.url, test.name);
    if (result) passedTests++;
    await new Promise(resolve => setTimeout(resolve, 500)); // Small delay between tests
  }

  console.log('\n📊 Test Results:');
  console.log(`✅ Passed: ${passedTests}/${totalTests}`);
  console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests}`);

  if (passedTests === totalTests) {
    console.log('\n🎉 ALL SYSTEMS ARE WORKING! 🎉');
    console.log('\n📋 Quick Access URLs:');
    console.log('🏠 Landing Page: http://localhost:3003');
    console.log('🧪 Beta Login: http://localhost:3003/beta-login');
    console.log('🔐 Live Login: http://localhost:3003/login');
    console.log('⚙️ Backend API: http://localhost:3002/api');
    console.log('\n🔑 Test Credentials:');
    console.log('   Admin: admin / Admin@123');
    console.log('   Demo: Demo / 123');
    console.log('   May: May Rakgama / MayAdmin@2024');
  } else {
    console.log('\n⚠️ Some systems are not responding properly.');
  }
}

// Run the test
testSystem().catch(console.error);
