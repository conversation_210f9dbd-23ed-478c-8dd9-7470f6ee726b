/**
 * Modern SaaS Landing Page Styles for AgriIntel
 * Professional livestock management platform with glassmorphism effects,
 * authentic South African livestock photography, and modern UI/UX
 */

/* CSS Variables for AgriIntel Brand Colors */
:root {
  /* Primary Colors - Deep Agricultural Blues */
  --agri-primary-dark: #1565C0;
  --agri-primary: #1976D2;
  --agri-primary-light: #42A5F5;

  /* Secondary Colors - Emerald Greens */
  --agri-secondary-dark: #2E7D32;
  --agri-secondary: #388E3C;
  --agri-secondary-light: #66BB6A;

  /* Accent Colors - Warm Golds */
  --agri-accent-dark: #F57C00;
  --agri-accent: #FF8F00;
  --agri-accent-light: #FFB74D;

  /* BETA Tier Colors - Bright Yellow/Orange for Visibility */
  --agri-beta-primary: #FF9800;
  --agri-beta-secondary: #FFB74D;
  --agri-beta-gradient: linear-gradient(135deg, #FF9800 0%, #FFC107 100%);
  --agri-beta-bg: rgba(255, 152, 0, 0.1);
  --agri-beta-border: #FF9800;

  /* Professional Tier Colors - Vibrant Green for Visibility */
  --agri-pro-primary: #4CAF50;
  --agri-pro-secondary: #66BB6A;
  --agri-pro-gradient: linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%);
  --agri-pro-bg: rgba(76, 175, 80, 0.1);
  --agri-pro-border: #4CAF50;
  
  /* Neutral Colors */
  --agri-white: #FFFFFF;
  --agri-light-gray: #F8F9FA;
  --agri-medium-gray: #E9ECEF;
  --agri-dark-gray: #495057;
  --agri-black: #212529;
  
  /* Glassmorphism Effects - More Transparent */
  --glass-bg: rgba(255, 255, 255, 0.08);
  --glass-border: rgba(255, 255, 255, 0.15);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  --glass-backdrop: blur(15px);
  
  /* Typography Scale */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1.125rem; /* 18px - Large readable body text */
  --font-size-lg: 1.25rem;    /* 20px */
  --font-size-xl: 1.5rem;     /* 24px */
  --font-size-2xl: 1.875rem;  /* 30px */
  --font-size-3xl: 2.25rem;   /* 36px */
  --font-size-4xl: 3rem;      /* 48px - Large headings */
  --font-size-5xl: 3.75rem;   /* 60px */
  --font-size-6xl: 4.5rem;    /* 72px */
  
  /* Spacing System */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  --spacing-4xl: 6rem;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
  
  /* Z-Index Scale */
  --z-background: -1;
  --z-base: 0;
  --z-overlay: 10;
  --z-modal: 100;
  --z-navigation: 1000;
}

/* 2029 Agricultural Consciousness Animations */
@keyframes agricultural-consciousness {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    filter: hue-rotate(0deg) brightness(1) saturate(100%);
  }
  25% {
    transform: scale(1.01) rotate(0.2deg);
    filter: hue-rotate(15deg) brightness(1.05) saturate(110%);
  }
  50% {
    transform: scale(1.005) rotate(-0.1deg);
    filter: hue-rotate(30deg) brightness(0.98) saturate(95%);
  }
  75% {
    transform: scale(1.015) rotate(0.15deg);
    filter: hue-rotate(45deg) brightness(1.02) saturate(105%);
  }
}

@keyframes agricultural-drift {
  0% {
    transform: translateX(0) translateY(0) rotate(0deg);
  }
  25% {
    transform: translateX(5px) translateY(-2px) rotate(0.3deg);
  }
  50% {
    transform: translateX(-2px) translateY(5px) rotate(-0.2deg);
  }
  75% {
    transform: translateX(3px) translateY(1px) rotate(0.1deg);
  }
  100% {
    transform: translateX(0) translateY(0) rotate(0deg);
  }
}

/* Dark Mode Variables */
.dark-mode {
  --agri-white: #121212;
  --agri-light-gray: #1E1E1E;
  --agri-medium-gray: #2D2D2D;
  --agri-dark-gray: #E0E0E0;
  --agri-black: #FFFFFF;
  --glass-bg: rgba(0, 0, 0, 0.25);
  --glass-border: rgba(255, 255, 255, 0.15);
}

/* Base Styles */
.modern-saas-landing {
  min-height: 100vh;
  font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
  color: var(--agri-white);
  background: transparent;
  overflow-x: hidden;
  position: relative;
}

/* Professional Agricultural Background with Clear Visibility */
.landing-background-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: var(--z-background);
  overflow: hidden;
  background: linear-gradient(135deg,
    rgba(21, 101, 192, 0.9) 0%,
    rgba(46, 125, 50, 0.9) 50%,
    rgba(245, 124, 0, 0.9) 100%
  );
}

/* Remove blurry overlay - keep clean background */

/* Clean professional pattern overlay */
.landing-background-container::after {
  content: '';
  position: absolute;
  inset: 0;
  background-image:
    linear-gradient(45deg, rgba(255, 255, 255, 0.03) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(255, 255, 255, 0.03) 25%, transparent 25%);
  background-size: 60px 60px;
  pointer-events: none;
  z-index: 2;
}

.landing-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  z-index: 3;
  opacity: 0.2;
}

.landing-background::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg,
    rgba(21, 101, 192, 0.1) 0%,
    rgba(46, 125, 50, 0.1) 50%,
    rgba(245, 124, 0, 0.1) 100%
  );
  z-index: 4;
}

/* Remove heavy overlay - content should be clearly visible */

/* Navigation */
.landing-navigation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-navigation);
  background: transparent;
  border-bottom: none;
  box-shadow: none;
  padding: var(--spacing-sm) 0;
  height: 70px;
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  position: relative;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}

.nav-center {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  gap: var(--spacing-lg);
}

.nav-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.nav-title {
  color: var(--agri-white) !important;
  font-weight: 700 !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  background: linear-gradient(135deg, #FFFFFF 0%, #E3F2FD 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.8));
}

.nav-tagline {
  color: var(--agri-white) !important;
  font-weight: 400 !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  opacity: 0.9;
  font-style: italic;
}

.agriintel-logo-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: rgba(46, 125, 50, 0.1);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-lg);
  border: 2px solid rgba(46, 125, 50, 0.3);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal);
}

.agriintel-logo-container:hover {
  background: rgba(46, 125, 50, 0.2);
  border-color: rgba(245, 124, 0, 0.5);
  transform: scale(1.02);
}

.logo-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.agriintel-logo-image {
  height: 45px;
  width: auto;
  object-fit: contain;
  filter: drop-shadow(0 2px 8px rgba(46, 125, 50, 0.3));
  transition: all var(--transition-normal);
}

.agriintel-logo-image:hover {
  filter: drop-shadow(0 4px 12px rgba(245, 124, 0, 0.4));
}

.logo-tagline {
  font-size: var(--font-size-xs) !important;
  color: var(--agri-white) !important;
  opacity: 0.9;
  margin-top: 2px !important;
}

.nav-badge {
  background: linear-gradient(135deg, #F57C00 0%, #FF9800 100%) !important;
  color: var(--agri-white) !important;
  font-weight: 600 !important;
  font-size: var(--font-size-xs) !important;
  border: 2px solid rgba(245, 124, 0, 0.3) !important;
  box-shadow: 0 4px 12px rgba(245, 124, 0, 0.3) !important;
  transition: all var(--transition-normal) !important;
}

.nav-badge:hover {
  transform: scale(1.05) !important;
  box-shadow: 0 6px 16px rgba(245, 124, 0, 0.5) !important;
}

.nav-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.language-selector .MuiSelect-root {
  color: var(--agri-white) !important;
  border-color: var(--glass-border) !important;
}

.theme-toggle {
  color: var(--agri-white) !important;
  background: var(--glass-bg) !important;
  border: 1px solid var(--glass-border) !important;
  transition: var(--transition-normal) !important;
}

.theme-toggle:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  transform: scale(1.05);
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* Professional Agricultural SaaS Button System */
.nav-button {
  font-weight: 600 !important;
  padding: var(--spacing-md) var(--spacing-xl) !important;
  border-radius: var(--radius-lg) !important;
  text-transform: none !important;
  font-size: var(--font-size-base) !important;
  transition: all var(--transition-normal) !important;
  position: relative;
  overflow: hidden;
  border: 2px solid transparent !important;
}

.nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.nav-button:hover::before {
  left: 100%;
}

.nav-button-outline {
  color: var(--agri-white) !important;
  border-color: rgba(46, 125, 50, 0.5) !important;
  background: rgba(46, 125, 50, 0.1) !important;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(46, 125, 50, 0.2) !important;
}

.nav-button-outline:hover {
  background: rgba(46, 125, 50, 0.2) !important;
  border-color: rgba(245, 124, 0, 0.7) !important;
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 25px rgba(245, 124, 0, 0.3) !important;
}

.nav-button-primary {
  background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 50%, #66BB6A 100%) !important;
  color: var(--agri-white) !important;
  border-color: rgba(46, 125, 50, 0.3) !important;
  box-shadow:
    0 4px 15px rgba(46, 125, 50, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.nav-button-primary:hover {
  background: linear-gradient(135deg, #1B5E20 0%, #2E7D32 50%, #4CAF50 100%) !important;
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 8px 25px rgba(46, 125, 50, 0.5),
    0 4px 15px rgba(245, 124, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

/* Main Content Sections */
.landing-section {
  position: relative;
  z-index: var(--z-base);
  padding: var(--spacing-4xl) 0;
}

.landing-section:first-of-type {
  padding-top: calc(var(--spacing-4xl) + 80px); /* Account for fixed nav */
}

/* Hero Section */
.hero-section {
  text-align: center;
  color: var(--agri-white);
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  z-index: var(--z-base);
  padding-top: 80px;
}

/* Pricing Cards with Clear Tier Colors */
.pricing-card-beta {
  background: transparent !important;
  border: 2px solid var(--agri-beta-border) !important;
  box-shadow: none !important;
  color: var(--agri-white) !important;
}

.pricing-card-beta .plan-badge {
  background: var(--agri-beta-gradient) !important;
  color: white !important;
  font-weight: bold !important;
}

.pricing-card-beta .MuiButton-contained {
  background: var(--agri-beta-gradient) !important;
  color: white !important;
  border: none !important;
}

.pricing-card-pro {
  background: transparent !important;
  border: 2px solid var(--agri-pro-border) !important;
  box-shadow: none !important;
  color: var(--agri-white) !important;
}

.pricing-card-pro .plan-badge {
  background: var(--agri-pro-gradient) !important;
  color: white !important;
  font-weight: bold !important;
}

.pricing-card-pro .MuiButton-contained {
  background: var(--agri-pro-gradient) !important;
  color: white !important;
  border: none !important;
}

/* Content Cards with Better Visibility */
.content-card {
  background: transparent !important;
  border: none !important;
  border-radius: var(--radius-lg) !important;
  box-shadow: none !important;
  color: var(--agri-white) !important;
}

/* Ensure all text in landing page is visible */
.modern-saas-landing {
  color: var(--agri-white);
}

.modern-saas-landing .MuiTypography-root {
  color: inherit;
}

.modern-saas-landing .content-card .MuiTypography-root {
  color: var(--agri-black) !important;
}

.modern-saas-landing .pricing-card-beta .MuiTypography-root,
.modern-saas-landing .pricing-card-pro .MuiTypography-root {
  color: var(--agri-black) !important;
}

/* Navigation text */
.landing-navigation .MuiTypography-root,
.landing-navigation .MuiButton-root {
  color: var(--agri-black) !important;
}

/* Section backgrounds for better content visibility */
.landing-section {
  position: relative;
  z-index: 5;
  padding: var(--spacing-2xl) 0;
}

.landing-section.hero-section {
  padding-top: 90px; /* Account for fixed navigation */
  min-height: calc(100vh - 70px);
}

.landing-section:not(.hero-section) {
  background: transparent;
  margin: var(--spacing-xl) 0;
  border-radius: var(--radius-xl);
}

/* Footer styling */
.landing-footer {
  background: rgba(0, 0, 0, 0.8) !important;
  color: var(--agri-white) !important;
  position: relative;
  z-index: 5;
}

.landing-footer .MuiTypography-root {
  color: var(--agri-white) !important;
}

/* Professional Agricultural SaaS Typography System */
.hero-title {
  font-size: var(--font-size-6xl) !important;
  font-weight: 800 !important;
  line-height: 1.1 !important;
  margin-bottom: var(--spacing-lg) !important;
  color: var(--agri-white) !important;
  text-shadow:
    2px 2px 4px rgba(0, 0, 0, 0.9),
    0 0 20px rgba(46, 125, 50, 0.4),
    0 0 40px rgba(245, 124, 0, 0.2);
  background: linear-gradient(135deg, #FFFFFF 0%, #E8F5E8 50%, #C8E6C9 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.8));
}

.hero-subtitle {
  font-size: var(--font-size-xl) !important;
  font-weight: 400 !important;
  line-height: 1.6 !important;
  margin-bottom: var(--spacing-2xl) !important;
  color: var(--agri-white) !important;
  text-shadow:
    1px 1px 3px rgba(0, 0, 0, 0.9),
    0 0 15px rgba(21, 101, 192, 0.3);
  opacity: 0.95;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

/* Glassmorphism Cards */
.glass-card {
  background: transparent !important;
  border: none !important;
  border-radius: var(--radius-xl) !important;
  box-shadow: none !important;
  transition: var(--transition-normal) !important;
}

.glass-card:hover {
  transform: translateY(-8px);
}

/* About Section Styles */
.about-section {
  background: transparent;
  border-radius: var(--radius-xl);
  margin: var(--spacing-xl) 0;
  padding: var(--spacing-2xl) 0;
}

.about-title {
  font-size: var(--font-size-4xl) !important;
  font-weight: 800 !important;
  color: var(--agri-white) !important;
  margin-bottom: var(--spacing-md) !important;
  text-shadow:
    2px 2px 4px rgba(0, 0, 0, 0.8),
    0 0 20px rgba(21, 101, 192, 0.4);
  background: linear-gradient(135deg, #FFFFFF 0%, #E3F2FD 50%, #BBDEFB 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.8));
}

.about-subtitle {
  font-size: var(--font-size-xl) !important;
  color: var(--agri-white) !important;
  margin-bottom: var(--spacing-lg) !important;
  text-shadow:
    1px 1px 2px rgba(0, 0, 0, 0.8),
    0 0 15px rgba(46, 125, 50, 0.3);
  opacity: 0.9;
  background: linear-gradient(135deg, #FFFFFF 0%, #E8F5E8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.8));
}

.about-description {
  font-size: var(--font-size-lg) !important;
  color: var(--agri-white) !important;
  margin-bottom: var(--spacing-lg) !important;
  line-height: 1.7 !important;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9);
  opacity: 0.95;
}

.about-features {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.about-feature {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: transparent;
  border-radius: var(--radius-lg);
}

.about-feature-icon {
  color: var(--agri-accent) !important;
  font-size: 2rem !important;
}

.about-image-container {
  position: relative;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.about-image {
  width: 100%;
  height: 400px;
  object-fit: cover;
}

.about-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: var(--spacing-xl);
  color: white;
}

.overlay-content h4 {
  font-size: var(--font-size-3xl) !important;
  font-weight: 800 !important;
  margin-bottom: var(--spacing-xs) !important;
}

/* Enhanced Sponsors Section with Carousel */
.sponsors-section-inline {
  text-align: center;
  padding: var(--spacing-3xl) 0;
  position: relative;
  overflow: hidden;
}

.sponsors-title {
  font-size: var(--font-size-2xl) !important;
  font-weight: 700 !important;
  color: var(--agri-white) !important;
  margin-bottom: var(--spacing-xl) !important;
  text-shadow:
    2px 2px 4px rgba(0, 0, 0, 0.8),
    0 0 15px rgba(245, 124, 0, 0.3);
  background: linear-gradient(135deg, #FFFFFF 0%, #FFF3E0 50%, #FFE0B2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.8));
}

.sponsors-carousel-container {
  position: relative;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  overflow: hidden;
  border-radius: var(--radius-xl);
  background: rgba(46, 125, 50, 0.05);
  padding: var(--spacing-xl);
  border: 2px solid rgba(46, 125, 50, 0.2);
}

.sponsors-carousel {
  display: flex;
  animation: sponsorSlide 20s infinite linear;
  gap: var(--spacing-xl);
}

.sponsors-carousel:hover {
  animation-play-state: paused;
}

@keyframes sponsorSlide {
  0% { transform: translateX(0); }
  100% { transform: translateX(-50%); }
}

.sponsor-item {
  background: rgba(255, 255, 255, 0.1);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
  min-width: 200px;
  max-width: 250px;
  border: 2px solid rgba(245, 124, 0, 0.2);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.sponsor-item:hover {
  transform: translateY(-8px) scale(1.05);
  background: rgba(245, 124, 0, 0.1);
  border-color: rgba(245, 124, 0, 0.5);
  box-shadow: 0 10px 30px rgba(245, 124, 0, 0.3);
}

.sponsor-logo {
  width: 100%;
  height: 80px;
  object-fit: contain;
  filter:
    brightness(1.3)
    contrast(1.2)
    drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
  transition: all var(--transition-normal);
}

.sponsor-item:hover .sponsor-logo {
  filter:
    brightness(1.5)
    contrast(1.3)
    drop-shadow(0 6px 12px rgba(245, 124, 0, 0.4));
}

/* Material-UI Component Overrides - Remove All White Backgrounds */
.modern-saas-landing .MuiPaper-root {
  background: transparent !important;
  box-shadow: none !important;
}

.modern-saas-landing .MuiCard-root {
  background: transparent !important;
  box-shadow: none !important;
}

.modern-saas-landing .MuiContainer-root {
  background: transparent !important;
}

.modern-saas-landing .MuiBox-root {
  background: transparent !important;
}

.modern-saas-landing .MuiGrid-root {
  background: transparent !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: var(--font-size-4xl) !important;
  }

  .hero-subtitle {
    font-size: var(--font-size-lg) !important;
  }

  .nav-container {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .about-features {
    gap: var(--spacing-md);
  }

  .about-image {
    height: 300px;
  }

  .sponsors-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
  }

  .nav-actions {
    width: 100%;
    justify-content: center;
  }
  
  .landing-section {
    padding: var(--spacing-2xl) 0;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus States */
.nav-button:focus,
.theme-toggle:focus {
  outline: 2px solid var(--agri-accent) !important;
  outline-offset: 2px !important;
}
