/**
 * Modern SaaS Landing Page Styles for AgriIntel
 * Professional livestock management platform with glassmorphism effects,
 * authentic South African livestock photography, and modern UI/UX
 */

/* CSS Variables for AgriIntel Brand Colors */
:root {
  /* Primary Colors - Deep Agricultural Blues */
  --agri-primary-dark: #1565C0;
  --agri-primary: #1976D2;
  --agri-primary-light: #42A5F5;

  /* Secondary Colors - Emerald Greens */
  --agri-secondary-dark: #2E7D32;
  --agri-secondary: #388E3C;
  --agri-secondary-light: #66BB6A;

  /* Accent Colors - Warm Golds */
  --agri-accent-dark: #F57C00;
  --agri-accent: #FF8F00;
  --agri-accent-light: #FFB74D;

  /* BETA Tier Colors - Yellow Accents */
  --agri-beta-primary: #F57C00;
  --agri-beta-secondary: #FFB74D;
  --agri-beta-gradient: linear-gradient(135deg, #F57C00 0%, #FFB74D 100%);

  /* Professional Tier Colors - Green Accents */
  --agri-pro-primary: #2E7D32;
  --agri-pro-secondary: #66BB6A;
  --agri-pro-gradient: linear-gradient(135deg, #2E7D32 0%, #66BB6A 100%);
  
  /* Neutral Colors */
  --agri-white: #FFFFFF;
  --agri-light-gray: #F8F9FA;
  --agri-medium-gray: #E9ECEF;
  --agri-dark-gray: #495057;
  --agri-black: #212529;
  
  /* Glassmorphism Effects */
  --glass-bg: rgba(255, 255, 255, 0.15);
  --glass-border: rgba(255, 255, 255, 0.25);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  --glass-backdrop: blur(20px);
  
  /* Typography Scale */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1.125rem; /* 18px - Large readable body text */
  --font-size-lg: 1.25rem;    /* 20px */
  --font-size-xl: 1.5rem;     /* 24px */
  --font-size-2xl: 1.875rem;  /* 30px */
  --font-size-3xl: 2.25rem;   /* 36px */
  --font-size-4xl: 3rem;      /* 48px - Large headings */
  --font-size-5xl: 3.75rem;   /* 60px */
  --font-size-6xl: 4.5rem;    /* 72px */
  
  /* Spacing System */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  --spacing-4xl: 6rem;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
  
  /* Z-Index Scale */
  --z-background: -1;
  --z-base: 0;
  --z-overlay: 10;
  --z-modal: 100;
  --z-navigation: 1000;
}

/* 2029 Agricultural Consciousness Animations */
@keyframes agricultural-consciousness {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    filter: hue-rotate(0deg) brightness(1) saturate(100%);
  }
  25% {
    transform: scale(1.01) rotate(0.2deg);
    filter: hue-rotate(15deg) brightness(1.05) saturate(110%);
  }
  50% {
    transform: scale(1.005) rotate(-0.1deg);
    filter: hue-rotate(30deg) brightness(0.98) saturate(95%);
  }
  75% {
    transform: scale(1.015) rotate(0.15deg);
    filter: hue-rotate(45deg) brightness(1.02) saturate(105%);
  }
}

@keyframes agricultural-drift {
  0% {
    transform: translateX(0) translateY(0) rotate(0deg);
  }
  25% {
    transform: translateX(5px) translateY(-2px) rotate(0.3deg);
  }
  50% {
    transform: translateX(-2px) translateY(5px) rotate(-0.2deg);
  }
  75% {
    transform: translateX(3px) translateY(1px) rotate(0.1deg);
  }
  100% {
    transform: translateX(0) translateY(0) rotate(0deg);
  }
}

/* Dark Mode Variables */
.dark-mode {
  --agri-white: #121212;
  --agri-light-gray: #1E1E1E;
  --agri-medium-gray: #2D2D2D;
  --agri-dark-gray: #E0E0E0;
  --agri-black: #FFFFFF;
  --glass-bg: rgba(0, 0, 0, 0.25);
  --glass-border: rgba(255, 255, 255, 0.15);
}

/* Base Styles */
.modern-saas-landing {
  min-height: 100vh;
  font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
  color: var(--agri-black);
  background: var(--agri-light-gray);
  overflow-x: hidden;
  position: relative;
}

/* 2029 Quantum Consciousness Background with Livestock Photography */
.landing-background-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: var(--z-background);
  overflow: hidden;
  background: var(--quantum-abyss);
}

.landing-background-container::before {
  content: '';
  position: absolute;
  inset: 0;
  background:
    radial-gradient(ellipse at 20% 80%, rgba(144, 238, 144, 0.08) 0%, transparent 50%),
    radial-gradient(ellipse at 80% 20%, rgba(255, 255, 224, 0.06) 0%, transparent 50%),
    radial-gradient(ellipse at 50% 50%, rgba(152, 251, 152, 0.04) 0%, transparent 40%),
    radial-gradient(ellipse at 30% 30%, rgba(255, 250, 205, 0.05) 0%, transparent 45%);
  pointer-events: none;
  z-index: 1;
  animation: agricultural-consciousness 30s ease-in-out infinite;
}

.landing-background-container::after {
  content: '';
  position: absolute;
  inset: 0;
  background-image:
    radial-gradient(circle at 25px 25px, rgba(144, 238, 144, 0.02) 1px, transparent 1px),
    radial-gradient(circle at 75px 75px, rgba(255, 255, 224, 0.015) 1px, transparent 1px),
    linear-gradient(45deg, transparent 48%, rgba(152, 251, 152, 0.005) 49%, rgba(152, 251, 152, 0.005) 51%, transparent 52%);
  background-size: 50px 50px, 100px 100px, 80px 80px;
  pointer-events: none;
  z-index: 2;
  animation: agricultural-drift 40s linear infinite;
}

.landing-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  z-index: 3;
}

.landing-background::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg,
    rgba(0, 0, 0, 0.6) 0%,
    rgba(144, 238, 144, 0.03) 25%,
    rgba(255, 255, 224, 0.02) 50%,
    rgba(152, 251, 152, 0.025) 75%,
    rgba(0, 0, 0, 0.5) 100%
  );
  -webkit-backdrop-filter: blur(0.5px) saturate(110%);
  backdrop-filter: blur(0.5px) saturate(110%);
  z-index: 4;
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(21, 101, 192, 0.85) 0%,
    rgba(46, 125, 50, 0.75) 50%,
    rgba(245, 124, 0, 0.65) 100%
  );
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
}

/* Navigation */
.landing-navigation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-navigation);
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border-bottom: 1px solid var(--glass-border);
  padding: var(--spacing-md) 0;
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.agriintel-logo-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.logo-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.agriintel-logo-image {
  height: 48px;
  width: auto;
  object-fit: contain;
}

.logo-tagline {
  font-size: var(--font-size-xs) !important;
  color: var(--agri-white) !important;
  opacity: 0.9;
  margin-top: 2px !important;
}

.nav-badge {
  background: var(--agri-pro-gradient) !important;
  color: var(--agri-white) !important;
  font-weight: 600 !important;
  font-size: var(--font-size-xs) !important;
}

.nav-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.language-selector .MuiSelect-root {
  color: var(--agri-white) !important;
  border-color: var(--glass-border) !important;
}

.theme-toggle {
  color: var(--agri-white) !important;
  background: var(--glass-bg) !important;
  border: 1px solid var(--glass-border) !important;
  transition: var(--transition-normal) !important;
}

.theme-toggle:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  transform: scale(1.05);
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.nav-button {
  font-weight: 600 !important;
  padding: var(--spacing-sm) var(--spacing-lg) !important;
  border-radius: var(--radius-lg) !important;
  text-transform: none !important;
  font-size: var(--font-size-base) !important;
  transition: var(--transition-normal) !important;
}

.nav-button-outline {
  color: var(--agri-white) !important;
  border-color: var(--glass-border) !important;
  background: var(--glass-bg) !important;
}

.nav-button-outline:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: var(--agri-white) !important;
  transform: translateY(-2px);
}

.nav-button-primary {
  background: var(--agri-pro-gradient) !important;
  color: var(--agri-white) !important;
  border: none !important;
  box-shadow: 0 4px 15px rgba(46, 125, 50, 0.3) !important;
}

.nav-button-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(46, 125, 50, 0.4) !important;
}

/* Main Content Sections */
.landing-section {
  position: relative;
  z-index: var(--z-base);
  padding: var(--spacing-4xl) 0;
}

.landing-section:first-of-type {
  padding-top: calc(var(--spacing-4xl) + 80px); /* Account for fixed nav */
}

/* Hero Section */
.hero-section {
  text-align: center;
  color: var(--agri-white);
}

.hero-title {
  font-size: var(--font-size-6xl) !important;
  font-weight: 800 !important;
  line-height: 1.1 !important;
  margin-bottom: var(--spacing-lg) !important;
  background: linear-gradient(135deg, var(--agri-white) 0%, var(--agri-accent-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: var(--font-size-xl) !important;
  font-weight: 400 !important;
  line-height: 1.6 !important;
  margin-bottom: var(--spacing-2xl) !important;
  opacity: 0.95;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Glassmorphism Cards */
.glass-card {
  background: var(--glass-bg) !important;
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border) !important;
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--glass-shadow) !important;
  transition: var(--transition-normal) !important;
}

.glass-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: var(--font-size-4xl) !important;
  }
  
  .hero-subtitle {
    font-size: var(--font-size-lg) !important;
  }
  
  .nav-container {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .nav-actions {
    width: 100%;
    justify-content: center;
  }
  
  .landing-section {
    padding: var(--spacing-2xl) 0;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus States */
.nav-button:focus,
.theme-toggle:focus {
  outline: 2px solid var(--agri-accent) !important;
  outline-offset: 2px !important;
}
