import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Alert,
  <PERSON>,
  <PERSON>ack,
  Divider,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  useTheme,
  alpha
} from '@mui/material';
import {
  Security,
  CheckCircle,
  Error,
  Warning,
  Info,
  Lock,
  LockOpen,
  Person,
  AdminPanelSettings
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import {
  moduleConfigurations,
  featureLimits,
  getUserAccessLevel,
  canAccessModule,
  getAvailableModules,
  getLockedModules,
  getFeatureLimit,
  getUpgradePath,
  validateRouteAccess
} from '../../utils/enhancedAccessControl';

interface TestResult {
  test: string;
  status: 'pass' | 'fail' | 'warning' | 'info';
  message: string;
  timestamp: Date;
}

const AccessControlTest: React.FC = () => {
  const theme = useTheme();
  const { user } = useAuth();
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);

  const addTestResult = (test: string, status: TestResult['status'], message: string) => {
    setTestResults(prev => [...prev, {
      test,
      status,
      message,
      timestamp: new Date()
    }]);
  };

  const runAccessControlTests = async () => {
    setIsRunningTests(true);
    setTestResults([]);

    // Test 1: User Authentication
    if (user) {
      addTestResult('User Authentication', 'pass', `User logged in: ${user.username} (${user.role})`);
    } else {
      addTestResult('User Authentication', 'fail', 'No user logged in');
    }

    // Test 2: Access Level Detection
    const accessLevel = getUserAccessLevel(user);
    addTestResult('Access Level', 'info', `Current access level: ${accessLevel}`);

    // Test 3: Module Access Tests
    const testRoutes = [
      '/beta-dashboard',
      '/beta-dashboard/animals',
      '/beta-dashboard/health',
      '/beta-dashboard/breeding',
      '/beta-dashboard/financial',
      '/dashboard',
      '/dashboard/animals',
      '/dashboard/breeding',
      '/dashboard/analytics'
    ];

    testRoutes.forEach(route => {
      const validation = validateRouteAccess(user, route);
      addTestResult(
        `Route Access: ${route}`,
        validation.allowed ? 'pass' : 'warning',
        validation.allowed ? 'Access granted' : validation.reason || 'Access denied'
      );
    });

    // Test 4: Feature Limits
    Object.keys(featureLimits).forEach(featureName => {
      const limit = getFeatureLimit(user, featureName);
      addTestResult(
        `Feature Limit: ${featureName}`,
        'info',
        `Limit: ${limit === -1 ? 'Unlimited' : limit}`
      );
    });

    // Test 5: Module Availability
    const availableModules = getAvailableModules(user);
    const lockedModules = getLockedModules(user);
    
    addTestResult(
      'Available Modules',
      'pass',
      `${availableModules.length} modules available: ${availableModules.map(m => m.name).join(', ')}`
    );
    
    addTestResult(
      'Locked Modules',
      'warning',
      `${lockedModules.length} modules locked: ${lockedModules.map(m => m.name).join(', ')}`
    );

    // Test 6: Upgrade Path
    const upgradePath = getUpgradePath(user);
    addTestResult(
      'Upgrade Path',
      'info',
      upgradePath.next ? `Can upgrade to ${upgradePath.next} for ${upgradePath.price}` : 'No upgrade available'
    );

    setIsRunningTests(false);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return <CheckCircle sx={{ color: theme.palette.success.main }} />;
      case 'fail':
        return <Error sx={{ color: theme.palette.error.main }} />;
      case 'warning':
        return <Warning sx={{ color: theme.palette.warning.main }} />;
      case 'info':
        return <Info sx={{ color: theme.palette.info.main }} />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return theme.palette.success.main;
      case 'fail':
        return theme.palette.error.main;
      case 'warning':
        return theme.palette.warning.main;
      case 'info':
        return theme.palette.info.main;
    }
  };

  const availableModules = getAvailableModules(user);
  const lockedModules = getLockedModules(user);
  const accessLevel = getUserAccessLevel(user);

  return (
    <Box sx={{ p: 3 }}>
      <Card
        sx={{
          background: alpha(theme.palette.background.paper, 0.9),
          backdropFilter: 'blur(20px)',
          border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
        }}
      >
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
            <Security sx={{ fontSize: 40, color: theme.palette.primary.main }} />
            <Box>
              <Typography variant="h4" fontWeight="bold">
                Access Control Testing
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                Test user permissions, module access, and feature limits
              </Typography>
            </Box>
          </Box>

          {/* User Info */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                    <Person sx={{ color: theme.palette.primary.main }} />
                    <Typography variant="h6">Current User</Typography>
                  </Box>
                  {user ? (
                    <>
                      <Typography><strong>Username:</strong> {user.username}</Typography>
                      <Typography><strong>Role:</strong> {user.role}</Typography>
                      <Typography><strong>Access Level:</strong> {accessLevel}</Typography>
                      <Chip
                        label={accessLevel.toUpperCase()}
                        color={accessLevel === 'admin' ? 'error' : accessLevel === 'enterprise' ? 'secondary' : 'primary'}
                        sx={{ mt: 1 }}
                      />
                    </>
                  ) : (
                    <Typography color="text.secondary">No user logged in</Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                    <AdminPanelSettings sx={{ color: theme.palette.primary.main }} />
                    <Typography variant="h6">Access Summary</Typography>
                  </Box>
                  <Typography><strong>Available Modules:</strong> {availableModules.length}</Typography>
                  <Typography><strong>Locked Modules:</strong> {lockedModules.length}</Typography>
                  <Typography><strong>Total Modules:</strong> {Object.keys(moduleConfigurations).length}</Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Module Access Table */}
          <Typography variant="h6" gutterBottom>
            Module Access Matrix
          </Typography>
          <TableContainer component={Paper} sx={{ mb: 3 }}>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Module</TableCell>
                  <TableCell>Category</TableCell>
                  <TableCell>Required Level</TableCell>
                  <TableCell>Access Status</TableCell>
                  <TableCell>Beta Access</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {Object.values(moduleConfigurations).map((module) => {
                  const hasAccess = canAccessModule(user, module.id);
                  return (
                    <TableRow key={module.id}>
                      <TableCell>{module.name}</TableCell>
                      <TableCell>
                        <Chip
                          label={module.category}
                          size="small"
                          color={module.category === 'core' ? 'success' : module.category === 'premium' ? 'warning' : 'error'}
                        />
                      </TableCell>
                      <TableCell>{module.requiredLevel}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {hasAccess ? <LockOpen color="success" /> : <Lock color="error" />}
                          <Typography color={hasAccess ? 'success.main' : 'error.main'}>
                            {hasAccess ? 'Granted' : 'Denied'}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={module.betaAccess ? 'Yes' : 'No'}
                          size="small"
                          color={module.betaAccess ? 'success' : 'default'}
                        />
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>

          <Button
            variant="contained"
            startIcon={<Security />}
            onClick={runAccessControlTests}
            disabled={isRunningTests}
            sx={{
              background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
              '&:hover': {
                background: `linear-gradient(135deg, ${theme.palette.primary.dark}, ${theme.palette.primary.main})`
              }
            }}
          >
            {isRunningTests ? 'Running Tests...' : 'Run Access Control Tests'}
          </Button>

          {testResults.length > 0 && (
            <>
              <Divider sx={{ my: 3 }} />
              <Typography variant="h6" gutterBottom>
                Test Results
              </Typography>
              
              <Stack spacing={2}>
                {testResults.map((result, index) => (
                  <Alert
                    key={index}
                    severity={result.status === 'pass' ? 'success' : 
                             result.status === 'fail' ? 'error' :
                             result.status === 'warning' ? 'warning' : 'info'}
                    icon={getStatusIcon(result.status)}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="subtitle2" fontWeight="bold">
                          {result.test}
                        </Typography>
                        <Typography variant="body2">
                          {result.message}
                        </Typography>
                      </Box>
                      <Chip
                        label={result.status.toUpperCase()}
                        size="small"
                        sx={{
                          backgroundColor: getStatusColor(result.status),
                          color: 'white',
                          fontWeight: 'bold'
                        }}
                      />
                    </Box>
                  </Alert>
                ))}
              </Stack>
            </>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default AccessControlTest;
