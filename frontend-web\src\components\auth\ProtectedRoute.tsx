import React from 'react';
import { Navigate } from 'react-router-dom';

interface ProtectedRouteProps {
  element: React.ReactElement;
  allowedTiers: string[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ element, allowedTiers }) => {
  // In a real app, you'd get the user from an auth context.
  // For simulation, we'll use localStorage.
  const userTier = localStorage.getItem('userTier'); // e.g., 'beta', 'professional', 'enterprise'
  const isAuthenticated = !!userTier;

  if (!isAuthenticated) {
    // If not authenticated, redirect to the main landing page.
    return <Navigate to="/" />;
  }

  if (!allowedTiers.includes(userTier)) {
    // If authenticated but tier is not allowed, redirect to an upgrade page.
    return <Navigate to="/subscription" />;
  }

  return element;
};

export default ProtectedRoute;