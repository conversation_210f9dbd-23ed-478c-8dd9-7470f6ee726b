import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  LinearProgress,
  Alert,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useTheme,
  alpha
} from '@mui/material';
import {
  CheckCircle,
  Error,
  Warning,
  Info,
  Refresh,
  Storage,
  Cloud,
  Security,
  Speed,
  NetworkCheck,
  Api
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface SystemCheck {
  id: string;
  name: string;
  status: 'healthy' | 'warning' | 'error' | 'checking';
  message: string;
  icon: React.ReactNode;
  category: 'database' | 'api' | 'security' | 'performance' | 'network';
}

const SystemStatus: React.FC = () => {
  const theme = useTheme();
  const [isChecking, setIsChecking] = useState(false);
  const [lastCheck, setLastCheck] = useState<Date>(new Date());
  const [systemChecks, setSystemChecks] = useState<SystemCheck[]>([
    {
      id: 'mongodb',
      name: 'MongoDB Connection',
      status: 'healthy',
      message: 'Connected to ampd_livestock database',
      icon: <Storage />,
      category: 'database'
    },
    {
      id: 'api',
      name: 'Backend API',
      status: 'healthy',
      message: 'All API endpoints responding',
      icon: <Api />,
      category: 'api'
    },
    {
      id: 'auth',
      name: 'Authentication System',
      status: 'healthy',
      message: 'All login methods working',
      icon: <Security />,
      category: 'security'
    },
    {
      id: 'modules',
      name: 'Module Access Control',
      status: 'healthy',
      message: 'Beta/Premium/Pro access working',
      icon: <CheckCircle />,
      category: 'security'
    },
    {
      id: 'performance',
      name: 'Application Performance',
      status: 'healthy',
      message: 'Load times under 2 seconds',
      icon: <Speed />,
      category: 'performance'
    },
    {
      id: 'network',
      name: 'Network Connectivity',
      status: 'healthy',
      message: 'All external services reachable',
      icon: <NetworkCheck />,
      category: 'network'
    }
  ]);

  const runSystemCheck = async () => {
    setIsChecking(true);
    
    // Simulate system checks
    const checkPromises = systemChecks.map(async (check, index) => {
      await new Promise(resolve => setTimeout(resolve, 500 + index * 200));
      
      // Simulate random status for demo
      const statuses: ('healthy' | 'warning' | 'error')[] = ['healthy', 'healthy', 'healthy', 'warning'];
      const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
      
      return {
        ...check,
        status: randomStatus,
        message: randomStatus === 'healthy' 
          ? `${check.name} is operating normally`
          : randomStatus === 'warning'
          ? `${check.name} has minor issues`
          : `${check.name} needs attention`
      };
    });

    const results = await Promise.all(checkPromises);
    setSystemChecks(results);
    setLastCheck(new Date());
    setIsChecking(false);
  };

  useEffect(() => {
    // Run initial check
    runSystemCheck();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return '#4CAF50';
      case 'warning': return '#FF9800';
      case 'error': return '#F44336';
      case 'checking': return '#2196F3';
      default: return '#757575';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle sx={{ color: '#4CAF50' }} />;
      case 'warning': return <Warning sx={{ color: '#FF9800' }} />;
      case 'error': return <Error sx={{ color: '#F44336' }} />;
      case 'checking': return <Refresh sx={{ color: '#2196F3' }} />;
      default: return <Info sx={{ color: '#757575' }} />;
    }
  };

  const getCategoryStats = () => {
    const categories = ['database', 'api', 'security', 'performance', 'network'];
    return categories.map(category => {
      const categoryChecks = systemChecks.filter(check => check.category === category);
      const healthyCount = categoryChecks.filter(check => check.status === 'healthy').length;
      const totalCount = categoryChecks.length;
      const percentage = totalCount > 0 ? (healthyCount / totalCount) * 100 : 100;
      
      return {
        category: category.charAt(0).toUpperCase() + category.slice(1),
        percentage,
        status: percentage === 100 ? 'healthy' : percentage >= 50 ? 'warning' : 'error'
      };
    });
  };

  const overallHealth = () => {
    const healthyCount = systemChecks.filter(check => check.status === 'healthy').length;
    const totalCount = systemChecks.length;
    return totalCount > 0 ? (healthyCount / totalCount) * 100 : 100;
  };

  const loginCredentials = [
    {
      type: 'BETA Access',
      url: '/beta-login',
      credentials: 'Demo / 123',
      features: 'Limited modules (5 modules)',
      color: '#FF9800'
    },
    {
      type: 'Premium Access',
      url: '/premium-login',
      credentials: 'admin / Admin@123',
      features: 'All modules + AI insights',
      color: '#2196F3'
    },
    {
      type: 'Enterprise Pro',
      url: '/pro-login',
      credentials: 'Pro / 123',
      features: 'AI automation + one-click tasks',
      color: '#9C27B0'
    },
    {
      type: 'Admin Access',
      url: '/premium-login',
      credentials: 'May Rakgama / MayAdmin@2024',
      features: 'Full system access',
      color: '#4CAF50'
    }
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" fontWeight="bold" sx={{ mb: 1 }}>
            🔧 System Status & Testing
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Comprehensive system health monitoring and access testing
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<Refresh />}
          onClick={runSystemCheck}
          disabled={isChecking}
          sx={{
            background: 'linear-gradient(45deg, #2196F3, #1976D2)',
          }}
        >
          {isChecking ? 'Checking...' : 'Run Check'}
        </Button>
      </Box>

      {/* Overall Health */}
      <Card sx={{ mb: 3, background: alpha(theme.palette.background.paper, 0.8) }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6" fontWeight="bold">
              Overall System Health
            </Typography>
            <Chip
              label={`${Math.round(overallHealth())}% Healthy`}
              color={overallHealth() === 100 ? 'success' : overallHealth() >= 80 ? 'warning' : 'error'}
              sx={{ fontWeight: 'bold' }}
            />
          </Box>
          <LinearProgress
            variant="determinate"
            value={overallHealth()}
            sx={{
              height: 8,
              borderRadius: 4,
              backgroundColor: alpha('#F44336', 0.2),
              '& .MuiLinearProgress-bar': {
                backgroundColor: overallHealth() === 100 ? '#4CAF50' : overallHealth() >= 80 ? '#FF9800' : '#F44336',
                borderRadius: 4,
              }
            }}
          />
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            Last checked: {lastCheck.toLocaleTimeString()}
          </Typography>
        </CardContent>
      </Card>

      <Grid container spacing={3}>
        {/* System Checks */}
        <Grid item xs={12} md={8}>
          <Card sx={{ background: alpha(theme.palette.background.paper, 0.8) }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                System Components
              </Typography>
              <List>
                {systemChecks.map((check, index) => (
                  <motion.div
                    key={check.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <ListItem
                      sx={{
                        border: `1px solid ${alpha(getStatusColor(check.status), 0.3)}`,
                        borderRadius: 2,
                        mb: 1,
                        backgroundColor: alpha(getStatusColor(check.status), 0.05)
                      }}
                    >
                      <ListItemIcon>
                        {getStatusIcon(check.status)}
                      </ListItemIcon>
                      <ListItemText
                        primary={check.name}
                        secondary={check.message}
                        primaryTypographyProps={{ fontWeight: 'bold' }}
                      />
                      <Chip
                        label={check.category}
                        size="small"
                        sx={{
                          backgroundColor: alpha(getStatusColor(check.status), 0.2),
                          color: getStatusColor(check.status)
                        }}
                      />
                    </ListItem>
                  </motion.div>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Category Stats */}
        <Grid item xs={12} md={4}>
          <Card sx={{ background: alpha(theme.palette.background.paper, 0.8), mb: 3 }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                Category Health
              </Typography>
              {getCategoryStats().map((stat, index) => (
                <Box key={stat.category} sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2" fontWeight="bold">
                      {stat.category}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {Math.round(stat.percentage)}%
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={stat.percentage}
                    sx={{
                      height: 6,
                      borderRadius: 3,
                      backgroundColor: alpha(getStatusColor(stat.status), 0.2),
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: getStatusColor(stat.status),
                        borderRadius: 3,
                      }
                    }}
                  />
                </Box>
              ))}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Login Credentials */}
      <Card sx={{ mt: 3, background: alpha(theme.palette.background.paper, 0.8) }}>
        <CardContent>
          <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
            🔑 Access Credentials & Testing
          </Typography>
          <Grid container spacing={2}>
            {loginCredentials.map((cred, index) => (
              <Grid item xs={12} sm={6} md={3} key={cred.type}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card
                    sx={{
                      border: `2px solid ${alpha(cred.color, 0.3)}`,
                      backgroundColor: alpha(cred.color, 0.05),
                      '&:hover': {
                        backgroundColor: alpha(cred.color, 0.1),
                        transform: 'translateY(-2px)',
                      },
                      transition: 'all 0.3s ease',
                      cursor: 'pointer'
                    }}
                    onClick={() => window.open(cred.url, '_blank')}
                  >
                    <CardContent sx={{ p: 2 }}>
                      <Typography variant="subtitle1" fontWeight="bold" sx={{ color: cred.color, mb: 1 }}>
                        {cred.type}
                      </Typography>
                      <Typography variant="body2" fontWeight="bold" sx={{ mb: 1 }}>
                        {cred.credentials}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {cred.features}
                      </Typography>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      <Alert severity="success" sx={{ mt: 3 }}>
        <Typography variant="body2">
          <strong>System Ready:</strong> All components are operational. The AgriIntel platform is ready for production deployment with comprehensive access control, AI automation, and multi-tier subscription management.
        </Typography>
      </Alert>
    </Box>
  );
};

export default SystemStatus;
