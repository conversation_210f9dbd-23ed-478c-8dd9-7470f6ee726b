/**
 * Enhanced Error Recovery System for AgriIntel
 * Automatic error recovery, retry mechanisms, and graceful degradation
 */

import { AppError, ErrorType } from './errorHandling';

export interface RecoveryStrategy {
  name: string;
  canHandle: (error: AppError) => boolean;
  recover: (error: AppError, context?: any) => Promise<boolean>;
  priority: number;
}

export interface RecoveryContext {
  componentName?: string;
  userId?: string;
  route?: string;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
}

class EnhancedErrorRecoveryManager {
  private static instance: EnhancedErrorRecoveryManager;
  private strategies: RecoveryStrategy[] = [];
  private recoveryHistory: Map<string, RecoveryContext[]> = new Map();
  private isRecovering: boolean = false;

  private constructor() {
    this.initializeDefaultStrategies();
  }

  static getInstance(): EnhancedErrorRecoveryManager {
    if (!EnhancedErrorRecoveryManager.instance) {
      EnhancedErrorRecoveryManager.instance = new EnhancedErrorRecoveryManager();
    }
    return EnhancedErrorRecoveryManager.instance;
  }

  private initializeDefaultStrategies() {
    // Network error recovery
    this.addStrategy({
      name: 'NetworkRetry',
      priority: 1,
      canHandle: (error) => error.type === ErrorType.NETWORK,
      recover: async (error, context) => {
        if (context?.retryCount >= 3) return false;
        
        console.log('Attempting network recovery...');
        await this.delay(1000 * (context?.retryCount || 1));
        
        // Check network connectivity
        if (navigator.onLine) {
          return true;
        }
        
        return false;
      }
    });

    // Chunk loading error recovery
    this.addStrategy({
      name: 'ChunkLoadingRetry',
      priority: 2,
      canHandle: (error) => 
        error.message.includes('Loading chunk') || 
        error.message.includes('ChunkLoadError'),
      recover: async (error, context) => {
        if (context?.retryCount >= 2) {
          // If retries failed, reload the page
          console.log('Chunk loading failed multiple times, reloading page...');
          window.location.reload();
          return true;
        }
        
        console.log('Attempting chunk loading recovery...');
        await this.delay(2000);
        
        // Clear module cache if possible
        if ('webpackChunkName' in error.details) {
          this.clearModuleCache(error.details.webpackChunkName);
        }
        
        return true;
      }
    });

    // Authentication error recovery
    this.addStrategy({
      name: 'AuthenticationRecovery',
      priority: 3,
      canHandle: (error) => error.type === ErrorType.AUTHENTICATION,
      recover: async (error, context) => {
        console.log('Attempting authentication recovery...');
        
        // Try to refresh token
        const refreshed = await this.attemptTokenRefresh();
        if (refreshed) return true;
        
        // Redirect to login
        const currentPath = window.location.pathname;
        const loginPath = currentPath.includes('beta') ? '/beta-login' : '/login';
        window.location.href = `${loginPath}?redirect=${encodeURIComponent(currentPath)}`;
        
        return true;
      }
    });

    // Database error recovery
    this.addStrategy({
      name: 'DatabaseRetry',
      priority: 4,
      canHandle: (error) => error.type === ErrorType.DATABASE,
      recover: async (error, context) => {
        if (context?.retryCount >= 2) return false;
        
        console.log('Attempting database recovery...');
        await this.delay(3000 * (context?.retryCount || 1));
        
        // Check if backend is responsive
        try {
          const response = await fetch('/api/health', { 
            method: 'GET',
            timeout: 5000 
          } as any);
          return response.ok;
        } catch {
          return false;
        }
      }
    });

    // Memory error recovery
    this.addStrategy({
      name: 'MemoryCleanup',
      priority: 5,
      canHandle: (error) => 
        error.message.includes('out of memory') ||
        error.message.includes('Maximum call stack'),
      recover: async (error, context) => {
        console.log('Attempting memory cleanup...');
        
        // Clear caches
        this.clearApplicationCaches();
        
        // Trigger garbage collection if available
        if ('gc' in window) {
          (window as any).gc();
        }
        
        // Reduce memory usage
        this.reduceMemoryUsage();
        
        await this.delay(1000);
        return true;
      }
    });

    // Component error recovery
    this.addStrategy({
      name: 'ComponentRecovery',
      priority: 6,
      canHandle: (error) => error.details?.componentStack,
      recover: async (error, context) => {
        console.log('Attempting component recovery...');
        
        // Reset component state if possible
        if (context?.componentName) {
          this.resetComponentState(context.componentName);
        }
        
        return true;
      }
    });

    // Graceful degradation
    this.addStrategy({
      name: 'GracefulDegradation',
      priority: 10, // Lowest priority - fallback
      canHandle: () => true,
      recover: async (error, context) => {
        console.log('Applying graceful degradation...');
        
        // Disable non-essential features
        this.disableNonEssentialFeatures();
        
        // Show simplified UI
        this.enableSimplifiedMode();
        
        return true;
      }
    });
  }

  public addStrategy(strategy: RecoveryStrategy) {
    this.strategies.push(strategy);
    this.strategies.sort((a, b) => a.priority - b.priority);
  }

  public async attemptRecovery(
    error: AppError, 
    context: Partial<RecoveryContext> = {}
  ): Promise<boolean> {
    if (this.isRecovering) {
      console.log('Recovery already in progress, skipping...');
      return false;
    }

    this.isRecovering = true;
    
    const fullContext: RecoveryContext = {
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries: 3,
      ...context
    };

    // Record recovery attempt
    this.recordRecoveryAttempt(error, fullContext);

    try {
      // Find applicable strategies
      const applicableStrategies = this.strategies.filter(strategy => 
        strategy.canHandle(error)
      );

      console.log(`Found ${applicableStrategies.length} recovery strategies for error:`, error);

      // Try each strategy in priority order
      for (const strategy of applicableStrategies) {
        try {
          console.log(`Trying recovery strategy: ${strategy.name}`);
          const success = await strategy.recover(error, fullContext);
          
          if (success) {
            console.log(`Recovery successful with strategy: ${strategy.name}`);
            this.recordSuccessfulRecovery(error, strategy.name);
            return true;
          }
        } catch (recoveryError) {
          console.error(`Recovery strategy ${strategy.name} failed:`, recoveryError);
        }
      }

      console.log('All recovery strategies failed');
      return false;
    } finally {
      this.isRecovering = false;
    }
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private async attemptTokenRefresh(): Promise<boolean> {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      if (!refreshToken) return false;

      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refreshToken })
      });

      if (response.ok) {
        const data = await response.json();
        localStorage.setItem('token', data.token);
        return true;
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
    }
    
    return false;
  }

  private clearModuleCache(chunkName: string) {
    // Clear webpack module cache if accessible
    if ('webpackChunkName' in window) {
      try {
        delete (window as any).__webpack_require__.cache[chunkName];
      } catch (error) {
        console.warn('Could not clear module cache:', error);
      }
    }
  }

  private clearApplicationCaches() {
    // Clear various caches
    try {
      // Clear localStorage non-essential items
      const essentialKeys = ['token', 'refreshToken', 'language', 'theme'];
      Object.keys(localStorage).forEach(key => {
        if (!essentialKeys.includes(key)) {
          localStorage.removeItem(key);
        }
      });

      // Clear sessionStorage
      sessionStorage.clear();

      // Clear browser caches if possible
      if ('caches' in window) {
        caches.keys().then(names => {
          names.forEach(name => {
            if (!name.includes('essential')) {
              caches.delete(name);
            }
          });
        });
      }
    } catch (error) {
      console.warn('Cache clearing failed:', error);
    }
  }

  private reduceMemoryUsage() {
    // Dispatch custom event to notify components to reduce memory usage
    window.dispatchEvent(new CustomEvent('agri-reduce-memory', {
      detail: { timestamp: Date.now() }
    }));
  }

  private resetComponentState(componentName: string) {
    // Dispatch custom event to reset specific component
    window.dispatchEvent(new CustomEvent('agri-reset-component', {
      detail: { componentName, timestamp: Date.now() }
    }));
  }

  private disableNonEssentialFeatures() {
    // Store disabled features in sessionStorage
    const disabledFeatures = [
      'animations',
      'backgroundImages',
      'advancedCharts',
      'realTimeUpdates'
    ];
    
    sessionStorage.setItem('disabledFeatures', JSON.stringify(disabledFeatures));
    
    // Dispatch event to notify components
    window.dispatchEvent(new CustomEvent('agri-disable-features', {
      detail: { features: disabledFeatures }
    }));
  }

  private enableSimplifiedMode() {
    sessionStorage.setItem('simplifiedMode', 'true');
    
    window.dispatchEvent(new CustomEvent('agri-simplified-mode', {
      detail: { enabled: true }
    }));
  }

  private recordRecoveryAttempt(error: AppError, context: RecoveryContext) {
    const errorKey = `${error.type}-${error.message.substring(0, 50)}`;
    
    if (!this.recoveryHistory.has(errorKey)) {
      this.recoveryHistory.set(errorKey, []);
    }
    
    this.recoveryHistory.get(errorKey)!.push(context);
  }

  private recordSuccessfulRecovery(error: AppError, strategyName: string) {
    console.log(`Successful recovery recorded: ${strategyName} for ${error.type}`);
    
    // Could send to analytics service
    if (process.env.NODE_ENV === 'production') {
      // Analytics.track('error_recovery_success', {
      //   errorType: error.type,
      //   strategy: strategyName,
      //   timestamp: Date.now()
      // });
    }
  }

  public getRecoveryHistory(): Map<string, RecoveryContext[]> {
    return new Map(this.recoveryHistory);
  }

  public clearHistory() {
    this.recoveryHistory.clear();
  }
}

// React hook for error recovery
export const useErrorRecovery = () => {
  const manager = EnhancedErrorRecoveryManager.getInstance();

  const attemptRecovery = async (error: AppError, context?: Partial<RecoveryContext>) => {
    return manager.attemptRecovery(error, context);
  };

  return {
    attemptRecovery,
    getRecoveryHistory: () => manager.getRecoveryHistory()
  };
};

export default EnhancedErrorRecoveryManager;
