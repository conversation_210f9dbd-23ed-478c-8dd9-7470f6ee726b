/**
 * Color Utilities - AgriIntel
 * Color utility classes for the application
 */

/* Text Colors */
.text-primary { color: var(--color-primary) !important; }
.text-primary-dark { color: var(--color-primary-dark) !important; }
.text-primary-light { color: var(--color-primary-light) !important; }
.text-secondary { color: var(--color-secondary) !important; }
.text-secondary-dark { color: var(--color-secondary-dark) !important; }
.text-secondary-light { color: var(--color-secondary-light) !important; }
.text-accent { color: var(--color-accent) !important; }
.text-accent-dark { color: var(--color-accent-dark) !important; }
.text-accent-light { color: var(--color-accent-light) !important; }

.text-success { color: var(--color-success) !important; }
.text-warning { color: var(--color-warning) !important; }
.text-error { color: var(--color-error) !important; }
.text-info { color: var(--color-info) !important; }

.text-white { color: var(--color-white) !important; }
.text-black { color: var(--color-black) !important; }

.text-muted { color: var(--color-text-secondary) !important; }
.text-light { color: var(--color-text-tertiary) !important; }
.text-dark { color: var(--color-text-primary) !important; }

/* Background Colors */
.bg-primary { background-color: var(--color-primary) !important; }
.bg-primary-dark { background-color: var(--color-primary-dark) !important; }
.bg-primary-light { background-color: var(--color-primary-light) !important; }
.bg-secondary { background-color: var(--color-secondary) !important; }
.bg-secondary-dark { background-color: var(--color-secondary-dark) !important; }
.bg-secondary-light { background-color: var(--color-secondary-light) !important; }
.bg-accent { background-color: var(--color-accent) !important; }
.bg-accent-dark { background-color: var(--color-accent-dark) !important; }
.bg-accent-light { background-color: var(--color-accent-light) !important; }

.bg-success { background-color: var(--color-success) !important; }
.bg-warning { background-color: var(--color-warning) !important; }
.bg-error { background-color: var(--color-error) !important; }
.bg-info { background-color: var(--color-info) !important; }

.bg-white { background-color: var(--color-white) !important; }
.bg-black { background-color: var(--color-black) !important; }
.bg-transparent { background-color: transparent !important; }

.bg-light { background-color: var(--color-background-secondary) !important; }
.bg-lighter { background-color: var(--color-background-tertiary) !important; }

/* Background Gradients */
.bg-gradient-primary {
  background: var(--agri-pro-gradient) !important;
  color: var(--color-white);
}

.bg-gradient-secondary {
  background: var(--agri-premium-gradient) !important;
  color: var(--color-white);
}

.bg-gradient-accent {
  background: var(--agri-beta-gradient) !important;
  color: var(--color-white);
}

/* Border Colors */
.border-primary { border-color: var(--color-primary) !important; }
.border-primary-dark { border-color: var(--color-primary-dark) !important; }
.border-primary-light { border-color: var(--color-primary-light) !important; }
.border-secondary { border-color: var(--color-secondary) !important; }
.border-secondary-dark { border-color: var(--color-secondary-dark) !important; }
.border-secondary-light { border-color: var(--color-secondary-light) !important; }
.border-accent { border-color: var(--color-accent) !important; }
.border-accent-dark { border-color: var(--color-accent-dark) !important; }
.border-accent-light { border-color: var(--color-accent-light) !important; }

.border-success { border-color: var(--color-success) !important; }
.border-warning { border-color: var(--color-warning) !important; }
.border-error { border-color: var(--color-error) !important; }
.border-info { border-color: var(--color-info) !important; }

.border-white { border-color: var(--color-white) !important; }
.border-black { border-color: var(--color-black) !important; }
.border-transparent { border-color: transparent !important; }

.border-light { border-color: var(--color-border-light) !important; }
.border-default { border-color: var(--color-border) !important; }
.border-dark { border-color: var(--color-border-dark) !important; }

/* Opacity Utilities */
.opacity-0 { opacity: 0 !important; }
.opacity-25 { opacity: 0.25 !important; }
.opacity-50 { opacity: 0.5 !important; }
.opacity-75 { opacity: 0.75 !important; }
.opacity-100 { opacity: 1 !important; }

/* Glass Morphism Utilities */
.glass-bg {
  background: var(--glass-bg) !important;
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
}

.glass-border {
  border: 1px solid var(--glass-border) !important;
}

.glass-shadow {
  box-shadow: var(--glass-shadow) !important;
}

.glass-card {
  background: var(--glass-bg) !important;
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border) !important;
  box-shadow: var(--glass-shadow) !important;
}

/* Status Color Backgrounds with Opacity */
.bg-success-light { background-color: rgba(76, 175, 80, 0.1) !important; }
.bg-warning-light { background-color: rgba(255, 152, 0, 0.1) !important; }
.bg-error-light { background-color: rgba(244, 67, 54, 0.1) !important; }
.bg-info-light { background-color: rgba(33, 150, 243, 0.1) !important; }

.bg-primary-light-10 { background-color: rgba(46, 125, 50, 0.1) !important; }
.bg-primary-light-20 { background-color: rgba(46, 125, 50, 0.2) !important; }
.bg-primary-light-30 { background-color: rgba(46, 125, 50, 0.3) !important; }

.bg-secondary-light-10 { background-color: rgba(21, 101, 192, 0.1) !important; }
.bg-secondary-light-20 { background-color: rgba(21, 101, 192, 0.2) !important; }
.bg-secondary-light-30 { background-color: rgba(21, 101, 192, 0.3) !important; }

.bg-accent-light-10 { background-color: rgba(245, 124, 0, 0.1) !important; }
.bg-accent-light-20 { background-color: rgba(245, 124, 0, 0.2) !important; }
.bg-accent-light-30 { background-color: rgba(245, 124, 0, 0.3) !important; }

/* Hover States */
.hover-bg-primary:hover { background-color: var(--color-primary) !important; }
.hover-bg-secondary:hover { background-color: var(--color-secondary) !important; }
.hover-bg-accent:hover { background-color: var(--color-accent) !important; }
.hover-bg-light:hover { background-color: var(--color-background-hover) !important; }

.hover-text-primary:hover { color: var(--color-primary) !important; }
.hover-text-secondary:hover { color: var(--color-secondary) !important; }
.hover-text-accent:hover { color: var(--color-accent) !important; }
.hover-text-white:hover { color: var(--color-white) !important; }

/* Focus States */
.focus-border-primary:focus { border-color: var(--color-primary) !important; }
.focus-border-secondary:focus { border-color: var(--color-secondary) !important; }
.focus-border-accent:focus { border-color: var(--color-accent) !important; }

/* AgriIntel Brand Colors */
.text-agri-green { color: var(--agri-green-primary) !important; }
.text-agri-blue { color: var(--agri-blue-primary) !important; }
.text-agri-gold { color: var(--agri-gold-primary) !important; }

.bg-agri-green { background-color: var(--agri-green-primary) !important; }
.bg-agri-blue { background-color: var(--agri-blue-primary) !important; }
.bg-agri-gold { background-color: var(--agri-gold-primary) !important; }

.border-agri-green { border-color: var(--agri-green-primary) !important; }
.border-agri-blue { border-color: var(--agri-blue-primary) !important; }
.border-agri-gold { border-color: var(--agri-gold-primary) !important; }
