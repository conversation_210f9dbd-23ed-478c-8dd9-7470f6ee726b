/**
 * Form Component Styles
 * Professional form designs for AgriIntel platform
 */

/* ===== BASE FORM STYLES ===== */

.agri-form {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.agri-form-container {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--glass-shadow);
}

/* ===== FORM GROUPS ===== */

.agri-form-group {
  margin-bottom: var(--spacing-lg);
}

.agri-form-group:last-child {
  margin-bottom: 0;
}

.agri-form-row {
  display: flex;
  gap: var(--spacing-md);
  align-items: flex-start;
}

.agri-form-col {
  flex: 1;
}

.agri-form-col-auto {
  flex: none;
}

/* ===== LABELS ===== */

.agri-form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
  line-height: var(--line-height-normal);
}

.agri-form-label.required::after {
  content: ' *';
  color: var(--color-error);
}

.agri-form-label-inline {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: 0;
}

/* ===== INPUT FIELDS ===== */

.agri-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  font-family: inherit;
  font-size: var(--font-size-base);
  color: var(--color-text-primary);
  background: var(--color-background-primary);
  transition: var(--transition-normal);
  min-height: 44px;
}

.agri-input:focus {
  outline: none;
  border-color: var(--agri-primary);
  box-shadow: 0 0 0 3px rgba(21, 101, 192, 0.1);
}

.agri-input:disabled {
  background: var(--color-background-disabled);
  color: var(--color-text-disabled);
  cursor: not-allowed;
}

.agri-input::placeholder {
  color: var(--color-text-placeholder);
}

/* Input Variants */
.agri-input-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-sm);
  min-height: 36px;
}

.agri-input-lg {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-lg);
  min-height: 52px;
}

/* Input States */
.agri-input.error {
  border-color: var(--color-error);
}

.agri-input.error:focus {
  box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.1);
}

.agri-input.success {
  border-color: var(--color-success);
}

.agri-input.success:focus {
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

/* ===== TEXTAREA ===== */

.agri-textarea {
  resize: vertical;
  min-height: 120px;
  line-height: var(--line-height-relaxed);
}

.agri-textarea-sm {
  min-height: 80px;
}

.agri-textarea-lg {
  min-height: 160px;
}

/* ===== SELECT FIELDS ===== */

.agri-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-sm) center;
  background-repeat: no-repeat;
  background-size: 16px 16px;
  padding-right: var(--spacing-xl);
}

.agri-select:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%231565c0' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

/* ===== CHECKBOX & RADIO ===== */

.agri-checkbox,
.agri-radio {
  appearance: none;
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-border);
  background: var(--color-background-primary);
  cursor: pointer;
  transition: var(--transition-normal);
  position: relative;
  flex-shrink: 0;
}

.agri-checkbox {
  border-radius: var(--radius-sm);
}

.agri-radio {
  border-radius: var(--radius-full);
}

.agri-checkbox:checked,
.agri-radio:checked {
  background: var(--agri-primary);
  border-color: var(--agri-primary);
}

.agri-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--color-white);
  font-size: 12px;
  font-weight: bold;
}

.agri-radio:checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: var(--color-white);
  border-radius: var(--radius-full);
}

.agri-checkbox:focus,
.agri-radio:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(21, 101, 192, 0.1);
}

/* ===== INPUT GROUPS ===== */

.agri-input-group {
  display: flex;
  align-items: stretch;
}

.agri-input-group .agri-input {
  border-radius: 0;
  border-right: none;
}

.agri-input-group .agri-input:first-child {
  border-radius: var(--radius-lg) 0 0 var(--radius-lg);
}

.agri-input-group .agri-input:last-child {
  border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
  border-right: 1px solid var(--color-border);
}

.agri-input-group .agri-input:only-child {
  border-radius: var(--radius-lg);
  border-right: 1px solid var(--color-border);
}

.agri-input-addon {
  display: flex;
  align-items: center;
  padding: 0 var(--spacing-md);
  background: var(--color-background-secondary);
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  white-space: nowrap;
}

.agri-input-addon:first-child {
  border-radius: var(--radius-lg) 0 0 var(--radius-lg);
  border-right: none;
}

.agri-input-addon:last-child {
  border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
  border-left: none;
}

/* ===== HELP TEXT & VALIDATION ===== */

.agri-form-help {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin-top: var(--spacing-xs);
  line-height: var(--line-height-normal);
}

.agri-form-error {
  font-size: var(--font-size-xs);
  color: var(--color-error);
  margin-top: var(--spacing-xs);
  line-height: var(--line-height-normal);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.agri-form-success {
  font-size: var(--font-size-xs);
  color: var(--color-success);
  margin-top: var(--spacing-xs);
  line-height: var(--line-height-normal);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

/* ===== FORM ACTIONS ===== */

.agri-form-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--color-border);
}

.agri-form-actions.center {
  justify-content: center;
}

.agri-form-actions.start {
  justify-content: flex-start;
}

.agri-form-actions.between {
  justify-content: space-between;
}

/* ===== FIELDSETS ===== */

.agri-fieldset {
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.agri-fieldset:last-child {
  margin-bottom: 0;
}

.agri-legend {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  padding: 0 var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
  .agri-form-container {
    padding: var(--spacing-lg);
  }
  
  .agri-form-row {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .agri-form-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .agri-input-group {
    flex-direction: column;
  }
  
  .agri-input-group .agri-input,
  .agri-input-addon {
    border-radius: var(--radius-lg);
    border: 1px solid var(--color-border);
  }
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  .agri-input,
  .agri-checkbox,
  .agri-radio {
    transition: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .agri-input,
  .agri-select,
  .agri-checkbox,
  .agri-radio {
    border: 2px solid var(--color-text-primary);
  }
  
  .agri-input:focus,
  .agri-select:focus,
  .agri-checkbox:focus,
  .agri-radio:focus {
    box-shadow: 0 0 0 3px var(--color-text-primary);
  }
  
  .agri-checkbox:checked,
  .agri-radio:checked {
    background: var(--color-text-primary);
    border-color: var(--color-text-primary);
  }
}
