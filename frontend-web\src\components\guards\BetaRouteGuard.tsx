import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { Al<PERSON>, Box, Typography, But<PERSON>, Card, CardContent, Chip, useTheme, alpha } from '@mui/material';
import { Lock, Upgrade, ArrowBack, Star } from '@mui/icons-material';
import { motion } from 'framer-motion';
import { validateRouteAccess, getModuleRestrictions, getUpgradePath, moduleConfigurations } from '../../utils/enhancedAccessControl';

interface BetaRouteGuardProps {
  children: React.ReactNode;
}

const BetaRouteGuard: React.FC<BetaRouteGuardProps> = ({ children }) => {
  const { user } = useAuth();
  const location = useLocation();
  const theme = useTheme();

  // Validate route access using enhanced access control
  const accessValidation = validateRouteAccess(user, location.pathname);

  // If access is denied, handle redirection or show upgrade prompt
  if (!accessValidation.allowed) {
    if (accessValidation.redirectTo) {
      return <Navigate to={accessValidation.redirectTo} replace />;
    }

    // Extract module from path for upgrade prompt
    const pathSegments = location.pathname.split('/');
    const currentModule = pathSegments[pathSegments.length - 1] || 'dashboard';
    const moduleConfig = moduleConfigurations[currentModule];
    const restrictions = getModuleRestrictions(user, currentModule);
    const upgradePath = getUpgradePath(user);

    if (moduleConfig && !moduleConfig.betaAccess) {
      return (
        <Box
          sx={{
            minHeight: '100vh',
            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.background.default, 0.9)})`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            p: 3
          }}
        >
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Card
              sx={{
                maxWidth: 600,
                background: alpha(theme.palette.background.paper, 0.9),
                backdropFilter: 'blur(20px)',
                border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                borderRadius: 3,
                overflow: 'hidden'
              }}
            >
              <CardContent sx={{ p: 4, textAlign: 'center' }}>
                <Box
                  sx={{
                    width: 100,
                    height: 100,
                    borderRadius: '50%',
                    background: `linear-gradient(135deg, ${theme.palette.warning.main}, ${theme.palette.warning.dark})`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mx: 'auto',
                    mb: 3,
                    boxShadow: theme.shadows[8]
                  }}
                >
                  <Lock sx={{ fontSize: 50, color: 'white' }} />
                </Box>

                <Chip
                  label="Premium Feature"
                  sx={{
                    background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
                    color: 'white',
                    fontWeight: 'bold',
                    mb: 2
                  }}
                />

                <Typography variant="h4" fontWeight="bold" gutterBottom>
                  {moduleConfig.name}
                </Typography>

                <Typography variant="h6" color="text.secondary" sx={{ mb: 3 }}>
                  {moduleConfig.description}
                </Typography>

                <Alert severity="info" sx={{ mb: 3, textAlign: 'left' }}>
                  <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
                    Your BETA access includes:
                  </Typography>
                  <Typography variant="body2">
                    • Dashboard & Overview<br/>
                    • Animal Management (up to 50 animals)<br/>
                    • Basic Health Records<br/>
                    • Educational Resources
                  </Typography>
                </Alert>

                {restrictions.length > 0 && (
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
                      Upgrade Benefits:
                    </Typography>
                    {restrictions.map((restriction, index) => (
                      <Typography key={index} variant="body2" color="text.secondary">
                        • {restriction}
                      </Typography>
                    ))}
                  </Box>
                )}

                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'center' }}>
                  <Button
                    variant="contained"
                    startIcon={<Star />}
                    onClick={() => window.location.href = '/login'}
                    size="large"
                    sx={{
                      background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
                      '&:hover': {
                        background: `linear-gradient(135deg, ${theme.palette.primary.dark}, ${theme.palette.primary.main})`
                      }
                    }}
                  >
                    Upgrade to {upgradePath.next} - {upgradePath.price}
                  </Button>

                  <Button
                    variant="outlined"
                    startIcon={<ArrowBack />}
                    onClick={() => window.location.href = '/beta-dashboard'}
                    size="large"
                  >
                    Back to BETA
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Box>
      );
    }
  }

  // Access granted - render children
  return <>{children}</>;
};

export default BetaRouteGuard;
