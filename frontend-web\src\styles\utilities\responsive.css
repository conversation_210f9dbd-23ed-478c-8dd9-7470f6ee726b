/**
 * Responsive Utilities - AgriIntel
 * Responsive design utility classes for different screen sizes
 */

/* ===== BREAKPOINT VARIABLES ===== */
:root {
  --breakpoint-xs: 480px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* ===== RESPONSIVE DISPLAY UTILITIES ===== */

/* Mobile First - Show on all screens by default */
.block { display: block !important; }
.inline-block { display: inline-block !important; }
.inline { display: inline !important; }
.flex { display: flex !important; }
.inline-flex { display: inline-flex !important; }
.grid { display: grid !important; }
.hidden { display: none !important; }

/* Small screens and up (640px+) */
@media (min-width: 640px) {
  .sm\:block { display: block !important; }
  .sm\:inline-block { display: inline-block !important; }
  .sm\:inline { display: inline !important; }
  .sm\:flex { display: flex !important; }
  .sm\:inline-flex { display: inline-flex !important; }
  .sm\:grid { display: grid !important; }
  .sm\:hidden { display: none !important; }
}

/* Medium screens and up (768px+) */
@media (min-width: 768px) {
  .md\:block { display: block !important; }
  .md\:inline-block { display: inline-block !important; }
  .md\:inline { display: inline !important; }
  .md\:flex { display: flex !important; }
  .md\:inline-flex { display: inline-flex !important; }
  .md\:grid { display: grid !important; }
  .md\:hidden { display: none !important; }
}

/* Large screens and up (1024px+) */
@media (min-width: 1024px) {
  .lg\:block { display: block !important; }
  .lg\:inline-block { display: inline-block !important; }
  .lg\:inline { display: inline !important; }
  .lg\:flex { display: flex !important; }
  .lg\:inline-flex { display: inline-flex !important; }
  .lg\:grid { display: grid !important; }
  .lg\:hidden { display: none !important; }
}

/* Extra large screens and up (1280px+) */
@media (min-width: 1280px) {
  .xl\:block { display: block !important; }
  .xl\:inline-block { display: inline-block !important; }
  .xl\:inline { display: inline !important; }
  .xl\:flex { display: flex !important; }
  .xl\:inline-flex { display: inline-flex !important; }
  .xl\:grid { display: grid !important; }
  .xl\:hidden { display: none !important; }
}

/* ===== RESPONSIVE GRID UTILITIES ===== */

/* Grid columns for different screen sizes */
@media (min-width: 640px) {
  .sm\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
  .sm\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
  .sm\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }
  .sm\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)) !important; }
  .sm\:grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)) !important; }
}

@media (min-width: 768px) {
  .md\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }
  .md\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)) !important; }
  .md\:grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)) !important; }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
  .lg\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }
  .lg\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)) !important; }
  .lg\:grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)) !important; }
}

@media (min-width: 1280px) {
  .xl\:grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
  .xl\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
  .xl\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
  .xl\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }
  .xl\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)) !important; }
  .xl\:grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)) !important; }
}

/* ===== RESPONSIVE FLEXBOX UTILITIES ===== */

@media (min-width: 640px) {
  .sm\:flex-row { flex-direction: row !important; }
  .sm\:flex-col { flex-direction: column !important; }
  .sm\:justify-start { justify-content: flex-start !important; }
  .sm\:justify-center { justify-content: center !important; }
  .sm\:justify-end { justify-content: flex-end !important; }
  .sm\:justify-between { justify-content: space-between !important; }
  .sm\:items-start { align-items: flex-start !important; }
  .sm\:items-center { align-items: center !important; }
  .sm\:items-end { align-items: flex-end !important; }
}

@media (min-width: 768px) {
  .md\:flex-row { flex-direction: row !important; }
  .md\:flex-col { flex-direction: column !important; }
  .md\:justify-start { justify-content: flex-start !important; }
  .md\:justify-center { justify-content: center !important; }
  .md\:justify-end { justify-content: flex-end !important; }
  .md\:justify-between { justify-content: space-between !important; }
  .md\:items-start { align-items: flex-start !important; }
  .md\:items-center { align-items: center !important; }
  .md\:items-end { align-items: flex-end !important; }
}

@media (min-width: 1024px) {
  .lg\:flex-row { flex-direction: row !important; }
  .lg\:flex-col { flex-direction: column !important; }
  .lg\:justify-start { justify-content: flex-start !important; }
  .lg\:justify-center { justify-content: center !important; }
  .lg\:justify-end { justify-content: flex-end !important; }
  .lg\:justify-between { justify-content: space-between !important; }
  .lg\:items-start { align-items: flex-start !important; }
  .lg\:items-center { align-items: center !important; }
  .lg\:items-end { align-items: flex-end !important; }
}

/* ===== RESPONSIVE SPACING ===== */

@media (min-width: 640px) {
  .sm\:p-0 { padding: 0 !important; }
  .sm\:p-2 { padding: var(--spacing-sm) !important; }
  .sm\:p-4 { padding: var(--spacing-lg) !important; }
  .sm\:p-6 { padding: var(--spacing-2xl) !important; }
  .sm\:m-0 { margin: 0 !important; }
  .sm\:m-2 { margin: var(--spacing-sm) !important; }
  .sm\:m-4 { margin: var(--spacing-lg) !important; }
  .sm\:m-6 { margin: var(--spacing-2xl) !important; }
}

@media (min-width: 768px) {
  .md\:p-0 { padding: 0 !important; }
  .md\:p-2 { padding: var(--spacing-sm) !important; }
  .md\:p-4 { padding: var(--spacing-lg) !important; }
  .md\:p-6 { padding: var(--spacing-2xl) !important; }
  .md\:p-8 { padding: var(--spacing-3xl) !important; }
  .md\:m-0 { margin: 0 !important; }
  .md\:m-2 { margin: var(--spacing-sm) !important; }
  .md\:m-4 { margin: var(--spacing-lg) !important; }
  .md\:m-6 { margin: var(--spacing-2xl) !important; }
  .md\:m-8 { margin: var(--spacing-3xl) !important; }
}

@media (min-width: 1024px) {
  .lg\:p-0 { padding: 0 !important; }
  .lg\:p-2 { padding: var(--spacing-sm) !important; }
  .lg\:p-4 { padding: var(--spacing-lg) !important; }
  .lg\:p-6 { padding: var(--spacing-2xl) !important; }
  .lg\:p-8 { padding: var(--spacing-3xl) !important; }
  .lg\:p-10 { padding: var(--spacing-4xl) !important; }
  .lg\:m-0 { margin: 0 !important; }
  .lg\:m-2 { margin: var(--spacing-sm) !important; }
  .lg\:m-4 { margin: var(--spacing-lg) !important; }
  .lg\:m-6 { margin: var(--spacing-2xl) !important; }
  .lg\:m-8 { margin: var(--spacing-3xl) !important; }
  .lg\:m-10 { margin: var(--spacing-4xl) !important; }
}

/* ===== RESPONSIVE TEXT ALIGNMENT ===== */

@media (min-width: 640px) {
  .sm\:text-left { text-align: left !important; }
  .sm\:text-center { text-align: center !important; }
  .sm\:text-right { text-align: right !important; }
}

@media (min-width: 768px) {
  .md\:text-left { text-align: left !important; }
  .md\:text-center { text-align: center !important; }
  .md\:text-right { text-align: right !important; }
}

@media (min-width: 1024px) {
  .lg\:text-left { text-align: left !important; }
  .lg\:text-center { text-align: center !important; }
  .lg\:text-right { text-align: right !important; }
}

/* ===== RESPONSIVE WIDTH/HEIGHT ===== */

@media (min-width: 640px) {
  .sm\:w-full { width: 100% !important; }
  .sm\:w-1\/2 { width: 50% !important; }
  .sm\:w-1\/3 { width: 33.333333% !important; }
  .sm\:w-2\/3 { width: 66.666667% !important; }
  .sm\:w-1\/4 { width: 25% !important; }
  .sm\:w-3\/4 { width: 75% !important; }
}

@media (min-width: 768px) {
  .md\:w-full { width: 100% !important; }
  .md\:w-1\/2 { width: 50% !important; }
  .md\:w-1\/3 { width: 33.333333% !important; }
  .md\:w-2\/3 { width: 66.666667% !important; }
  .md\:w-1\/4 { width: 25% !important; }
  .md\:w-3\/4 { width: 75% !important; }
}

@media (min-width: 1024px) {
  .lg\:w-full { width: 100% !important; }
  .lg\:w-1\/2 { width: 50% !important; }
  .lg\:w-1\/3 { width: 33.333333% !important; }
  .lg\:w-2\/3 { width: 66.666667% !important; }
  .lg\:w-1\/4 { width: 25% !important; }
  .lg\:w-3\/4 { width: 75% !important; }
}

/* ===== MOBILE-SPECIFIC UTILITIES ===== */

/* Mobile only (up to 639px) */
@media (max-width: 639px) {
  .mobile-only { display: block !important; }
  .mobile-hidden { display: none !important; }
  
  .mobile-text-center { text-align: center !important; }
  .mobile-text-left { text-align: left !important; }
  
  .mobile-full-width { width: 100% !important; }
  .mobile-p-2 { padding: var(--spacing-sm) !important; }
  .mobile-p-4 { padding: var(--spacing-lg) !important; }
  
  .mobile-flex-col { flex-direction: column !important; }
  .mobile-items-center { align-items: center !important; }
  .mobile-justify-center { justify-content: center !important; }
}

/* Desktop only (1024px+) */
@media (min-width: 1024px) {
  .desktop-only { display: block !important; }
  .desktop-hidden { display: none !important; }
}

/* ===== CONTAINER RESPONSIVE UTILITIES ===== */

.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-md);
  padding-right: var(--spacing-md);
}

@media (min-width: 640px) {
  .container { max-width: 640px; }
}

@media (min-width: 768px) {
  .container { max-width: 768px; }
}

@media (min-width: 1024px) {
  .container { max-width: 1024px; }
}

@media (min-width: 1280px) {
  .container { max-width: 1280px; }
}

@media (min-width: 1536px) {
  .container { max-width: 1536px; }
}
