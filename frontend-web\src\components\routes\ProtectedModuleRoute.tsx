import React from 'react';
import { useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import UnifiedAccessControl from '../access/UnifiedAccessControl';
import { canAccessModule } from '../../utils/unifiedAccessControl';

interface ProtectedModuleRouteProps {
  moduleId: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * ProtectedModuleRoute - Wraps module components with unified access control
 * Shows upgrade prompts for locked modules instead of hiding them completely
 */
const ProtectedModuleRoute: React.FC<ProtectedModuleRouteProps> = ({
  moduleId,
  children,
  fallback
}) => {
  const { user } = useAuth();
  const location = useLocation();

  // Extract module ID from path if not provided
  const actualModuleId = moduleId || location.pathname.split('/').pop() || 'dashboard';

  // Check if user has access to this module
  const hasAccess = canAccessModule(user, actualModuleId);

  // If user has access, render the module
  if (hasAccess) {
    return <>{children}</>;
  }

  // If no access, show unified access control (upgrade prompt)
  return (
    <UnifiedAccessControl
      moduleId={actualModuleId}
      showUpgradePrompt={true}
      fallbackComponent={fallback}
    >
      {children}
    </UnifiedAccessControl>
  );
};

export default ProtectedModuleRoute;
