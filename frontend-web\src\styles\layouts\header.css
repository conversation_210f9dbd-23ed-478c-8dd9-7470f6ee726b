/**
 * Header Layout Styles
 * Professional header navigation for AgriIntel platform
 */

/* ===== HEADER CONTAINER ===== */

.agri-header {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border-bottom: 1px solid var(--glass-border);
  z-index: var(--z-header);
  transition: var(--transition-normal);
}

.agri-header.scrolled {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: var(--shadow-md);
}

.agri-header.dark-mode {
  background: rgba(18, 18, 18, 0.95);
}

/* ===== HEADER CONTENT ===== */

.agri-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

/* ===== HEADER LEFT SECTION ===== */

.agri-header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.agri-header-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  text-decoration: none;
  color: inherit;
}

.agri-header-logo {
  height: 48px;
  width: auto;
  object-fit: contain;
  transition: var(--transition-normal);
}

.agri-header-logo:hover {
  transform: scale(1.05);
}

.agri-header-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0;
  white-space: nowrap;
}

.agri-header-subtitle {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin: 0;
  white-space: nowrap;
}

/* Mobile Menu Toggle */
.agri-mobile-menu-toggle {
  display: none;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border: none;
  border-radius: var(--radius-md);
  background: transparent;
  color: var(--color-text-primary);
  cursor: pointer;
  transition: var(--transition-normal);
}

.agri-mobile-menu-toggle:hover {
  background: var(--color-background-hover);
}

.agri-mobile-menu-toggle:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}

/* ===== HEADER NAVIGATION ===== */

.agri-header-nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.agri-nav-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  list-style: none;
  margin: 0;
  padding: 0;
}

.agri-nav-item {
  position: relative;
}

.agri-nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--color-text-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-base);
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
  white-space: nowrap;
}

.agri-nav-link:hover {
  background: var(--color-background-hover);
  color: var(--agri-primary);
}

.agri-nav-link.active {
  background: var(--agri-primary);
  color: var(--color-white);
}

.agri-nav-link:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}

/* ===== HEADER RIGHT SECTION ===== */

.agri-header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* Search */
.agri-header-search {
  position: relative;
  min-width: 300px;
}

.agri-search-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md) var(--spacing-sm) var(--spacing-xl);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-full);
  background: var(--color-background-secondary);
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  transition: var(--transition-normal);
}

.agri-search-input:focus {
  outline: none;
  border-color: var(--agri-primary);
  background: var(--color-background-primary);
  box-shadow: 0 0 0 3px rgba(21, 101, 192, 0.1);
}

.agri-search-input::placeholder {
  color: var(--color-text-placeholder);
}

.agri-search-icon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-text-secondary);
  pointer-events: none;
}

/* Language Selector */
.agri-language-selector {
  min-width: 120px;
}

/* Theme Toggle */
.agri-theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-full);
  background: var(--color-background-primary);
  color: var(--color-text-primary);
  cursor: pointer;
  transition: var(--transition-normal);
}

.agri-theme-toggle:hover {
  background: var(--color-background-hover);
  border-color: var(--agri-primary);
  transform: scale(1.05);
}

.agri-theme-toggle:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}

/* Notifications */
.agri-notifications {
  position: relative;
}

.agri-notification-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border: none;
  border-radius: var(--radius-full);
  background: transparent;
  color: var(--color-text-primary);
  cursor: pointer;
  transition: var(--transition-normal);
  position: relative;
}

.agri-notification-btn:hover {
  background: var(--color-background-hover);
}

.agri-notification-btn:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}

.agri-notification-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  background: var(--color-error);
  border-radius: var(--radius-full);
  border: 2px solid var(--color-background-primary);
}

.agri-notification-count {
  position: absolute;
  top: 4px;
  right: 4px;
  min-width: 16px;
  height: 16px;
  background: var(--color-error);
  color: var(--color-white);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--color-background-primary);
}

/* User Profile */
.agri-user-profile {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: var(--transition-normal);
}

.agri-user-profile:hover {
  background: var(--color-background-hover);
}

.agri-user-profile:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}

.agri-user-avatar {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
  background: var(--agri-primary);
  color: var(--color-white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-sm);
  flex-shrink: 0;
}

.agri-user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.agri-user-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  line-height: 1.2;
  white-space: nowrap;
}

.agri-user-role {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  line-height: 1.2;
  white-space: nowrap;
}

/* Action Buttons */
.agri-header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.agri-header-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-lg);
  font-family: inherit;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-normal);
  white-space: nowrap;
}

.agri-header-btn:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}

.agri-header-btn-primary {
  background: var(--agri-primary);
  color: var(--color-white);
  border: 1px solid var(--agri-primary);
}

.agri-header-btn-primary:hover {
  background: var(--agri-primary-dark);
  border-color: var(--agri-primary-dark);
  transform: translateY(-1px);
}

.agri-header-btn-outline {
  background: transparent;
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.agri-header-btn-outline:hover {
  background: var(--color-background-hover);
  border-color: var(--agri-primary);
  color: var(--agri-primary);
}

/* ===== MOBILE MENU ===== */

.agri-mobile-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--color-background-primary);
  border: 1px solid var(--color-border);
  border-top: none;
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-lg);
  display: none;
  z-index: var(--z-dropdown);
}

.agri-mobile-menu.open {
  display: block;
}

.agri-mobile-nav {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.agri-mobile-nav .agri-nav-link {
  justify-content: flex-start;
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
}

.agri-mobile-controls {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--color-border);
}

.agri-mobile-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
}

.agri-mobile-actions .agri-header-btn {
  justify-content: center;
  width: 100%;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 1024px) {
  .agri-header-search {
    min-width: 200px;
  }
  
  .agri-user-info {
    display: none;
  }
}

@media (max-width: 768px) {
  .agri-header-content {
    padding: 0 var(--spacing-md);
  }
  
  .agri-header-nav,
  .agri-header-search,
  .agri-language-selector {
    display: none;
  }
  
  .agri-mobile-menu-toggle {
    display: flex;
  }
  
  .agri-header-right {
    gap: var(--spacing-sm);
  }
  
  .agri-header-title {
    font-size: var(--font-size-lg);
  }
}

@media (max-width: 480px) {
  .agri-header-content {
    padding: 0 var(--spacing-sm);
  }
  
  .agri-header-logo {
    height: 40px;
  }
  
  .agri-header-subtitle {
    display: none;
  }
  
  .agri-theme-toggle,
  .agri-notifications {
    display: none;
  }
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  .agri-header,
  .agri-header-logo:hover,
  .agri-nav-link,
  .agri-theme-toggle:hover,
  .agri-header-btn:hover {
    transition: none;
    transform: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .agri-header {
    background: var(--color-background-primary);
    border-bottom: 2px solid var(--color-text-primary);
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
  }
  
  .agri-nav-link.active,
  .agri-header-btn-primary {
    background: var(--color-text-primary);
    color: var(--color-background-primary);
    border-color: var(--color-text-primary);
  }
  
  .agri-search-input,
  .agri-theme-toggle {
    border: 2px solid var(--color-text-primary);
  }
}
