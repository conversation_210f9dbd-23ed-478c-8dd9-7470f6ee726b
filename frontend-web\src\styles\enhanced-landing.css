/* Enhanced Landing Page Styles - External CSS */

/* Container and Layout */
.enhanced-landing-container {
  position: relative;
  min-height: 100vh;
  overflow-x: hidden;
}

/* Dynamic Background */
.dynamic-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(135deg, rgba(46, 125, 50, 0.85), rgba(76, 175, 80, 0.7)), var(--bg-image);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  transition: background-image 1.5s ease-in-out;
  z-index: -1;
}

/* Header Styles */
.enhanced-landing-header {
  background: rgba(46, 125, 50, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.enhanced-landing-title {
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 2rem;
  font-weight: bold;
  margin: 0;
}

.enhanced-beta-badge {
  background: #FFD700;
  color: #2E7D32;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.6em;
  font-weight: bold;
}

.enhanced-nav-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
}

.enhanced-btn-beta {
  background: #FFD700;
  color: #2E7D32;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.enhanced-btn-beta:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

.enhanced-btn-live {
  background: transparent;
  color: white;
  border: 2px solid white;
  padding: 10px 24px;
  border-radius: 25px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.enhanced-btn-live:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

/* Main Content */
.enhanced-landing-main {
  padding: 80px 20px 40px;
  text-align: center;
  position: relative;
  z-index: 1;
}

.enhanced-hero-title {
  font-size: clamp(2.5rem, 5vw, 4.5rem);
  font-weight: bold;
  color: white;
  text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.7);
  margin-bottom: 20px;
  line-height: 1.2;
}

.enhanced-hero-subtitle {
  font-size: clamp(1.2rem, 3vw, 1.8rem);
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.6);
  margin-bottom: 40px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

/* Action Buttons */
.enhanced-action-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 60px;
}

.enhanced-cta-primary {
  background: linear-gradient(135deg, #FFD700, #FFC107);
  color: #2E7D32;
  border: none;
  padding: 20px 40px;
  border-radius: 50px;
  font-size: 1.3rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
  transform: scale(1);
  min-width: 250px;
}

.enhanced-cta-primary:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 35px rgba(255, 215, 0, 0.6);
}

.enhanced-cta-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 3px solid white;
  padding: 17px 40px;
  border-radius: 50px;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  min-width: 200px;
}

.enhanced-cta-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Features Grid */
.enhanced-features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 60px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.enhanced-feature-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 40px 30px;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.enhanced-feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.enhanced-feature-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

.enhanced-feature-title {
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 15px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.enhanced-feature-description {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  line-height: 1.6;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Stats Grid */
.enhanced-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
  margin-bottom: 60px;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.enhanced-stat-item {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 30px 20px;
  text-align: center;
  transition: all 0.3s ease;
}

.enhanced-stat-item:hover {
  transform: translateY(-3px);
  background: rgba(255, 255, 255, 0.15);
}

.enhanced-stat-number {
  font-size: 3rem;
  font-weight: bold;
  color: #FFD700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  margin-bottom: 10px;
}

.enhanced-stat-label {
  color: white;
  font-size: 1.1rem;
  font-weight: 500;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Pricing Grid */
.enhanced-pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 60px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.enhanced-pricing-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 40px 30px;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
}

.enhanced-pricing-card.featured {
  border: 2px solid #FFD700;
  transform: scale(1.05);
}

.enhanced-pricing-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.enhanced-pricing-card.featured:hover {
  transform: scale(1.05) translateY(-5px);
}

.enhanced-pricing-title {
  color: white;
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 15px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.enhanced-pricing-price {
  color: #FFD700;
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.enhanced-pricing-description {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  margin-bottom: 25px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.enhanced-pricing-features {
  list-style: none;
  padding: 0;
  margin-bottom: 30px;
}

.enhanced-pricing-features li {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  margin-bottom: 10px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.enhanced-pricing-button {
  background: linear-gradient(135deg, #2E7D32, #4CAF50);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.enhanced-pricing-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(46, 125, 50, 0.4);
}

.enhanced-pricing-button.beta {
  background: linear-gradient(135deg, #FFD700, #FFC107);
  color: #2E7D32;
}

.enhanced-pricing-button.beta:hover {
  box-shadow: 0 8px 20px rgba(255, 215, 0, 0.4);
}

/* Footer */
.enhanced-landing-footer {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 40px 20px 20px;
  text-align: center;
  color: white;
}

.enhanced-footer-title {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 15px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.enhanced-footer-description {
  font-size: 1.2rem;
  margin-bottom: 25px;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.enhanced-footer-contact {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
  margin-bottom: 25px;
}

.enhanced-footer-contact span {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.enhanced-footer-copyright {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .enhanced-landing-header {
    padding: 1rem;
    flex-direction: column;
    gap: 15px;
  }
  
  .enhanced-nav-buttons {
    gap: 10px;
  }
  
  .enhanced-action-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .enhanced-cta-primary,
  .enhanced-cta-secondary {
    min-width: 280px;
  }
  
  .enhanced-features-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .enhanced-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  
  .enhanced-pricing-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .enhanced-footer-contact {
    flex-direction: column;
    gap: 15px;
  }
}
