/* Simple Beta Login Styles */

.simple-beta-login {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: Arial, sans-serif;
  color: white;
  text-align: center;
}

.simple-beta-login-container {
  max-width: 500px;
  padding: 3rem;
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.simple-beta-login-title {
  margin-bottom: 2rem;
  font-size: 2.5rem;
  font-weight: bold;
}

.simple-beta-login-welcome {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 10px;
  margin-bottom: 2rem;
}

.simple-beta-login-welcome-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.simple-beta-login-welcome h2 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
}

.simple-beta-login-welcome p {
  margin: 0;
  opacity: 0.9;
}

.simple-beta-login-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.simple-beta-login-dot {
  width: 10px;
  height: 10px;
  background: #FFD700;
  border-radius: 50%;
  animation: pulse 1s infinite;
}

.simple-beta-login-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.simple-beta-login-dot:nth-child(3) {
  animation-delay: 0.4s;
}

.simple-beta-login-button {
  padding: 1rem 2rem;
  background: #2196F3;
  color: white;
  border: none;
  border-radius: 5px;
  text-decoration: none;
  font-size: 1.1rem;
  font-weight: bold;
  display: inline-block;
  transition: all 0.3s ease;
}

.simple-beta-login-button:hover {
  background: #1976D2;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
}

.simple-beta-login-features {
  margin-top: 2rem;
  opacity: 0.8;
}

.simple-beta-login-features p {
  margin-bottom: 1rem;
  font-weight: 600;
}

.simple-beta-login-features-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.simple-beta-login-features-list li {
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 5px;
}

@keyframes pulse {
  0%, 100% { 
    opacity: 0.3; 
  }
  50% { 
    opacity: 1; 
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .simple-beta-login-container {
    margin: 1rem;
    padding: 2rem;
  }
  
  .simple-beta-login-title {
    font-size: 2rem;
  }
}
