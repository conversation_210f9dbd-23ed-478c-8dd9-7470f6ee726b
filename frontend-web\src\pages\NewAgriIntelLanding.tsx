import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Grid,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Box,
  Chip,
  Rating,
  CircularProgress,
  Avatar,
  Divider,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Tooltip,
  Paper
} from '@mui/material';
import {
  PlayArrow,
  ArrowForward,
  Star,
  Pets,
  LocalHospital,
  TrendingUp,
  Agriculture,
  Security,
  Analytics,
  Support,
  Language,
  Brightness4,
  Brightness7,
  CheckCircle,
  Verified,
  TrendingUpOutlined,
  PeopleOutline,
  BusinessCenter,
  Phone,
  Email,
  LocationOn
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';

// Import styles
import '../styles/new-agriintel-landing.css';
import '../styles/components/agriintel-branding.css';

// Types and Interfaces
interface SubscriptionPlan {
  id: string;
  name: string;
  price: string;
  period: string;
  description: string;
  theme: 'beta' | 'professional';
  popular: boolean;
  badge: string;
  badgeColor: string;
  features: string[];
  ctaText: string;
  route: string;
  limitations?: string[];
  highlights?: string[];
}

interface FeatureHighlight {
  icon: React.ReactElement;
  title: string;
  description: string;
  gradient: string;
}

interface TestimonialData {
  name: string;
  role: string;
  company: string;
  rating: number;
  comment: string;
  avatar: string;
  location: string;
}

const AgriIntelLanding: React.FC = () => {
  const navigate = useNavigate();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingPlan, setLoadingPlan] = useState<string | null>(null);
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const [isDarkMode, setIsDarkMode] = useState(false);

  // South African Languages
  const languages = [
    { code: 'en', name: 'English' },
    { code: 'af', name: 'Afrikaans' },
    { code: 'zu', name: 'isiZulu' },
    { code: 'xh', name: 'isiXhosa' },
    { code: 'st', name: 'Sesotho' },
    { code: 'tn', name: 'Setswana' },
    { code: 'ss', name: 'siSwati' },
    { code: 've', name: 'Tshivenda' },
    { code: 'ts', name: 'Xitsonga' },
    { code: 'nr', name: 'isiNdebele' },
    { code: 'nso', name: 'Sepedi' }
  ];

  // Livestock images for rotating background
  const livestockImages = [
    `${process.env.PUBLIC_URL}/images/animals/cattle-1.jpeg`,
    `${process.env.PUBLIC_URL}/images/animals/cattle-2.avif`,
    `${process.env.PUBLIC_URL}/images/modules/animals/cattle-3.jpeg`,
    `${process.env.PUBLIC_URL}/images/modules/animals/cattle-4.jpeg`,
    `${process.env.PUBLIC_URL}/images/animals/cattle-5.avif`,
    `${process.env.PUBLIC_URL}/images/animals/cattle-6.jpeg`
  ];

  // Rotate background images every 6 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prev) => (prev + 1) % livestockImages.length);
    }, 6000);
    return () => clearInterval(interval);
  }, [livestockImages.length]);

  // Two-tier subscription structure (BETA V1 and Professional V1)
  const subscriptionPlans: SubscriptionPlan[] = [
    {
      id: 'beta',
      name: 'BETA V1',
      price: 'Free',
      period: 'Exhibition Trial',
      description: 'Perfect starter kit for small-scale South African farmers with exhibition-only features',
      theme: 'beta',
      popular: false,
      badge: 'FREE TRIAL',
      badgeColor: '#FFA726', // Yellow accent
      features: [
        '🐄 Up to 50 animals (exhibition limit)',
        '📊 Basic dashboard overview',
        '💰 Simple financial tracking',
        '🍃 Basic feed management',
        '📚 Resources & government programs',
        '📧 Email support only',
        '📱 No mobile app access',
        '⏰ 30-day trial period'
      ],
      limitations: [
        'Limited to 50 animals maximum',
        'Excel-only reports',
        'No AI automation features',
        'No marketplace access'
      ],
      ctaText: 'Start Free Trial',
      route: '/login'
    },
    {
      id: 'professional',
      name: 'Professional V1',
      price: 'R699',
      period: 'per month',
      description: 'Complete livestock management solution with AI automation and marketplace connections',
      theme: 'professional',
      popular: true,
      badge: 'MOST POPULAR',
      badgeColor: '#4CAF50', // Green accent
      features: [
        '🐄 Unlimited animals',
        '🤖 AI-powered insights & automation',
        '📱 Full mobile app access',
        '🏪 Marketplace connections (Uber-style)',
        '👨‍⚕️ Veterinarian network access',
        '🚚 Supplier & auctioneer connections',
        '🔒 Security service partnerships',
        '💳 In-app payment system',
        '📊 Advanced analytics & reports',
        '☎️ Priority phone support',
        '🌍 Multi-language support'
      ],
      highlights: [
        'AI automation saves 10+ hours weekly',
        'Marketplace increases revenue by 25%',
        'Priority support with 2-hour response'
      ],
      ctaText: 'Go Professional',
      route: '/login'
    }
  ];

  // Feature highlights
  const features: FeatureHighlight[] = [
    {
      icon: <Pets className="feature-icon" />,
      title: 'Smart Animal Management',
      description: 'AI-powered tracking of health, breeding, and performance with predictive insights',
      gradient: 'linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%)'
    },
    {
      icon: <LocalHospital className="feature-icon" />,
      title: 'Health Monitoring',
      description: 'Proactive health alerts with veterinary network integration',
      gradient: 'linear-gradient(135deg, #2196F3 0%, #1565C0 100%)'
    },
    {
      icon: <TrendingUp className="feature-icon" />,
      title: 'Financial Analytics',
      description: 'Maximize profits with detailed financial tracking and market insights',
      gradient: 'linear-gradient(135deg, #FF9800 0%, #F57C00 100%)'
    },
    {
      icon: <Agriculture className="feature-icon" />,
      title: 'Feed Optimization',
      description: 'Optimize feed costs and nutrition for better growth and health',
      gradient: 'linear-gradient(135deg, #8BC34A 0%, #689F38 100%)'
    },
    {
      icon: <BusinessCenter className="feature-icon" />,
      title: 'Marketplace Integration',
      description: 'Connect with veterinarians, suppliers, and buyers through our platform',
      gradient: 'linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%)'
    },
    {
      icon: <Security className="feature-icon" />,
      title: 'Security & Compliance',
      description: 'Ensure compliance with South African agricultural regulations',
      gradient: 'linear-gradient(135deg, #F44336 0%, #D32F2F 100%)'
    }
  ];

  // Testimonials
  const testimonials: TestimonialData[] = [
    {
      name: 'Sipho Mthembu',
      role: 'Cattle Farmer',
      company: 'Mthembu Livestock Farm',
      rating: 5,
      comment: 'AgriIntel transformed my farming operation. The AI insights helped me increase my herd productivity by 30%.',
      avatar: `${process.env.PUBLIC_URL}/images/testimonials/farmer1.jpg`,
      location: 'KwaZulu-Natal'
    },
    {
      name: 'Maria van der Merwe',
      role: 'Dairy Farm Owner',
      company: 'Sunshine Dairy',
      rating: 5,
      comment: 'The marketplace feature connected me with reliable suppliers. My feed costs dropped by 20%.',
      avatar: `${process.env.PUBLIC_URL}/images/testimonials/farmer2.jpg`,
      location: 'Western Cape'
    },
    {
      name: 'Thabo Molefe',
      role: 'Mixed Farming',
      company: 'Molefe Agricultural Enterprise',
      rating: 5,
      comment: 'Professional support and AI automation saved me hours of manual work every week.',
      avatar: `${process.env.PUBLIC_URL}/images/testimonials/farmer3.jpg`,
      location: 'Gauteng'
    }
  ];

  const handlePlanSelect = async (planId: string) => {
    setLoadingPlan(planId);
    setIsLoading(true);

    await new Promise(resolve => setTimeout(resolve, 1500));

    const plan = subscriptionPlans.find(p => p.id === planId);
    if (plan) {
      navigate(plan.route);
    }

    setIsLoading(false);
    setLoadingPlan(null);
  };

  const handleLanguageChange = (event: any) => {
    setSelectedLanguage(event.target.value);
  };

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
  };

  return (
    <main className={`agriintel-landing ${isDarkMode ? 'dark-mode' : 'light-mode'}`} role="main">
      {/* Dynamic Background with Livestock Images */}
      <div className="landing-background-container">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentImageIndex}
            className="landing-background"
            style={{
              backgroundImage: `url(${livestockImages[currentImageIndex]})`
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 1.5 }}
          />
        </AnimatePresence>
        <div className="background-overlay" />
      </div>

      {/* Navigation Header */}
      <motion.nav
        className="landing-navigation"
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        role="navigation"
        aria-label="Main navigation"
      >
        <Container maxWidth="xl">
          <Box className="nav-container">
            <div className="nav-brand">
              <div className="agriintel-logo-container">
                <div className="logo-wrapper">
                  <img
                    src={`${process.env.PUBLIC_URL}/images/logo/AgriIntel Logo with Bright Accents and Livestock.png`}
                    alt="AgriIntel - Smart Livestock Management"
                    className="agriintel-logo-image"
                  />
                  <Typography variant="caption" className="logo-tagline">
                    Smart Livestock Management Platform
                  </Typography>
                </div>
              </div>
              <Chip
                label="Production Ready"
                className="nav-badge"
                size="small"
              />
            </div>

            <div className="nav-controls">
              {/* Language Selector */}
              <FormControl size="small" className="language-selector">
                <Select
                  value={selectedLanguage}
                  onChange={handleLanguageChange}
                  startAdornment={<Language />}
                >
                  {languages.map((lang) => (
                    <MenuItem key={lang.code} value={lang.code}>
                      {lang.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              {/* Theme Toggle */}
              <Tooltip title={isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}>
                <IconButton
                  onClick={toggleTheme}
                  className="theme-toggle"
                  aria-label={isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
                  type="button"
                >
                  {isDarkMode ? <Brightness7 /> : <Brightness4 />}
                </IconButton>
              </Tooltip>
            </div>

            <div className="nav-actions">
              <Button
                variant="outlined"
                className="nav-button nav-button-outline"
                onClick={() => navigate('/login')}
                type="button"
                aria-label="Sign in to your AgriIntel account"
              >
                Sign In
              </Button>
              <Button
                variant="contained"
                className="nav-button nav-button-primary"
                onClick={() => handlePlanSelect('beta')}
                disabled={isLoading}
                startIcon={isLoading ? <CircularProgress size={16} /> : <PlayArrow />}
                type="button"
                aria-label="Start your free trial with AgriIntel BETA"
              >
                Start Free Trial
              </Button>
            </div>
          </Box>
        </Container>
      </motion.nav>

      {/* Hero Section */}
      <section className="hero-section" aria-labelledby="hero-title">
        <Container maxWidth="xl">
          <Grid container spacing={6} alignItems="center">
            <Grid item xs={12} lg={6}>
              <motion.div
                className="hero-content"
                initial={{ opacity: 0, x: -60 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 1, ease: "easeOut" }}
              >
                <Typography variant="h1" className="hero-title" id="hero-title">
                  Transform Your
                  <span className="gradient-text"> Livestock Management</span>
                  <br />
                  with AI-Powered Intelligence
                </Typography>

                <Typography variant="h5" className="hero-subtitle">
                  Join 2,500+ South African farmers using AgriIntel to increase productivity,
                  reduce costs, and connect with industry professionals through our
                  comprehensive livestock management platform.
                </Typography>

                <div className="hero-stats">
                  <div className="stat-item">
                    <Typography variant="h3" className="stat-number">30%</Typography>
                    <Typography variant="body2" className="stat-label">Productivity Increase</Typography>
                  </div>
                  <div className="stat-item">
                    <Typography variant="h3" className="stat-number">R2.5M</Typography>
                    <Typography variant="body2" className="stat-label">Revenue Generated</Typography>
                  </div>
                  <div className="stat-item">
                    <Typography variant="h3" className="stat-number">2,500+</Typography>
                    <Typography variant="body2" className="stat-label">Active Farmers</Typography>
                  </div>
                </div>

                <div className="hero-actions">
                  <Button
                    variant="contained"
                    size="large"
                    className="cta-primary"
                    onClick={() => handlePlanSelect('beta')}
                    disabled={isLoading}
                    startIcon={loadingPlan === 'beta' ? <CircularProgress size={20} /> : <PlayArrow />}
                    type="button"
                    aria-label="Start your free 30-day trial with AgriIntel BETA"
                  >
                    {loadingPlan === 'beta' ? 'Loading...' : 'Start Free Trial'}
                  </Button>
                  <Button
                    variant="outlined"
                    size="large"
                    className="cta-secondary"
                    onClick={() => handlePlanSelect('professional')}
                    disabled={isLoading}
                    endIcon={loadingPlan === 'professional' ? <CircularProgress size={20} /> : <ArrowForward />}
                    type="button"
                    aria-label="Upgrade to AgriIntel Professional for R699 per month"
                  >
                    {loadingPlan === 'professional' ? 'Loading...' : 'Go Professional'}
                  </Button>
                </div>

                <div className="hero-rating">
                  <Rating value={5} readOnly size="large" />
                  <Typography variant="h6">
                    4.9/5 from 2,500+ verified farmers
                  </Typography>
                </div>
              </motion.div>
            </Grid>

            <Grid item xs={12} lg={6}>
              <motion.div
                className="hero-visual"
                initial={{ opacity: 0, x: 60 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 1, ease: "easeOut", delay: 0.3 }}
              >
                <div className="hero-dashboard-preview">
                  <img
                    src={`${process.env.PUBLIC_URL}/images/dashboard/main-dashboard.jpg`}
                    alt="AgriIntel Dashboard Preview"
                    className="hero-dashboard-image"
                  />
                  <div className="dashboard-overlay">
                    <div className="overlay-badge">
                      <Star />
                      <span>Live Dashboard</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            </Grid>
          </Grid>
        </Container>
      </section>

      {/* Features Section */}
      <section className="features-section">
        <Container maxWidth="xl">
          <motion.div
            className="section-header"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Typography variant="h2" className="section-title">
              Comprehensive Livestock Management Features
            </Typography>
            <Typography variant="h6" className="section-subtitle">
              Everything you need to manage your livestock operation efficiently
            </Typography>
          </motion.div>

          <Grid container spacing={4}>
            {features.map((feature, index) => (
              <Grid item xs={12} md={6} lg={4} key={index}>
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card className="feature-card">
                    <CardContent>
                      <div className="feature-icon-container">
                        {feature.icon}
                      </div>
                      <Typography variant="h5" className="feature-title">
                        {feature.title}
                      </Typography>
                      <Typography variant="body1" className="feature-description">
                        {feature.description}
                      </Typography>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </Container>
      </section>

      {/* Subscription Plans Section */}
      <section className="pricing-section">
        <Container maxWidth="xl">
          <motion.div
            className="section-header"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Typography variant="h2" className="section-title">
              Choose Your AgriIntel Plan
            </Typography>
            <Typography variant="h6" className="section-subtitle">
              Two-tier system designed for South African farmers
            </Typography>
          </motion.div>

          <Grid container spacing={4} justifyContent="center">
            {subscriptionPlans.map((plan, index) => (
              <Grid item xs={12} md={6} key={plan.id}>
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  viewport={{ once: true }}
                >
                  <Card
                    className={`pricing-card pricing-card-${plan.theme} ${plan.popular ? 'popular' : ''}`}
                    onClick={() => handlePlanSelect(plan.id)}
                    role="button"
                    tabIndex={0}
                    aria-label={`Select ${plan.name} plan for ${plan.price} ${plan.period}`}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handlePlanSelect(plan.id);
                      }
                    }}
                  >
                    <div className="plan-badge-container">
                      <Chip
                        label={plan.badge}
                        className="plan-badge"
                        sx={{
                          backgroundColor: plan.badgeColor,
                          color: 'white',
                          fontWeight: 'bold'
                        }}
                      />
                    </div>

                    <CardContent className="pricing-content">
                      <Typography variant="h4" className="plan-name">
                        {plan.name}
                      </Typography>
                      <Typography variant="body1" className="plan-description">
                        {plan.description}
                      </Typography>

                      <div className="plan-pricing">
                        <Typography variant="h2" className="plan-price">
                          {plan.price}
                        </Typography>
                        <Typography variant="body2" className="plan-period">
                          {plan.period}
                        </Typography>
                      </div>

                      <div className="plan-features">
                        {plan.features.map((feature, idx) => (
                          <div key={idx} className="feature-item">
                            <CheckCircle className="feature-check" />
                            <Typography variant="body2">{feature}</Typography>
                          </div>
                        ))}
                      </div>

                      {plan.limitations && (
                        <div className="plan-limitations">
                          <Typography variant="subtitle2" className="limitations-title">
                            Limitations:
                          </Typography>
                          {plan.limitations.map((limitation, idx) => (
                            <Typography key={idx} variant="caption" className="limitation-item">
                              • {limitation}
                            </Typography>
                          ))}
                        </div>
                      )}

                      {plan.highlights && (
                        <div className="plan-highlights">
                          {plan.highlights.map((highlight, idx) => (
                            <div key={idx} className="highlight-item">
                              <Verified className="highlight-icon" />
                              <Typography variant="caption">{highlight}</Typography>
                            </div>
                          ))}
                        </div>
                      )}

                      <Button
                        variant="contained"
                        size="large"
                        className={`plan-cta plan-cta-${plan.theme}`}
                        fullWidth
                        disabled={loadingPlan === plan.id}
                        startIcon={loadingPlan === plan.id ? <CircularProgress size={20} /> : null}
                        type="button"
                        aria-label={`${plan.ctaText} - ${plan.name} plan`}
                      >
                        {loadingPlan === plan.id ? 'Loading...' : plan.ctaText}
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </Container>
      </section>

      {/* Testimonials Section */}
      <section className="testimonials-section">
        <Container maxWidth="xl">
          <motion.div
            className="section-header"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Typography variant="h2" className="section-title">
              Trusted by South African Farmers
            </Typography>
            <Typography variant="h6" className="section-subtitle">
              Real stories from farmers who transformed their operations with AgriIntel
            </Typography>
          </motion.div>

          <Grid container spacing={4}>
            {testimonials.map((testimonial, index) => (
              <Grid item xs={12} md={4} key={index}>
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card className="testimonial-card">
                    <CardContent>
                      <div className="testimonial-header">
                        <Avatar
                          src={testimonial.avatar}
                          alt={testimonial.name}
                          className="testimonial-avatar"
                        />
                        <div className="testimonial-info">
                          <Typography variant="h6" className="testimonial-name">
                            {testimonial.name}
                          </Typography>
                          <Typography variant="body2" className="testimonial-role">
                            {testimonial.role}
                          </Typography>
                          <Typography variant="caption" className="testimonial-company">
                            {testimonial.company}
                          </Typography>
                          <div className="testimonial-location">
                            <LocationOn fontSize="small" />
                            <Typography variant="caption">{testimonial.location}</Typography>
                          </div>
                        </div>
                      </div>

                      <Rating value={testimonial.rating} readOnly size="small" />

                      <Typography variant="body1" className="testimonial-comment">
                        "{testimonial.comment}"
                      </Typography>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </Container>
      </section>

      {/* About Section */}
      <section className="about-section">
        <Container maxWidth="xl">
          <Grid container spacing={6} alignItems="center">
            <Grid item xs={12} lg={6}>
              <motion.div
                className="about-content"
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <Typography variant="h2" className="about-title">
                  About AgriIntel
                </Typography>
                <Typography variant="h5" className="about-subtitle">
                  Empowering South African Agriculture Through Technology
                </Typography>

                <Typography variant="body1" className="about-description">
                  Founded by <strong>May and Caiphus</strong>, visionary leaders dedicated to solving
                  critical challenges in global agriculture. Our mission is to introduce modern technological
                  practices to the agricultural sector, empowering farmers, veterinarians, and industry partners
                  with the tools they need to thrive in a changing world.
                </Typography>

                <Typography variant="body1" className="about-description">
                  We believe in leveraging data for precision agriculture, enhancing food security through
                  technology, optimizing supply chains with predictive analytics, and promoting sustainable
                  farming practices for a better future.
                </Typography>

                <div className="about-features">
                  <div className="about-feature">
                    <TrendingUpOutlined className="about-feature-icon" />
                    <div>
                      <Typography variant="h6">AI-Powered Insights</Typography>
                      <Typography variant="body2">Advanced algorithms for predictive analytics</Typography>
                    </div>
                  </div>
                  <div className="about-feature">
                    <PeopleOutline className="about-feature-icon" />
                    <div>
                      <Typography variant="h6">Community Network</Typography>
                      <Typography variant="body2">Connect with farmers, vets, and suppliers</Typography>
                    </div>
                  </div>
                  <div className="about-feature">
                    <Security className="about-feature-icon" />
                    <div>
                      <Typography variant="h6">Secure & Compliant</Typography>
                      <Typography variant="body2">Meets South African agricultural standards</Typography>
                    </div>
                  </div>
                </div>
              </motion.div>
            </Grid>

            <Grid item xs={12} lg={6}>
              <motion.div
                className="about-visual"
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <div className="about-image-container">
                  <img
                    src={`${process.env.PUBLIC_URL}/images/gallery/cattle-farm-1.jpg`}
                    alt="Modern South African Farm"
                    className="about-image"
                  />
                  <div className="about-overlay">
                    <div className="overlay-content">
                      <Typography variant="h4">2,500+</Typography>
                      <Typography variant="body2">Farmers Trust AgriIntel</Typography>
                    </div>
                  </div>
                </div>
              </motion.div>
            </Grid>
          </Grid>
        </Container>
      </section>

      {/* Sponsors Section */}
      <section className="sponsors-section">
        <Container maxWidth="xl">
          <motion.div
            className="section-header"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Typography variant="h4" className="sponsors-title">
              Supported by Leading Agricultural Organizations
            </Typography>
          </motion.div>

          <div className="sponsors-grid">
            <motion.div
              className="sponsor-item"
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <img
                src={`${process.env.PUBLIC_URL}/images/sponsors/Agricultural research council.png`}
                alt="Agricultural Research Council"
                className="sponsor-logo"
              />
            </motion.div>
            <motion.div
              className="sponsor-item"
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <img
                src={`${process.env.PUBLIC_URL}/images/sponsors/Agriculture , land reform & rural development.png`}
                alt="Department of Agriculture, Land Reform and Rural Development"
                className="sponsor-logo"
              />
            </motion.div>
            <motion.div
              className="sponsor-item"
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <img
                src={`${process.env.PUBLIC_URL}/images/sponsors/National Agricultural Marketing Council.png`}
                alt="National Agricultural Marketing Council"
                className="sponsor-logo"
              />
            </motion.div>
            <motion.div
              className="sponsor-item"
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <img
                src={`${process.env.PUBLIC_URL}/images/sponsors/land bank.png`}
                alt="Land Bank"
                className="sponsor-logo"
              />
            </motion.div>
          </div>
        </Container>
      </section>

      {/* Footer */}
      <footer className="landing-footer">
        <Container maxWidth="xl">
          <Grid container spacing={6}>
            <Grid item xs={12} md={4}>
              <div className="footer-brand">
                <img
                  src={`${process.env.PUBLIC_URL}/images/logo/AgriIntel Logo with Bright Accents and Livestock.png`}
                  alt="AgriIntel"
                  className="footer-logo"
                />
                <Typography variant="body1" className="footer-description">
                  Transforming South African agriculture through intelligent livestock management solutions.
                </Typography>
                <div className="footer-contact">
                  <div className="contact-item">
                    <Email fontSize="small" />
                    <Typography variant="body2"><EMAIL></Typography>
                  </div>
                  <div className="contact-item">
                    <Phone fontSize="small" />
                    <Typography variant="body2">+27 11 123 4567</Typography>
                  </div>
                  <div className="contact-item">
                    <LocationOn fontSize="small" />
                    <Typography variant="body2">Johannesburg, South Africa</Typography>
                  </div>
                </div>
              </div>
            </Grid>

            <Grid item xs={12} md={2}>
              <div className="footer-section">
                <Typography variant="h6" className="footer-title">
                  Platform
                </Typography>
                <ul className="footer-links">
                  <li><a href="#features">Features</a></li>
                  <li><a href="#pricing">Pricing</a></li>
                  <li><a href="#testimonials">Testimonials</a></li>
                  <li><a href="#about">About</a></li>
                </ul>
              </div>
            </Grid>

            <Grid item xs={12} md={2}>
              <div className="footer-section">
                <Typography variant="h6" className="footer-title">
                  Support
                </Typography>
                <ul className="footer-links">
                  <li><a href="/help">Help Center</a></li>
                  <li><a href="/contact">Contact Us</a></li>
                  <li><a href="/documentation">Documentation</a></li>
                  <li><a href="/training">Training</a></li>
                </ul>
              </div>
            </Grid>

            <Grid item xs={12} md={2}>
              <div className="footer-section">
                <Typography variant="h6" className="footer-title">
                  Legal
                </Typography>
                <ul className="footer-links">
                  <li><a href="/privacy">Privacy Policy</a></li>
                  <li><a href="/terms">Terms of Service</a></li>
                  <li><a href="/compliance">Compliance</a></li>
                  <li><a href="/security">Security</a></li>
                </ul>
              </div>
            </Grid>

            <Grid item xs={12} md={2}>
              <div className="footer-section">
                <Typography variant="h6" className="footer-title">
                  Connect
                </Typography>
                <div className="footer-cta">
                  <Button
                    variant="contained"
                    size="small"
                    className="footer-cta-button"
                    onClick={() => handlePlanSelect('beta')}
                    startIcon={<PlayArrow />}
                    type="button"
                    aria-label="Start your free trial from footer"
                  >
                    Start Free Trial
                  </Button>
                </div>
              </div>
            </Grid>
          </Grid>

          <Divider className="footer-divider" />

          <div className="footer-bottom">
            <Typography variant="body2" className="footer-copyright">
              © 2024 AgriIntel. All rights reserved. Proudly South African.
            </Typography>
            <div className="footer-badges">
              <Chip
                label="ISO 27001 Certified"
                size="small"
                className="footer-badge"
              />
              <Chip
                label="GDPR Compliant"
                size="small"
                className="footer-badge"
              />
            </div>
          </div>
        </Container>
      </footer>
    </main>
  );
};

export default AgriIntelLanding;
