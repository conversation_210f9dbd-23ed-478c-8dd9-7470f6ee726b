#!/usr/bin/env node

/**
 * Test API Connectivity Script
 * Tests the connection between frontend and backend
 */

const http = require('http');
const https = require('https');

// Test endpoints
const endpoints = [
  {
    name: 'Backend Health Check',
    url: 'http://localhost:3001/health',
    method: 'GET'
  },
  {
    name: 'Backend API Status',
    url: 'http://localhost:3001/api/status',
    method: 'GET'
  },
  {
    name: 'Backend Database Status',
    url: 'http://localhost:3001/api/db-status',
    method: 'GET'
  },
  {
    name: 'Frontend Application',
    url: 'http://localhost:3002',
    method: 'GET'
  }
];

function testEndpoint(endpoint) {
  return new Promise((resolve) => {
    const url = new URL(endpoint.url);
    const client = url.protocol === 'https:' ? https : http;
    
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: endpoint.method,
      timeout: 5000
    };

    const req = client.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          name: endpoint.name,
          url: endpoint.url,
          status: res.statusCode,
          success: res.statusCode >= 200 && res.statusCode < 300,
          response: data.length > 500 ? data.substring(0, 500) + '...' : data
        });
      });
    });

    req.on('error', (error) => {
      resolve({
        name: endpoint.name,
        url: endpoint.url,
        status: 'ERROR',
        success: false,
        error: error.message
      });
    });

    req.on('timeout', () => {
      req.destroy();
      resolve({
        name: endpoint.name,
        url: endpoint.url,
        status: 'TIMEOUT',
        success: false,
        error: 'Request timeout'
      });
    });

    req.end();
  });
}

async function runTests() {
  console.log('🚀 Testing API Connectivity...\n');
  
  const results = [];
  
  for (const endpoint of endpoints) {
    console.log(`Testing: ${endpoint.name}...`);
    const result = await testEndpoint(endpoint);
    results.push(result);
    
    if (result.success) {
      console.log(`✅ ${result.name}: OK (${result.status})`);
    } else {
      console.log(`❌ ${result.name}: FAILED (${result.status || result.error})`);
    }
    console.log('');
  }
  
  // Summary
  console.log('📊 Test Summary:');
  console.log('================');
  
  const successful = results.filter(r => r.success).length;
  const total = results.length;
  
  console.log(`✅ Successful: ${successful}/${total}`);
  console.log(`❌ Failed: ${total - successful}/${total}`);
  
  if (successful === total) {
    console.log('\n🎉 All tests passed! The application should be working correctly.');
    console.log('\n🌐 Access the application at:');
    console.log('   • Landing Page: http://localhost:3002/');
    console.log('   • Beta Login: http://localhost:3002/beta-login');
    console.log('   • Premium Login: http://localhost:3002/premium-login');
    console.log('   • Registration: http://localhost:3002/register');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the failed endpoints.');
    
    // Show detailed error information
    const failed = results.filter(r => !r.success);
    if (failed.length > 0) {
      console.log('\n🔍 Failed Endpoints Details:');
      failed.forEach(result => {
        console.log(`\n${result.name}:`);
        console.log(`  URL: ${result.url}`);
        console.log(`  Error: ${result.error || result.status}`);
        if (result.response) {
          console.log(`  Response: ${result.response}`);
        }
      });
    }
  }
  
  console.log('\n📝 Next Steps:');
  console.log('   1. Make sure both frontend and backend servers are running');
  console.log('   2. Check that MongoDB is connected and accessible');
  console.log('   3. Verify that all environment variables are properly set');
  console.log('   4. Test the login functionality with Demo/123 or admin/Admin@123');
}

// Run the tests
runTests().catch(console.error);
