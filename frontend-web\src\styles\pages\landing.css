/**
 * Landing Page Styles
 * Professional landing page for AgriIntel platform
 */

/* ===== LANDING PAGE CONTAINER ===== */

.agri-landing {
  min-height: 100vh;
  background: var(--color-background-primary);
  position: relative;
  overflow-x: hidden;
}

.agri-landing.dark-mode {
  background: var(--color-background-dark);
}

/* ===== BACKGROUND ELEMENTS ===== */

.agri-landing-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    rgba(21, 101, 192, 0.05) 0%, 
    rgba(46, 125, 50, 0.05) 50%, 
    rgba(245, 124, 0, 0.05) 100%);
  z-index: -2;
}

.agri-landing-pattern {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(21, 101, 192, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(46, 125, 50, 0.1) 0%, transparent 50%);
  z-index: -1;
}

/* ===== HERO SECTION ===== */

.agri-hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: var(--spacing-2xl) 0;
}

.agri-hero-content {
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.agri-hero-title {
  font-size: clamp(var(--font-size-4xl), 8vw, var(--font-size-6xl));
  font-weight: var(--font-weight-extrabold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-lg);
  line-height: var(--line-height-tight);
}

.agri-hero-title .gradient-text {
  background: var(--agri-pro-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.agri-hero-subtitle {
  font-size: var(--font-size-xl);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-2xl);
  line-height: var(--line-height-relaxed);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.agri-hero-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
}

.agri-hero-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-2xl);
  border: none;
  border-radius: var(--radius-xl);
  font-family: inherit;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-normal);
  white-space: nowrap;
  min-height: 56px;
}

.agri-hero-btn:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}

.agri-hero-btn-primary {
  background: var(--agri-pro-gradient);
  color: var(--color-white);
  box-shadow: 0 8px 25px rgba(46, 125, 50, 0.3);
}

.agri-hero-btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(46, 125, 50, 0.4);
}

.agri-hero-btn-secondary {
  background: transparent;
  color: var(--color-text-primary);
  border: 2px solid var(--color-border);
}

.agri-hero-btn-secondary:hover {
  background: var(--color-background-hover);
  border-color: var(--agri-primary);
  color: var(--agri-primary);
  transform: translateY(-2px);
}

/* Hero Stats */
.agri-hero-stats {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-2xl);
}

.agri-hero-stat {
  text-align: center;
}

.agri-hero-stat-value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--agri-primary);
  margin-bottom: var(--spacing-xs);
  display: block;
}

.agri-hero-stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

/* ===== SECTION STYLES ===== */

.agri-section {
  padding: var(--spacing-4xl) 0;
  position: relative;
}

.agri-section-header {
  text-align: center;
  max-width: 800px;
  margin: 0 auto var(--spacing-2xl);
}

.agri-section-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
  line-height: var(--line-height-tight);
}

.agri-section-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
}

/* ===== FEATURES SECTION ===== */

.agri-features {
  background: var(--color-background-secondary);
}

.agri-features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-2xl);
}

.agri-feature-card {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  text-align: center;
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.agri-feature-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-lg);
  border-color: var(--agri-primary);
}

.agri-feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--agri-pro-gradient);
  opacity: 0;
  transition: var(--transition-normal);
}

.agri-feature-card:hover::before {
  opacity: 1;
}

.agri-feature-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--spacing-lg);
  background: var(--agri-pro-gradient);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  font-size: var(--font-size-2xl);
}

.agri-feature-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
}

.agri-feature-description {
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
}

/* ===== PRICING SECTION ===== */

.agri-pricing {
  background: var(--color-background-primary);
}

.agri-pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-2xl);
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.agri-pricing-card {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-2xl);
  text-align: center;
  position: relative;
  transition: var(--transition-normal);
}

.agri-pricing-card.featured {
  border-color: var(--agri-primary);
  transform: scale(1.05);
  z-index: 1;
}

.agri-pricing-card.featured::before {
  content: 'Most Popular';
  position: absolute;
  top: var(--spacing-lg);
  left: 50%;
  transform: translateX(-50%);
  background: var(--agri-accent);
  color: var(--color-white);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
}

.agri-pricing-plan {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-sm);
}

.agri-pricing-price {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--agri-primary);
  margin-bottom: var(--spacing-xs);
}

.agri-pricing-period {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xl);
}

.agri-pricing-features {
  list-style: none;
  padding: 0;
  margin: 0 0 var(--spacing-xl) 0;
}

.agri-pricing-features li {
  padding: var(--spacing-sm) 0;
  color: var(--color-text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

.agri-pricing-features li::before {
  content: '✓';
  color: var(--color-success);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
}

.agri-pricing-btn {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  border-radius: var(--radius-lg);
  font-family: inherit;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: var(--transition-normal);
}

.agri-pricing-btn-primary {
  background: var(--agri-pro-gradient);
  color: var(--color-white);
}

.agri-pricing-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(46, 125, 50, 0.3);
}

.agri-pricing-btn-secondary {
  background: transparent;
  color: var(--agri-primary);
  border: 2px solid var(--agri-primary);
}

.agri-pricing-btn-secondary:hover {
  background: var(--agri-primary);
  color: var(--color-white);
}

/* ===== TESTIMONIALS SECTION ===== */

.agri-testimonials {
  background: var(--color-background-secondary);
}

.agri-testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-2xl);
}

.agri-testimonial-card {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  transition: var(--transition-normal);
}

.agri-testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.agri-testimonial-quote {
  font-size: var(--font-size-lg);
  color: var(--color-text-primary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-lg);
  font-style: italic;
}

.agri-testimonial-author {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.agri-testimonial-avatar {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-full);
  background: var(--agri-primary);
  color: var(--color-white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
}

.agri-testimonial-info {
  display: flex;
  flex-direction: column;
}

.agri-testimonial-name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

.agri-testimonial-role {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
  .agri-hero-content {
    padding: 0 var(--spacing-md);
  }
  
  .agri-hero-actions {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .agri-hero-btn {
    width: 100%;
    max-width: 300px;
  }
  
  .agri-hero-stats {
    flex-direction: column;
    gap: var(--spacing-lg);
  }
  
  .agri-features-grid,
  .agri-pricing-grid,
  .agri-testimonials-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .agri-pricing-card.featured {
    transform: none;
  }
  
  .agri-section {
    padding: var(--spacing-2xl) 0;
  }
}

@media (max-width: 480px) {
  .agri-hero-title {
    font-size: var(--font-size-3xl);
  }
  
  .agri-hero-subtitle {
    font-size: var(--font-size-lg);
  }
  
  .agri-section-title {
    font-size: var(--font-size-3xl);
  }
  
  .agri-feature-card,
  .agri-pricing-card,
  .agri-testimonial-card {
    padding: var(--spacing-lg);
  }
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  .agri-feature-card:hover,
  .agri-pricing-card,
  .agri-testimonial-card:hover,
  .agri-hero-btn:hover {
    transform: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .agri-feature-card,
  .agri-pricing-card,
  .agri-testimonial-card {
    background: var(--color-background-primary);
    border: 2px solid var(--color-text-primary);
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
  }
  
  .agri-hero-title .gradient-text {
    background: none;
    -webkit-background-clip: unset;
    -webkit-text-fill-color: unset;
    background-clip: unset;
    color: var(--color-text-primary);
  }
}
