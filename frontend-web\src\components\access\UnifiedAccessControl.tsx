import React from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ack,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Alert,
  useTheme,
  alpha
} from '@mui/material';
import {
  Lock,
  Upgrade,
  CheckCircle,
  Star,
  ArrowForward,
  Security,
  TrendingUp
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import {
  canAccessModule,
  isBetaUser,
  isProfessionalUser,
  getModuleLimitations,
  getUpgradeMessage,
  moduleConfigurations
} from '../../utils/unifiedAccessControl';

interface UnifiedAccessControlProps {
  moduleId: string;
  children: React.ReactNode;
  showUpgradePrompt?: boolean;
  fallbackComponent?: React.ReactNode;
}

const UnifiedAccessControl: React.FC<UnifiedAccessControlProps> = ({
  moduleId,
  children,
  showUpgradePrompt = true,
  fallbackComponent
}) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const theme = useTheme();

  // Check if user has access to the module
  const hasAccess = canAccessModule(user, moduleId);
  const isUserBeta = isBetaUser(user);
  const isUserProfessional = isProfessionalUser(user);
  const moduleConfig = moduleConfigurations[moduleId];
  const limitations = getModuleLimitations(user, moduleId);
  const upgradeMessage = getUpgradeMessage(moduleId);

  // If user has access, render children
  if (hasAccess) {
    return <>{children}</>;
  }

  // If no upgrade prompt should be shown, render fallback or nothing
  if (!showUpgradePrompt) {
    return fallbackComponent ? <>{fallbackComponent}</> : null;
  }

  // If module doesn't exist, show error
  if (!moduleConfig) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        <Typography variant="body1" fontWeight="bold">
          Module Not Found
        </Typography>
        <Typography variant="body2">
          The requested module "{moduleId}" could not be found.
        </Typography>
      </Alert>
    );
  }

  // Show upgrade prompt for locked modules
  return (
    <Box sx={{ p: 3, minHeight: '60vh', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        style={{ width: '100%', maxWidth: 600 }}
      >
        <Card
          sx={{
            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.background.paper, 0.95)})`,
            backdropFilter: 'blur(20px)',
            border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
            borderRadius: 3,
            overflow: 'hidden'
          }}
        >
          <CardContent sx={{ p: 4 }}>
            {/* Header */}
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <Box
                sx={{
                  width: 80,
                  height: 80,
                  borderRadius: '50%',
                  background: `linear-gradient(135deg, ${theme.palette.warning.main}, ${theme.palette.warning.dark})`,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mx: 'auto',
                  mb: 2
                }}
              >
                <Lock sx={{ fontSize: 40, color: 'white' }} />
              </Box>
              
              <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
                {moduleConfig.name} Locked
              </Typography>
              
              <Typography variant="body1" sx={{ opacity: 0.8, mb: 2 }}>
                {moduleConfig.description}
              </Typography>

              {isUserBeta && (
                <Chip
                  label="BETA V1 USER"
                  sx={{
                    background: 'linear-gradient(135deg, #F57C00, #FFB74D)',
                    color: 'white',
                    fontWeight: 600
                  }}
                />
              )}
            </Box>

            {/* Current Plan Features */}
            {isUserBeta && (
              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center' }}>
                  <CheckCircle sx={{ color: 'success.main', mr: 1 }} />
                  Your BETA V1 Includes:
                </Typography>
                <List dense>
                  {[
                    'Dashboard Overview',
                    'Animal Management (50 max)',
                    'Basic Health Monitoring',
                    'Simple Feed Management',
                    'Basic Financial Tracking',
                    'Excel-only Reports',
                    'Resource Library Access'
                  ].map((feature, index) => (
                    <ListItem key={index} sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <CheckCircle sx={{ fontSize: 20, color: 'success.main' }} />
                      </ListItemIcon>
                      <ListItemText
                        primary={feature}
                        sx={{ '& .MuiListItemText-primary': { fontSize: '0.95rem' } }}
                      />
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}

            {/* Upgrade Message */}
            <Alert
              severity="info"
              sx={{
                mb: 3,
                background: `linear-gradient(135deg, ${alpha(theme.palette.info.main, 0.1)}, ${alpha(theme.palette.info.main, 0.05)})`,
                border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
              }}
            >
              <Typography variant="body1" sx={{ fontWeight: 600 }}>
                {upgradeMessage}
              </Typography>
            </Alert>

            {/* Professional Features */}
            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center' }}>
                <Star sx={{ color: 'warning.main', mr: 1 }} />
                Professional V1 Features:
              </Typography>
              <List dense>
                {moduleConfig.features?.map((feature, index) => (
                  <ListItem key={index} sx={{ py: 0.5 }}>
                    <ListItemIcon sx={{ minWidth: 32 }}>
                      <ArrowForward sx={{ fontSize: 20, color: 'primary.main' }} />
                    </ListItemIcon>
                    <ListItemText
                      primary={feature.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      sx={{ '& .MuiListItemText-primary': { fontSize: '0.95rem' } }}
                    />
                  </ListItem>
                )) || []}
              </List>
            </Box>

            {/* Benefits */}
            <Box sx={{ mb: 4, p: 3, background: alpha(theme.palette.success.main, 0.1), borderRadius: 2 }}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, color: 'success.main' }}>
                🚀 Professional Benefits:
              </Typography>
              <Stack spacing={1}>
                <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                  <TrendingUp sx={{ fontSize: 18, mr: 1, color: 'success.main' }} />
                  Increase revenue by 25% with marketplace connections
                </Typography>
                <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                  <Security sx={{ fontSize: 18, mr: 1, color: 'success.main' }} />
                  AI automation saves 10+ hours weekly
                </Typography>
                <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                  <CheckCircle sx={{ fontSize: 18, mr: 1, color: 'success.main' }} />
                  Priority support with 2-hour response time
                </Typography>
              </Stack>
            </Box>

            {/* Action Buttons */}
            <Stack spacing={2}>
              <Button
                variant="contained"
                size="large"
                startIcon={<Upgrade />}
                onClick={() => navigate('/login-pro')}
                sx={{
                  background: 'linear-gradient(135deg, #2E7D32, #66BB6A)',
                  color: 'white',
                  fontWeight: 600,
                  py: 1.5,
                  fontSize: '1.1rem',
                  textTransform: 'none',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #1B5E20, #2E7D32)',
                    transform: 'translateY(-2px)',
                    boxShadow: '0 8px 25px rgba(46, 125, 50, 0.3)'
                  }
                }}
              >
                Upgrade to Professional V1 - R699/month
              </Button>

              <Button
                variant="outlined"
                onClick={() => navigate('/dashboard')}
                sx={{
                  borderColor: alpha(theme.palette.text.primary, 0.3),
                  color: theme.palette.text.primary,
                  fontWeight: 600,
                  textTransform: 'none'
                }}
              >
                Back to Dashboard
              </Button>
            </Stack>
          </CardContent>
        </Card>
      </motion.div>
    </Box>
  );
};

export default UnifiedAccessControl;
