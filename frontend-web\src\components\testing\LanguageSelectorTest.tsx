import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Al<PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Grid,
  useTheme,
  alpha
} from '@mui/material';
import {
  Language,
  Translate,
  CheckCircle,
  Error,
  Warning,
  Info
} from '@mui/icons-material';
import LanguageSelector from '../LanguageSelector';
import EnhancedLanguageSelector from '../language/EnhancedLanguageSelector';
import { useLanguage } from '../../contexts/LanguageContext';
import { southAfricanLanguages } from '../../locales/southAfricanLanguages';

interface TestResult {
  test: string;
  status: 'pass' | 'fail' | 'warning' | 'info';
  message: string;
  timestamp: Date;
}

const LanguageSelectorTest: React.FC = () => {
  const theme = useTheme();
  const { language, setLanguage, translate } = useLanguage();
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);

  const addTestResult = (test: string, status: TestResult['status'], message: string) => {
    setTestResults(prev => [...prev, {
      test,
      status,
      message,
      timestamp: new Date()
    }]);
  };

  const runLanguageTests = async () => {
    setIsRunningTests(true);
    setTestResults([]);

    // Test 1: Language Context
    try {
      addTestResult('Language Context', 'pass', `Current language: ${language}`);
    } catch (error) {
      addTestResult('Language Context', 'fail', 'Language context not available');
    }

    // Test 2: Translation Function
    try {
      const testTranslation = translate('common.loading', { fallback: 'Loading...' });
      addTestResult('Translation Function', 'pass', `Translation test: "${testTranslation}"`);
    } catch (error) {
      addTestResult('Translation Function', 'fail', 'Translation function failed');
    }

    // Test 3: South African Languages
    const enabledLanguages = southAfricanLanguages.filter(lang => lang.enabled);
    addTestResult('SA Languages', 'pass', `${enabledLanguages.length} South African languages enabled`);

    // Test 4: Language Completeness
    const completeLanguages = southAfricanLanguages.filter(lang => lang.completeness >= 90);
    const incompleteLanguages = southAfricanLanguages.filter(lang => lang.completeness < 75);
    
    addTestResult('Language Completeness', 'info', 
      `${completeLanguages.length} languages >90% complete, ${incompleteLanguages.length} languages <75% complete`);

    // Test 5: Language Switching
    const originalLanguage = language;
    try {
      setLanguage('af'); // Switch to Afrikaans
      await new Promise(resolve => setTimeout(resolve, 100));
      setLanguage(originalLanguage); // Switch back
      addTestResult('Language Switching', 'pass', 'Language switching works correctly');
    } catch (error) {
      addTestResult('Language Switching', 'fail', 'Language switching failed');
    }

    // Test 6: Language Persistence
    const storedLanguage = localStorage.getItem('language');
    if (storedLanguage) {
      addTestResult('Language Persistence', 'pass', `Language persisted: ${storedLanguage}`);
    } else {
      addTestResult('Language Persistence', 'warning', 'No language stored in localStorage');
    }

    // Test 7: RTL Support
    const rtlLanguages = southAfricanLanguages.filter(lang => lang.rtl);
    addTestResult('RTL Support', 'info', `${rtlLanguages.length} RTL languages configured`);

    setIsRunningTests(false);
  };

  const testLanguageSwitch = (langCode: string) => {
    const oldLanguage = language;
    setLanguage(langCode);
    addTestResult('Manual Language Switch', 'pass', 
      `Switched from ${oldLanguage} to ${langCode}`);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return <CheckCircle sx={{ color: theme.palette.success.main }} />;
      case 'fail':
        return <Error sx={{ color: theme.palette.error.main }} />;
      case 'warning':
        return <Warning sx={{ color: theme.palette.warning.main }} />;
      case 'info':
        return <Info sx={{ color: theme.palette.info.main }} />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return theme.palette.success.main;
      case 'fail':
        return theme.palette.error.main;
      case 'warning':
        return theme.palette.warning.main;
      case 'info':
        return theme.palette.info.main;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Card
        sx={{
          background: alpha(theme.palette.background.paper, 0.9),
          backdropFilter: 'blur(20px)',
          border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
        }}
      >
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
            <Language sx={{ fontSize: 40, color: theme.palette.primary.main }} />
            <Box>
              <Typography variant="h4" fontWeight="bold">
                Language Selector Testing
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                Test language switching and translation functionality
              </Typography>
            </Box>
          </Box>

          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                Standard Language Selector
              </Typography>
              <LanguageSelector variant="premium" />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                Enhanced Language Selector
              </Typography>
              <EnhancedLanguageSelector showCompleteness={true} />
            </Grid>
          </Grid>

          <Stack direction="row" spacing={2} sx={{ mb: 3 }}>
            <Button
              variant="contained"
              startIcon={<Translate />}
              onClick={runLanguageTests}
              disabled={isRunningTests}
              sx={{
                background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
                '&:hover': {
                  background: `linear-gradient(135deg, ${theme.palette.primary.dark}, ${theme.palette.primary.main})`
                }
              }}
            >
              {isRunningTests ? 'Running Tests...' : 'Run Language Tests'}
            </Button>
          </Stack>

          <Typography variant="h6" gutterBottom>
            Quick Language Tests
          </Typography>
          <Stack direction="row" spacing={1} sx={{ mb: 3, flexWrap: 'wrap', gap: 1 }}>
            {southAfricanLanguages.slice(0, 6).map((lang) => (
              <Button
                key={lang.code}
                variant="outlined"
                size="small"
                onClick={() => testLanguageSwitch(lang.code)}
                sx={{
                  borderColor: language === lang.code ? theme.palette.primary.main : theme.palette.grey[400],
                  backgroundColor: language === lang.code ? alpha(theme.palette.primary.main, 0.1) : 'transparent'
                }}
              >
                {lang.flag} {lang.name}
              </Button>
            ))}
          </Stack>

          {testResults.length > 0 && (
            <>
              <Divider sx={{ my: 3 }} />
              <Typography variant="h6" gutterBottom>
                Test Results
              </Typography>
              
              <Stack spacing={2}>
                {testResults.map((result, index) => (
                  <Alert
                    key={index}
                    severity={result.status === 'pass' ? 'success' : 
                             result.status === 'fail' ? 'error' :
                             result.status === 'warning' ? 'warning' : 'info'}
                    icon={getStatusIcon(result.status)}
                    sx={{
                      '& .MuiAlert-message': {
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        width: '100%'
                      }
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="subtitle2" fontWeight="bold">
                          {result.test}
                        </Typography>
                        <Typography variant="body2">
                          {result.message}
                        </Typography>
                      </Box>
                      <Chip
                        label={result.status.toUpperCase()}
                        size="small"
                        sx={{
                          backgroundColor: getStatusColor(result.status),
                          color: 'white',
                          fontWeight: 'bold'
                        }}
                      />
                    </Box>
                  </Alert>
                ))}
              </Stack>
            </>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default LanguageSelectorTest;
