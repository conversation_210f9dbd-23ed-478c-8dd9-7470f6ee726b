import React from 'react';
import UpgradePrompt from './UpgradePrompt';

const CommercialUpgrade: React.FC = () => {
  const features = [
    'Market analysis and pricing',
    'Sales and auction management',
    'Customer relationship management',
    'Contract and agreement tracking',
    'Market trend analysis',
    'Competitive pricing insights',
    'Sales performance analytics',
    'Export/import documentation'
  ];

  return (
    <UpgradePrompt
      moduleName="Commercial Management"
      features={features}
      tier="Enterprise"
      price="R599/month"
    />
  );
};

export default CommercialUpgrade;
