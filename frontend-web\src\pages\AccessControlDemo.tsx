import React from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  <PERSON>,
  <PERSON><PERSON>,
  Al<PERSON>,
  Di<PERSON>r,
  useTheme
} from '@mui/material';
import {
  Security,
  Person,
  Business,
  AdminPanelSettings,
  Lock,
  CheckCircle,
  Cancel
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import AccessControl from '../components/auth/AccessControl';
import ModuleGuard from '../components/auth/ModuleGuard';

const AccessControlDemo: React.FC = () => {
  const { 
    user, 
    hasPermission, 
    canAccessModule, 
    getAccessLevel,
    isBetaUser,
    isEnterpriseUser,
    isAdminUser 
  } = useAuth();
  const theme = useTheme();

  const modules = [
    { id: 'animals', name: 'Animal Management', beta: true },
    { id: 'health', name: 'Health Records', beta: true },
    { id: 'feeding', name: 'Feed Management', beta: true },
    { id: 'breeding', name: 'Breeding Management', beta: false },
    { id: 'financial', name: 'Financial Management', beta: false },
    { id: 'inventory', name: 'Inventory Management', beta: false },
    { id: 'commercial', name: 'Commercial Operations', beta: false },
    { id: 'compliance', name: 'Compliance Management', beta: false },
    { id: 'analytics', name: 'Advanced Analytics', beta: false }
  ];

  const permissions = [
    'view_animals',
    'create_animal',
    'update_animal',
    'delete_animal',
    'view_health_records',
    'create_health_record',
    'view_breeding_records',
    'create_breeding_record',
    'view_financial_records',
    'create_financial_record',
    'view_advanced_analytics',
    'view_ai_insights'
  ];

  if (!user) {
    return (
      <Container maxWidth="md" sx={{ py: 8 }}>
        <Alert severity="warning">
          <Typography variant="h6">Please log in to view access control demo</Typography>
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: 6 }}>
          <Security sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />
          <Typography variant="h3" fontWeight="bold" sx={{ mb: 2 }}>
            Access Control Demo
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Testing role-based access control and permissions
          </Typography>
        </Box>

        {/* Current User Info */}
        <Card sx={{ mb: 4, borderRadius: 3 }}>
          <CardContent sx={{ p: 4 }}>
            <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
              Current User Information
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <Person sx={{ color: 'primary.main' }} />
                  <Box>
                    <Typography variant="body2" color="text.secondary">Username</Typography>
                    <Typography variant="h6">{user.username}</Typography>
                  </Box>
                </Box>
                
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <AdminPanelSettings sx={{ color: 'secondary.main' }} />
                  <Box>
                    <Typography variant="body2" color="text.secondary">Role</Typography>
                    <Chip 
                      label={user.role} 
                      color={isAdminUser() ? 'error' : isEnterpriseUser() ? 'success' : 'primary'}
                      variant="outlined"
                    />
                  </Box>
                </Box>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <Business sx={{ color: 'info.main' }} />
                  <Box>
                    <Typography variant="body2" color="text.secondary">Access Level</Typography>
                    <Typography variant="h6">{getAccessLevel()}</Typography>
                  </Box>
                </Box>
                
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {isBetaUser() && <Chip label="Beta User" color="primary" size="small" />}
                  {isEnterpriseUser() && <Chip label="Enterprise User" color="success" size="small" />}
                  {isAdminUser() && <Chip label="Admin User" color="error" size="small" />}
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Module Access */}
        <Card sx={{ mb: 4, borderRadius: 3 }}>
          <CardContent sx={{ p: 4 }}>
            <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
              Module Access Rights
            </Typography>
            
            <Grid container spacing={2}>
              {modules.map((module) => {
                const hasAccess = canAccessModule(module.id);
                return (
                  <Grid item xs={12} sm={6} md={4} key={module.id}>
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 2,
                        p: 2,
                        borderRadius: 2,
                        border: '1px solid',
                        borderColor: hasAccess ? 'success.main' : 'error.main',
                        backgroundColor: hasAccess 
                          ? theme.palette.success.main + '10' 
                          : theme.palette.error.main + '10'
                      }}
                    >
                      {hasAccess ? (
                        <CheckCircle sx={{ color: 'success.main' }} />
                      ) : (
                        <Cancel sx={{ color: 'error.main' }} />
                      )}
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="body1" fontWeight="bold">
                          {module.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {module.beta ? 'Beta Feature' : 'Premium Feature'}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                );
              })}
            </Grid>
          </CardContent>
        </Card>

        {/* Permission Testing */}
        <Card sx={{ mb: 4, borderRadius: 3 }}>
          <CardContent sx={{ p: 4 }}>
            <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
              Permission Testing
            </Typography>
            
            <Grid container spacing={2}>
              {permissions.map((permission) => {
                const hasAccess = hasPermission(permission);
                return (
                  <Grid item xs={12} sm={6} md={4} key={permission}>
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 2,
                        p: 2,
                        borderRadius: 2,
                        border: '1px solid',
                        borderColor: hasAccess ? 'success.main' : 'error.main',
                        backgroundColor: hasAccess 
                          ? theme.palette.success.main + '10' 
                          : theme.palette.error.main + '10'
                      }}
                    >
                      {hasAccess ? (
                        <CheckCircle sx={{ color: 'success.main' }} />
                      ) : (
                        <Lock sx={{ color: 'error.main' }} />
                      )}
                      <Typography variant="body2">
                        {permission.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </Typography>
                    </Box>
                  </Grid>
                );
              })}
            </Grid>
          </CardContent>
        </Card>

        {/* Access Control Component Demo */}
        <Card sx={{ mb: 4, borderRadius: 3 }}>
          <CardContent sx={{ p: 4 }}>
            <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
              Access Control Component Demo
            </Typography>
            
            <Divider sx={{ my: 3 }} />
            
            <Typography variant="h6" sx={{ mb: 2 }}>
              Beta Users Can See This:
            </Typography>
            <AccessControl requiredRole="beta">
              <Alert severity="success">
                ✅ This content is visible to Beta users and above
              </Alert>
            </AccessControl>
            
            <Divider sx={{ my: 3 }} />
            
            <Typography variant="h6" sx={{ mb: 2 }}>
              Professional Users Can See This:
            </Typography>
            <AccessControl requiredRole="professional">
              <Alert severity="info">
                ✅ This content is visible to Professional users and above
              </Alert>
            </AccessControl>
            
            <Divider sx={{ my: 3 }} />
            
            <Typography variant="h6" sx={{ mb: 2 }}>
              Enterprise Users Can See This:
            </Typography>
            <AccessControl requiredRole="enterprise">
              <Alert severity="warning">
                ✅ This content is visible to Enterprise users and above
              </Alert>
            </AccessControl>
            
            <Divider sx={{ my: 3 }} />
            
            <Typography variant="h6" sx={{ mb: 2 }}>
              Admin Users Can See This:
            </Typography>
            <AccessControl requiredRole="admin">
              <Alert severity="error">
                ✅ This content is visible to Admin users only
              </Alert>
            </AccessControl>
          </CardContent>
        </Card>

        {/* Module Guard Demo */}
        <Card sx={{ borderRadius: 3 }}>
          <CardContent sx={{ p: 4 }}>
            <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
              Module Guard Demo
            </Typography>
            
            <Typography variant="body1" sx={{ mb: 3 }}>
              Click the buttons below to test module access based on your current role:
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  variant="outlined"
                  fullWidth
                  onClick={() => window.open('/dashboard/breeding', '_blank')}
                  disabled={!canAccessModule('breeding')}
                >
                  Breeding Module
                </Button>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  variant="outlined"
                  fullWidth
                  onClick={() => window.open('/dashboard/financial', '_blank')}
                  disabled={!canAccessModule('financial')}
                >
                  Financial Module
                </Button>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  variant="outlined"
                  fullWidth
                  onClick={() => window.open('/dashboard/commercial', '_blank')}
                  disabled={!canAccessModule('commercial')}
                >
                  Commercial Module
                </Button>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  variant="outlined"
                  fullWidth
                  onClick={() => window.open('/dashboard/analytics', '_blank')}
                  disabled={!canAccessModule('analytics')}
                >
                  Analytics Module
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </motion.div>
    </Container>
  );
};

export default AccessControlDemo;
