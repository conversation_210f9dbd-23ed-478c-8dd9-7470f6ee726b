import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  Alert,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  useTheme,
  alpha,
  IconButton,
  Badge,
  LinearProgress,
  Divider
} from '@mui/material';
import {
  Emergency,
  LocalHospital,
  Security,
  Gavel,
  Store,
  NotificationImportant,
  CheckCircle,
  Cancel,
  Schedule,
  Phone,
  Message,
  LocationOn,
  Warning,
  AutoAwesome,
  TrendingUp,
  MonetizationOn
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useLanguage } from '../../contexts/LanguageContext';

interface AutoTask {
  id: string;
  type: 'health_alert' | 'security_alert' | 'sales_opportunity' | 'supply_request';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  animalId?: string;
  animalName?: string;
  location: string;
  createdAt: string;
  status: 'pending' | 'dispatched' | 'accepted' | 'in_progress' | 'completed' | 'cancelled';
  assignedProvider?: {
    id: string;
    name: string;
    type: string;
    eta?: string;
    phone?: string;
  };
  estimatedCost?: number;
  urgencyLevel: number; // 1-10 scale
}

interface PanicButton {
  active: boolean;
  location: string;
  timestamp: string;
  responseTime: string;
}

const AutoTaskSystem: React.FC = () => {
  const theme = useTheme();
  const { translate } = useLanguage();
  
  const [autoTasks, setAutoTasks] = useState<AutoTask[]>([]);
  const [panicButton, setPanicButton] = useState<PanicButton | null>(null);
  const [showPanicDialog, setShowPanicDialog] = useState(false);
  const [systemStatus, setSystemStatus] = useState<'active' | 'maintenance' | 'offline'>('active');

  // Sample auto-generated tasks
  useEffect(() => {
    const sampleTasks: AutoTask[] = [
      {
        id: 'task1',
        type: 'health_alert',
        priority: 'critical',
        title: 'Critical Health Alert - Cattle C001',
        description: 'AI detected abnormal behavior and elevated temperature in Bessie. Immediate veterinary attention required.',
        animalId: 'C001',
        animalName: 'Bessie',
        location: 'Pasture A, Section 2',
        createdAt: new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
        status: 'dispatched',
        assignedProvider: {
          id: 'vet1',
          name: 'Dr. Sarah Johnson',
          type: 'veterinarian',
          eta: '15 minutes',
          phone: '+27 11 123 4567'
        },
        estimatedCost: 1200,
        urgencyLevel: 9
      },
      {
        id: 'task2',
        type: 'sales_opportunity',
        priority: 'medium',
        title: 'Market Opportunity - Premium Cattle',
        description: 'High demand detected for Holstein cattle. 3 animals ready for sale with optimal weight.',
        location: 'Main Farm',
        createdAt: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
        status: 'pending',
        estimatedCost: 45000,
        urgencyLevel: 6
      },
      {
        id: 'task3',
        type: 'supply_request',
        priority: 'high',
        title: 'Feed Stock Alert',
        description: 'Lucerne hay stock below 20%. Automatic reorder triggered for 50 bales.',
        location: 'Feed Storage',
        createdAt: new Date(Date.now() - 1800000).toISOString(), // 30 minutes ago
        status: 'accepted',
        assignedProvider: {
          id: 'supplier1',
          name: 'AgriSupply Pro',
          type: 'supplier',
          eta: '2 hours'
        },
        estimatedCost: 8500,
        urgencyLevel: 7
      }
    ];
    setAutoTasks(sampleTasks);
  }, []);

  const handlePanicButton = () => {
    const panic: PanicButton = {
      active: true,
      location: 'Main Farm - GPS: -25.7479, 28.2293',
      timestamp: new Date().toISOString(),
      responseTime: '< 10 minutes'
    };
    setPanicButton(panic);
    setShowPanicDialog(true);

    // Auto-generate security task
    const securityTask: AutoTask = {
      id: `panic_${Date.now()}`,
      type: 'security_alert',
      priority: 'critical',
      title: 'PANIC BUTTON ACTIVATED',
      description: 'Emergency assistance requested. Security team dispatched immediately.',
      location: panic.location,
      createdAt: panic.timestamp,
      status: 'dispatched',
      assignedProvider: {
        id: 'security1',
        name: 'Farm Security Solutions',
        type: 'security',
        eta: '8 minutes',
        phone: '+27 82 911 0000'
      },
      urgencyLevel: 10
    };
    setAutoTasks(prev => [securityTask, ...prev]);
  };

  const handleTaskAction = (taskId: string, action: 'accept' | 'cancel' | 'complete') => {
    setAutoTasks(prev => prev.map(task => 
      task.id === taskId 
        ? { ...task, status: action === 'accept' ? 'accepted' : action === 'cancel' ? 'cancelled' : 'completed' }
        : task
    ));
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return theme.palette.error.main;
      case 'high': return theme.palette.warning.main;
      case 'medium': return theme.palette.info.main;
      case 'low': return theme.palette.success.main;
      default: return theme.palette.grey[500];
    }
  };

  const getTaskIcon = (type: string) => {
    switch (type) {
      case 'health_alert': return <LocalHospital />;
      case 'security_alert': return <Security />;
      case 'sales_opportunity': return <Gavel />;
      case 'supply_request': return <Store />;
      default: return <NotificationImportant />;
    }
  };

  const activeTasksCount = autoTasks.filter(task => ['pending', 'dispatched', 'accepted', 'in_progress'].includes(task.status)).length;
  const criticalTasksCount = autoTasks.filter(task => task.priority === 'critical' && task.status !== 'completed').length;

  return (
    <Box sx={{ p: 3 }}>
      {/* Header with System Status */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" fontWeight="bold" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <AutoAwesome sx={{ color: theme.palette.primary.main }} />
          Auto-Task System
          <Chip 
            label={systemStatus.toUpperCase()} 
            size="small" 
            color={systemStatus === 'active' ? 'success' : 'warning'}
          />
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Badge badgeContent={criticalTasksCount} color="error">
            <Button
              variant="contained"
              color="error"
              size="large"
              startIcon={<Emergency />}
              onClick={handlePanicButton}
              sx={{ 
                minHeight: 56,
                fontSize: '1.1rem',
                fontWeight: 'bold',
                animation: criticalTasksCount > 0 ? 'pulse 2s infinite' : 'none',
                '@keyframes pulse': {
                  '0%': { transform: 'scale(1)' },
                  '50%': { transform: 'scale(1.05)' },
                  '100%': { transform: 'scale(1)' }
                }
              }}
            >
              PANIC BUTTON
            </Button>
          </Badge>
        </Box>
      </Box>

      {/* System Overview Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <motion.div whileHover={{ y: -4 }}>
            <Card sx={{ borderRadius: 3, background: 'linear-gradient(135deg, #4CAF50, #66BB6A)' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" color="white" fontWeight="bold">
                      {activeTasksCount}
                    </Typography>
                    <Typography variant="body2" color="rgba(255,255,255,0.8)">
                      Active Tasks
                    </Typography>
                  </Box>
                  <AutoAwesome sx={{ fontSize: 40, color: 'rgba(255,255,255,0.8)' }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={3}>
          <motion.div whileHover={{ y: -4 }}>
            <Card sx={{ borderRadius: 3, background: 'linear-gradient(135deg, #F44336, #EF5350)' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" color="white" fontWeight="bold">
                      {criticalTasksCount}
                    </Typography>
                    <Typography variant="body2" color="rgba(255,255,255,0.8)">
                      Critical Alerts
                    </Typography>
                  </Box>
                  <Warning sx={{ fontSize: 40, color: 'rgba(255,255,255,0.8)' }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={3}>
          <motion.div whileHover={{ y: -4 }}>
            <Card sx={{ borderRadius: 3, background: 'linear-gradient(135deg, #2196F3, #42A5F5)' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" color="white" fontWeight="bold">
                      98%
                    </Typography>
                    <Typography variant="body2" color="rgba(255,255,255,0.8)">
                      Success Rate
                    </Typography>
                  </Box>
                  <TrendingUp sx={{ fontSize: 40, color: 'rgba(255,255,255,0.8)' }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={3}>
          <motion.div whileHover={{ y: -4 }}>
            <Card sx={{ borderRadius: 3, background: 'linear-gradient(135deg, #FF9800, #FFB74D)' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" color="white" fontWeight="bold">
                      5.2min
                    </Typography>
                    <Typography variant="body2" color="rgba(255,255,255,0.8)">
                      Avg Response
                    </Typography>
                  </Box>
                  <Schedule sx={{ fontSize: 40, color: 'rgba(255,255,255,0.8)' }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
      </Grid>

      {/* Active Panic Alert */}
      <AnimatePresence>
        {panicButton && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
          >
            <Alert 
              severity="error" 
              sx={{ 
                mb: 3, 
                border: '2px solid',
                borderColor: theme.palette.error.main,
                backgroundColor: alpha(theme.palette.error.main, 0.1),
                '& .MuiAlert-icon': { fontSize: 32 }
              }}
              action={
                <Button 
                  color="inherit" 
                  size="small" 
                  onClick={() => setPanicButton(null)}
                >
                  CLEAR
                </Button>
              }
            >
              <Typography variant="h6" fontWeight="bold">
                🚨 EMERGENCY RESPONSE ACTIVE
              </Typography>
              <Typography variant="body2">
                Security team dispatched to {panicButton.location} • ETA: {panicButton.responseTime}
              </Typography>
            </Alert>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Auto-Generated Tasks */}
      <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
        Auto-Generated Tasks
      </Typography>

      <Grid container spacing={3}>
        {autoTasks.map((task, index) => (
          <Grid item xs={12} key={task.id}>
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card sx={{ 
                borderRadius: 3,
                border: task.priority === 'critical' ? `2px solid ${theme.palette.error.main}` : '1px solid rgba(255,255,255,0.1)',
                background: task.priority === 'critical' 
                  ? alpha(theme.palette.error.main, 0.05)
                  : 'rgba(255,255,255,0.02)',
                backdropFilter: 'blur(10px)'
              }}>
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                    {/* Task Icon */}
                    <Avatar sx={{ 
                      backgroundColor: alpha(getPriorityColor(task.priority), 0.2),
                      color: getPriorityColor(task.priority)
                    }}>
                      {getTaskIcon(task.type)}
                    </Avatar>

                    {/* Task Details */}
                    <Box sx={{ flexGrow: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                        <Typography variant="h6" fontWeight="bold">
                          {task.title}
                        </Typography>
                        <Chip 
                          label={task.priority.toUpperCase()} 
                          size="small" 
                          sx={{ 
                            backgroundColor: getPriorityColor(task.priority),
                            color: 'white',
                            fontWeight: 'bold'
                          }}
                        />
                        <Chip 
                          label={task.status.replace('_', ' ').toUpperCase()} 
                          size="small" 
                          variant="outlined"
                        />
                      </Box>

                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        {task.description}
                      </Typography>

                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, mb: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                          <LocationOn sx={{ fontSize: 16, color: 'text.secondary' }} />
                          <Typography variant="body2" color="text.secondary">
                            {task.location}
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                          <Schedule sx={{ fontSize: 16, color: 'text.secondary' }} />
                          <Typography variant="body2" color="text.secondary">
                            {new Date(task.createdAt).toLocaleTimeString()}
                          </Typography>
                        </Box>
                        {task.estimatedCost && (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <MonetizationOn sx={{ fontSize: 16, color: 'text.secondary' }} />
                            <Typography variant="body2" color="text.secondary">
                              R{task.estimatedCost.toLocaleString()}
                            </Typography>
                          </Box>
                        )}
                      </Box>

                      {/* Urgency Level */}
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="caption" color="text.secondary">
                          Urgency Level: {task.urgencyLevel}/10
                        </Typography>
                        <LinearProgress 
                          variant="determinate" 
                          value={task.urgencyLevel * 10} 
                          sx={{ 
                            height: 6, 
                            borderRadius: 3,
                            backgroundColor: alpha(theme.palette.grey[300], 0.3),
                            '& .MuiLinearProgress-bar': {
                              backgroundColor: getPriorityColor(task.priority)
                            }
                          }}
                        />
                      </Box>

                      {/* Assigned Provider */}
                      {task.assignedProvider && (
                        <Box sx={{ 
                          p: 2, 
                          borderRadius: 2, 
                          backgroundColor: alpha(theme.palette.success.main, 0.1),
                          border: `1px solid ${alpha(theme.palette.success.main, 0.3)}`
                        }}>
                          <Typography variant="subtitle2" fontWeight="bold" color="success.main" gutterBottom>
                            Assigned Provider
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                            <Box>
                              <Typography variant="body2" fontWeight="bold">
                                {task.assignedProvider.name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {task.assignedProvider.type} • ETA: {task.assignedProvider.eta}
                              </Typography>
                            </Box>
                            {task.assignedProvider.phone && (
                              <IconButton size="small" color="success">
                                <Phone />
                              </IconButton>
                            )}
                          </Box>
                        </Box>
                      )}
                    </Box>

                    {/* Task Actions */}
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      {task.status === 'pending' && (
                        <>
                          <Button
                            variant="contained"
                            size="small"
                            startIcon={<CheckCircle />}
                            onClick={() => handleTaskAction(task.id, 'accept')}
                          >
                            Accept
                          </Button>
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<Cancel />}
                            onClick={() => handleTaskAction(task.id, 'cancel')}
                          >
                            Cancel
                          </Button>
                        </>
                      )}
                      {['accepted', 'in_progress'].includes(task.status) && (
                        <Button
                          variant="contained"
                          color="success"
                          size="small"
                          startIcon={<CheckCircle />}
                          onClick={() => handleTaskAction(task.id, 'complete')}
                        >
                          Complete
                        </Button>
                      )}
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* Panic Button Dialog */}
      <Dialog open={showPanicDialog} onClose={() => setShowPanicDialog(false)}>
        <DialogTitle sx={{ textAlign: 'center', color: theme.palette.error.main }}>
          <Emergency sx={{ fontSize: 48, mb: 1 }} />
          <Typography variant="h5" fontWeight="bold">
            Emergency Response Activated
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" sx={{ textAlign: 'center', mb: 2 }}>
            Security team has been notified and is en route to your location.
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center' }}>
            Emergency services will arrive within 10 minutes.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ justifyContent: 'center' }}>
          <Button onClick={() => setShowPanicDialog(false)} color="primary">
            OK
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AutoTaskSystem;
