/**
 * AgriIntel 2026 - Quantum Navigation System
 * Next-Generation Navigation with Neural Networks & Glassmorphism
 */

/* ===== QUANTUM NAVIGATION BASE ===== */
.agri-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: var(--glass-strong);
  -webkit-backdrop-filter: var(--glass-backdrop-strong);
  backdrop-filter: var(--glass-backdrop-strong);
  border-bottom: 1px solid var(--glass-border-medium);
  z-index: 1000;
  display: flex;
  align-items: center;
  padding: 0 var(--space-xl);
  transition: all var(--timing-normal);
}

.agri-nav::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--neural-primary);
  opacity: 0.3;
}

.agri-nav-scrolled {
  height: 64px;
  box-shadow: var(--shadow-elevated);
  background: var(--glass-medium);
}

/* ===== QUANTUM LOGO ===== */
.agri-nav-logo {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  text-decoration: none;
  color: var(--quantum-text);
  font-size: 1.5rem;
  font-weight: 800;
  background: var(--neural-primary);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 400% 400%;
  animation: holographic-rotate 3s ease infinite;
}

.agri-nav-logo-icon {
  width: 40px;
  height: 40px;
  background: var(--neural-primary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
  position: relative;
  overflow: hidden;
}

.agri-nav-logo-icon::before {
  content: '';
  position: absolute;
  inset: 0;
  background: var(--neural-secondary);
  opacity: 0;
  transition: opacity var(--timing-fast);
}

.agri-nav-logo:hover .agri-nav-logo-icon::before {
  opacity: 1;
}

/* ===== QUANTUM NAVIGATION MENU ===== */
.agri-nav-menu {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  margin-left: auto;
  list-style: none;
}

.agri-nav-item {
  position: relative;
}

.agri-nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-sm) var(--space-md);
  color: var(--quantum-text-secondary);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  border-radius: var(--radius-lg);
  transition: all var(--timing-fast);
  position: relative;
  overflow: hidden;
}

.agri-nav-link::before {
  content: '';
  position: absolute;
  inset: 0;
  background: var(--glass-light);
  opacity: 0;
  transition: opacity var(--timing-fast);
  border-radius: inherit;
}

.agri-nav-link:hover {
  color: var(--quantum-text);
  transform: translateY(-2px);
}

.agri-nav-link:hover::before {
  opacity: 1;
}

.agri-nav-link-active {
  color: var(--quantum-text);
  background: var(--glass-medium);
  border: 1px solid var(--glass-border-light);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.agri-nav-link-active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  width: 20px;
  height: 2px;
  background: var(--neural-primary);
  transform: translateX(-50%);
  border-radius: var(--radius-full);
}

/* ===== QUANTUM DROPDOWN ===== */
.agri-nav-dropdown {
  position: absolute;
  top: calc(100% + var(--space-sm));
  left: 0;
  min-width: 200px;
  background: var(--glass-strong);
  -webkit-backdrop-filter: var(--glass-backdrop-strong);
  backdrop-filter: var(--glass-backdrop-strong);
  border: 1px solid var(--glass-border-medium);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-elevated);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--timing-normal);
  z-index: 1001;
  padding: var(--space-sm);
}

.agri-nav-item:hover .agri-nav-dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.agri-nav-dropdown-item {
  display: block;
  padding: var(--space-sm) var(--space-md);
  color: var(--quantum-text-secondary);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: all var(--timing-fast);
  font-size: 0.875rem;
}

.agri-nav-dropdown-item:hover {
  color: var(--quantum-text);
  background: var(--glass-light);
  transform: translateX(4px);
}

/* ===== QUANTUM MOBILE MENU ===== */
.agri-nav-mobile-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  padding: var(--space-sm);
  background: var(--glass-light);
  border: 1px solid var(--glass-border-light);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--timing-fast);
}

.agri-nav-mobile-toggle:hover {
  background: var(--glass-medium);
  transform: scale(1.05);
}

.agri-nav-mobile-toggle span {
  width: 20px;
  height: 2px;
  background: var(--quantum-text);
  border-radius: var(--radius-full);
  transition: all var(--timing-fast);
}

.agri-nav-mobile-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.agri-nav-mobile-toggle.active span:nth-child(2) {
  opacity: 0;
}

.agri-nav-mobile-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

.agri-nav-mobile-menu {
  position: fixed;
  top: 80px;
  left: 0;
  right: 0;
  background: var(--glass-strong);
  -webkit-backdrop-filter: var(--glass-backdrop-strong);
  backdrop-filter: var(--glass-backdrop-strong);
  border-bottom: 1px solid var(--glass-border-medium);
  transform: translateY(-100%);
  transition: transform var(--timing-normal);
  z-index: 999;
  padding: var(--space-lg);
}

.agri-nav-mobile-menu.active {
  transform: translateY(0);
}

.agri-nav-mobile-menu .agri-nav-menu {
  flex-direction: column;
  align-items: stretch;
  margin-left: 0;
  gap: var(--space-sm);
}

.agri-nav-mobile-menu .agri-nav-link {
  padding: var(--space-md);
  justify-content: center;
  font-size: 1rem;
}

/* ===== QUANTUM USER MENU ===== */
.agri-nav-user {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  margin-left: var(--space-lg);
}

.agri-nav-user-avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  background: var(--neural-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--timing-fast);
  position: relative;
  overflow: hidden;
}

.agri-nav-user-avatar::before {
  content: '';
  position: absolute;
  inset: 0;
  background: var(--neural-secondary);
  opacity: 0;
  transition: opacity var(--timing-fast);
  border-radius: inherit;
}

.agri-nav-user-avatar:hover {
  transform: scale(1.1);
}

.agri-nav-user-avatar:hover::before {
  opacity: 1;
}

/* ===== RESPONSIVE QUANTUM NAVIGATION ===== */
@media (max-width: 768px) {
  .agri-nav {
    padding: 0 var(--space-lg);
  }
  
  .agri-nav-menu {
    display: none;
  }
  
  .agri-nav-mobile-toggle {
    display: flex;
  }
  
  .agri-nav-user {
    margin-left: var(--space-md);
  }
}

@media (max-width: 480px) {
  .agri-nav {
    padding: 0 var(--space-md);
    height: 64px;
  }
  
  .agri-nav-mobile-menu {
    top: 64px;
  }
  
  .agri-nav-logo {
    font-size: 1.25rem;
  }
  
  .agri-nav-logo-icon {
    width: 32px;
    height: 32px;
    font-size: 1rem;
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  .agri-nav,
  .agri-nav-link,
  .agri-nav-dropdown,
  .agri-nav-mobile-menu,
  .agri-nav-user-avatar {
    transition: none !important;
    animation: none !important;
    transform: none !important;
  }
  
  .agri-nav-link:hover,
  .agri-nav-user-avatar:hover {
    transform: none !important;
  }
}
