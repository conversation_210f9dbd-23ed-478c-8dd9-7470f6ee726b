/**
 * Authentication Flows Test Suite
 * Comprehensive testing for the dual authentication system
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';

// Import components
import AgriIntelLanding from '../pages/AgriIntelLanding';
import BetaV1Login from '../pages/BetaV1Login';
import ProfessionalV1Login from '../pages/ProfessionalV1Login';
import { AuthProvider } from '../contexts/AuthContext';
import { ThemeContextProvider } from '../contexts/ThemeContext';
import { LanguageProvider } from '../contexts/LanguageContext';

// Mock navigation
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock authentication context
const mockLogin = jest.fn();
const mockAuthContext = {
  user: null,
  login: mockLogin,
  logout: jest.fn(),
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

jest.mock('../contexts/AuthContext', () => ({
  useAuth: () => mockAuthContext,
  AuthProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const theme = createTheme();
  
  return (
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        <ThemeContextProvider>
          <LanguageProvider>
            <AuthProvider>
              {children}
            </AuthProvider>
          </LanguageProvider>
        </ThemeContextProvider>
      </ThemeProvider>
    </BrowserRouter>
  );
};

describe('Authentication Flows', () => {
  beforeEach(() => {
    mockNavigate.mockClear();
    mockLogin.mockClear();
  });

  describe('Landing Page Navigation', () => {
    test('should render landing page with dual authentication options', () => {
      render(
        <TestWrapper>
          <AgriIntelLanding />
        </TestWrapper>
      );

      // Check for BETA V1 option
      expect(screen.getByText('Try BETA V1')).toBeInTheDocument();
      expect(screen.getByText('Go Professional')).toBeInTheDocument();
    });

    test('should navigate to BETA login when Try BETA V1 is clicked', async () => {
      render(
        <TestWrapper>
          <AgriIntelLanding />
        </TestWrapper>
      );

      const betaButton = screen.getByText('Try BETA V1');
      fireEvent.click(betaButton);

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/login-beta');
      });
    });

    test('should navigate to Professional login when Go Professional is clicked', async () => {
      render(
        <TestWrapper>
          <AgriIntelLanding />
        </TestWrapper>
      );

      const proButton = screen.getByText('Go Professional');
      fireEvent.click(proButton);

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/login-pro');
      });
    });

    test('should handle subscription plan selection correctly', async () => {
      render(
        <TestWrapper>
          <AgriIntelLanding />
        </TestWrapper>
      );

      // Find and click BETA plan
      const betaPlanCard = screen.getByText('BETA V1').closest('[role="button"]');
      if (betaPlanCard) {
        fireEvent.click(betaPlanCard);
        
        await waitFor(() => {
          expect(mockNavigate).toHaveBeenCalledWith('/login-beta');
        });
      }
    });
  });

  describe('BETA V1 Login', () => {
    test('should render BETA V1 login page with correct branding', () => {
      render(
        <TestWrapper>
          <BetaV1Login />
        </TestWrapper>
      );

      expect(screen.getByText('Welcome to AgriIntel BETA')).toBeInTheDocument();
      expect(screen.getByText('BETA V1')).toBeInTheDocument();
      expect(screen.getByText('Start Free Trial')).toBeInTheDocument();
    });

    test('should display BETA features and limitations', () => {
      render(
        <TestWrapper>
          <BetaV1Login />
        </TestWrapper>
      );

      expect(screen.getByText('Up to 50 animals')).toBeInTheDocument();
      expect(screen.getByText('Excel-only reports')).toBeInTheDocument();
      expect(screen.getByText('Basic health monitoring')).toBeInTheDocument();
    });

    test('should show upgrade prompts for locked features', () => {
      render(
        <TestWrapper>
          <BetaV1Login />
        </TestWrapper>
      );

      expect(screen.getByText('🔒 Upgrade to Professional V1 for:')).toBeInTheDocument();
      expect(screen.getByText('AI-Powered Automation')).toBeInTheDocument();
      expect(screen.getByText('Marketplace Connections')).toBeInTheDocument();
    });

    test('should handle login form submission', async () => {
      render(
        <TestWrapper>
          <BetaV1Login />
        </TestWrapper>
      );

      const usernameInput = screen.getByLabelText('Username');
      const passwordInput = screen.getByLabelText('Password');
      const loginButton = screen.getByText('Start Free Trial');

      fireEvent.change(usernameInput, { target: { value: 'Demo' } });
      fireEvent.change(passwordInput, { target: { value: '123' } });
      fireEvent.click(loginButton);

      await waitFor(() => {
        expect(mockLogin).toHaveBeenCalledWith('Demo', '123');
      });
    });

    test('should navigate to Professional login when upgrade button is clicked', async () => {
      render(
        <TestWrapper>
          <BetaV1Login />
        </TestWrapper>
      );

      const upgradeButton = screen.getByText('Upgrade to Professional V1');
      fireEvent.click(upgradeButton);

      expect(mockNavigate).toHaveBeenCalledWith('/login-pro');
    });
  });

  describe('Professional V1 Login', () => {
    test('should render Professional V1 login page with correct branding', () => {
      render(
        <TestWrapper>
          <ProfessionalV1Login />
        </TestWrapper>
      );

      expect(screen.getByText('Professional Access')).toBeInTheDocument();
      expect(screen.getByText('PROFESSIONAL V1')).toBeInTheDocument();
      expect(screen.getByText('Access Professional Dashboard')).toBeInTheDocument();
    });

    test('should display Professional features', () => {
      render(
        <TestWrapper>
          <ProfessionalV1Login />
        </TestWrapper>
      );

      expect(screen.getByText('Unlimited Animal Management')).toBeInTheDocument();
      expect(screen.getByText('AI-Powered Health Monitoring')).toBeInTheDocument();
      expect(screen.getByText('Marketplace Connections')).toBeInTheDocument();
    });

    test('should show marketplace benefits', () => {
      render(
        <TestWrapper>
          <ProfessionalV1Login />
        </TestWrapper>
      );

      expect(screen.getByText('🏪 Marketplace Connections (Uber-style)')).toBeInTheDocument();
      expect(screen.getByText('Veterinarian Network Access')).toBeInTheDocument();
      expect(screen.getByText('In-app Payment System')).toBeInTheDocument();
    });

    test('should handle login form submission', async () => {
      render(
        <TestWrapper>
          <ProfessionalV1Login />
        </TestWrapper>
      );

      const usernameInput = screen.getByLabelText('Username');
      const passwordInput = screen.getByLabelText('Password');
      const loginButton = screen.getByText('Access Professional Dashboard');

      fireEvent.change(usernameInput, { target: { value: 'Pro' } });
      fireEvent.change(passwordInput, { target: { value: '123' } });
      fireEvent.click(loginButton);

      await waitFor(() => {
        expect(mockLogin).toHaveBeenCalledWith('Pro', '123');
      });
    });

    test('should navigate to BETA login when downgrade option is clicked', async () => {
      render(
        <TestWrapper>
          <ProfessionalV1Login />
        </TestWrapper>
      );

      const betaButton = screen.getByText('Start with BETA V1 (Free Trial)');
      fireEvent.click(betaButton);

      expect(mockNavigate).toHaveBeenCalledWith('/login-beta');
    });
  });

  describe('Form Validation', () => {
    test('should show error when login fields are empty', async () => {
      render(
        <TestWrapper>
          <BetaV1Login />
        </TestWrapper>
      );

      const loginButton = screen.getByText('Start Free Trial');
      fireEvent.click(loginButton);

      await waitFor(() => {
        expect(screen.getByText('Please enter both username and password')).toBeInTheDocument();
      });
    });

    test('should handle login errors gracefully', async () => {
      mockLogin.mockRejectedValueOnce(new Error('Invalid credentials'));

      render(
        <TestWrapper>
          <BetaV1Login />
        </TestWrapper>
      );

      const usernameInput = screen.getByLabelText('Username');
      const passwordInput = screen.getByLabelText('Password');
      const loginButton = screen.getByText('Start Free Trial');

      fireEvent.change(usernameInput, { target: { value: 'invalid' } });
      fireEvent.change(passwordInput, { target: { value: 'wrong' } });
      fireEvent.click(loginButton);

      await waitFor(() => {
        expect(screen.getByText('Invalid credentials')).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    test('should have proper ARIA labels on buttons', () => {
      render(
        <TestWrapper>
          <AgriIntelLanding />
        </TestWrapper>
      );

      const betaButton = screen.getByLabelText('Start BETA V1 free trial');
      const proButton = screen.getByLabelText('Access Professional V1 features');

      expect(betaButton).toBeInTheDocument();
      expect(proButton).toBeInTheDocument();
    });

    test('should support keyboard navigation', () => {
      render(
        <TestWrapper>
          <BetaV1Login />
        </TestWrapper>
      );

      const usernameInput = screen.getByLabelText('Username');
      const passwordInput = screen.getByLabelText('Password');

      expect(usernameInput).toHaveAttribute('required');
      expect(passwordInput).toHaveAttribute('required');
      expect(passwordInput).toHaveAttribute('type', 'password');
    });
  });

  describe('Responsive Design', () => {
    test('should render correctly on mobile viewport', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(
        <TestWrapper>
          <AgriIntelLanding />
        </TestWrapper>
      );

      // Should still render main elements
      expect(screen.getByText('Try BETA V1')).toBeInTheDocument();
      expect(screen.getByText('Go Professional')).toBeInTheDocument();
    });
  });
});
