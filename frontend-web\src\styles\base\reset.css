/**
 * CSS Reset - Modern Normalize
 * Based on normalize.css and modern best practices
 */

/* Box sizing rules */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Remove default margin and padding */
* {
  margin: 0;
  padding: 0;
}

/* Remove list styles on ul, ol elements with a list role */
ul[role='list'],
ol[role='list'] {
  list-style: none;
}

/* Set core root defaults */
html:focus-within {
  scroll-behavior: smooth;
}

/* Set core body defaults */
body {
  min-height: 100vh;
  text-rendering: optimizeSpeed;
  line-height: 1.5;
}

/* A elements that don't have a class get default styles */
a:not([class]) {
  text-decoration-skip-ink: auto;
}

/* Make images easier to work with */
img,
picture {
  max-width: 100%;
  display: block;
}

/* Inherit fonts for inputs and buttons */
input,
button,
textarea,
select {
  font: inherit;
}

/* Remove all animations, transitions and smooth scroll for people that prefer not to see them */
@media (prefers-reduced-motion: reduce) {
  html:focus-within {
   scroll-behavior: auto;
  }
  
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Additional resets for better consistency */

/* Remove default button styles */
button {
  background: none;
  border: none;
  cursor: pointer;
}

/* Remove default fieldset styles */
fieldset {
  border: none;
  margin: 0;
  padding: 0;
}

/* Remove default legend styles */
legend {
  padding: 0;
}

/* Remove default table styles */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* Remove default hr styles */
hr {
  border: none;
  height: 1px;
  background: currentColor;
  opacity: 0.25;
}

/* Remove default address styles */
address {
  font-style: normal;
}

/* Remove default cite styles */
cite {
  font-style: normal;
}

/* Remove default code styles */
code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 1em;
}

/* Remove default small styles */
small {
  font-size: 0.875em;
}

/* Remove default sub and sup styles */
sub,
sup {
  font-size: 0.75em;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/* Remove default summary styles */
summary {
  cursor: pointer;
}

/* Remove default details styles */
details {
  display: block;
}

/* Remove default mark styles */
mark {
  background-color: #ffff99;
  color: #000000;
}

/* Remove default del styles */
del {
  text-decoration: line-through;
}

/* Remove default ins styles */
ins {
  text-decoration: underline;
}

/* Remove default abbr styles */
abbr[title] {
  text-decoration: underline dotted;
}

/* Remove default b and strong styles */
b,
strong {
  font-weight: bolder;
}

/* Remove default i and em styles */
i,
em {
  font-style: italic;
}

/* Remove default q styles */
q {
  quotes: '"' '"' "'" "'";
}

/* Remove default blockquote styles */
blockquote {
  margin: 0;
  padding: 0;
}

/* Remove default dl, dt, dd styles */
dl,
dt,
dd {
  margin: 0;
}

/* Remove default h1-h6 styles */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-size: inherit;
  font-weight: inherit;
}

/* Remove default p styles */
p {
  margin: 0;
}

/* Remove default ul, ol styles */
ul,
ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

/* Remove default li styles */
li {
  margin: 0;
}

/* Remove default form styles */
form {
  margin: 0;
}

/* Remove default input styles */
input {
  margin: 0;
  padding: 0;
  border: none;
  background: none;
  font: inherit;
  color: inherit;
}

/* Remove default textarea styles */
textarea {
  margin: 0;
  padding: 0;
  border: none;
  background: none;
  font: inherit;
  color: inherit;
  resize: vertical;
}

/* Remove default select styles */
select {
  margin: 0;
  padding: 0;
  border: none;
  background: none;
  font: inherit;
  color: inherit;
}

/* Remove default option styles */
option {
  font: inherit;
  color: inherit;
}

/* Remove default optgroup styles */
optgroup {
  font: inherit;
  color: inherit;
}

/* Remove default label styles */
label {
  margin: 0;
  padding: 0;
  font: inherit;
  color: inherit;
}

/* Remove default progress styles */
progress {
  vertical-align: baseline;
}

/* Remove default meter styles */
meter {
  vertical-align: baseline;
}

/* Remove default canvas styles */
canvas {
  display: block;
}

/* Remove default svg styles */
svg {
  display: block;
  vertical-align: middle;
}

/* Remove default video styles */
video {
  display: block;
}

/* Remove default audio styles */
audio {
  display: block;
}

/* Remove default iframe styles */
iframe {
  border: none;
}

/* Remove default embed styles */
embed {
  display: block;
}

/* Remove default object styles */
object {
  display: block;
}
