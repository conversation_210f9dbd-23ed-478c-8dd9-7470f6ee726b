import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Box, CircularProgress, Typography } from '@mui/material';

const BetaOnlyDashboard: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  // Redirect to the enhanced beta dashboard
  useEffect(() => {
    // Redirect to the enhanced beta page which has the proper beta interface
    navigate('/enhanced-beta', { replace: true });
  }, [navigate]);

  // Show loading while redirecting
  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)'
      }}
    >
      <Box sx={{ textAlign: 'center', color: 'white' }}>
        <CircularProgress sx={{ color: 'white', mb: 2 }} />
        <Typography variant="h6">
          Loading AgriIntel BETA Dashboard...
        </Typography>
        <Typography variant="body2" sx={{ opacity: 0.8, mt: 1 }}>
          Welcome, {user?.username || 'Demo User'}
        </Typography>
      </Box>
    </Box>
  );
};

export default BetaOnlyDashboard;
