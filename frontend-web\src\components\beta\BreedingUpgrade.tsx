import React from 'react';
import UpgradePrompt from './UpgradePrompt';

const BreedingUpgrade: React.FC = () => {
  const features = [
    'Advanced breeding program management',
    'Genetic tracking and lineage analysis',
    'Breeding performance analytics',
    'Mating schedule optimization',
    'Pregnancy monitoring and alerts',
    'Breeding cost analysis',
    'Genetic diversity reports',
    'AI breeding recommendations'
  ];

  return (
    <UpgradePrompt
      moduleName="Breeding Management"
      features={features}
      tier="Professional"
      price="R299/month"
    />
  );
};

export default BreedingUpgrade;
