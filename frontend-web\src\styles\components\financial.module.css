/* Financial Module Component Styles - CSS Module */

.container {
  padding: var(--spacing-xl);
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
}

.title {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.controls {
  display: flex;
  gap: var(--spacing-sm);
}

.select {
  padding: var(--spacing-md) var(--spacing-lg);
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-base);
  transition: all var(--transition-normal);
}

.select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.button {
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  text-transform: none;
}

.buttonPrimary {
  composes: button;
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-md);
}

.buttonPrimary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.buttonSecondary {
  composes: button;
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--neutral-300);
}

.buttonSecondary:hover {
  background: var(--bg-tertiary);
  border-color: var(--primary-500);
}

/* Stats Grid */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.statCard {
  background: var(--bg-primary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--neutral-200);
  transition: all var(--transition-normal);
}

.statCard:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-300);
}

.statLabel {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin-bottom: var(--spacing-xs);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.statValue {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  margin: 0;
}

.statValuePositive {
  composes: statValue;
  color: var(--primary-600);
}

.statValueNegative {
  composes: statValue;
  color: #d32f2f;
}

.statValueNeutral {
  composes: statValue;
  color: var(--primary-500);
}

/* Content Grid */
.contentGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--neutral-200);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-300);
}

.cardHeader {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--neutral-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--bg-secondary);
}

.cardTitle {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.cardAction {
  color: var(--primary-500);
  text-decoration: none;
  font-weight: 500;
  font-size: var(--font-size-sm);
  transition: color var(--transition-fast);
}

.cardAction:hover {
  color: var(--primary-700);
  text-decoration: underline;
}

.cardContent {
  padding: var(--spacing-lg);
}

/* Table Styles */
.table {
  width: 100%;
  border-collapse: collapse;
}

.tableHeader {
  text-align: left;
  color: var(--text-muted);
  font-weight: 600;
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding-bottom: var(--spacing-md);
  border-bottom: 2px solid var(--neutral-200);
}

.tableRow {
  border-top: 1px solid var(--neutral-200);
  transition: background-color var(--transition-fast);
}

.tableRow:hover {
  background-color: var(--bg-secondary);
}

.tableCell {
  padding: var(--spacing-md) 0;
  vertical-align: middle;
}

.tableCellPositive {
  composes: tableCell;
  color: var(--primary-600);
  font-weight: 600;
}

.tableCellNegative {
  composes: tableCell;
  color: #d32f2f;
  font-weight: 600;
}

.emptyState {
  padding: var(--spacing-2xl);
  text-align: center;
  color: var(--text-muted);
  font-style: italic;
}

/* Budget Progress Bar */
.budgetItem {
  margin-bottom: var(--spacing-lg);
}

.budgetItem:last-child {
  margin-bottom: 0;
}

.budgetHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.budgetCategory {
  font-weight: 600;
  color: var(--text-primary);
  text-transform: capitalize;
}

.budgetAmount {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.progressBar {
  width: 100%;
  height: 8px;
  background-color: var(--neutral-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progressFill {
  height: 100%;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
  transition: width var(--transition-slow);
}

/* Action Cards Grid */
.actionGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.actionCard {
  background: var(--bg-primary);
  padding: var(--spacing-2xl);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  text-align: center;
  border: 1px solid var(--neutral-200);
  transition: all var(--transition-normal);
}

.actionCard:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-300);
}

.actionIcon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-md);
  display: block;
}

.actionTitle {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.actionDescription {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin-bottom: var(--spacing-lg);
  line-height: 1.5;
}

.actionButton {
  composes: button;
  composes: buttonPrimary;
  width: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: var(--spacing-lg);
  }
  
  .header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  
  .controls {
    justify-content: stretch;
  }
  
  .select,
  .button {
    flex: 1;
  }
  
  .statsGrid {
    grid-template-columns: 1fr;
  }
  
  .contentGrid {
    grid-template-columns: 1fr;
  }
  
  .actionGrid {
    grid-template-columns: 1fr;
  }
}
