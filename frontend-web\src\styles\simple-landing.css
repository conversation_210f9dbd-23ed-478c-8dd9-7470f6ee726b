/* Simple Landing Page Styles */

.simple-landing-features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.simple-landing-feature-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 10px;
  text-align: center;
}

.simple-landing-feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.simple-landing-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.simple-landing-stat-item {
  text-align: center;
}

.simple-landing-stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: #FFD700;
}

.simple-landing-pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.simple-landing-pricing-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 10px;
  text-align: center;
}

.simple-landing-pricing-card.featured {
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid #FFD700;
}

.simple-landing-pricing-price {
  font-size: 2rem;
  font-weight: bold;
  color: #FFD700;
}

.simple-landing-pricing-features {
  list-style: none;
  padding: 0;
}

.simple-landing-pricing-button {
  width: 100%;
  padding: 1rem;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
}

.simple-landing-pricing-button.beta {
  background: #FF9800;
}

.simple-landing-pricing-button.live {
  background: #2196F3;
}

.simple-landing-footer {
  text-align: center;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  margin-top: 3rem;
}

.simple-landing-footer-contact {
  margin-top: 1rem;
}

.simple-landing-footer-contact span {
  margin: 0 1rem;
}

.simple-landing-footer-copyright {
  margin-top: 1rem;
  opacity: 0.7;
}

.simple-landing {
  min-height: 100vh;
  background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
  color: white;
  font-family: Arial, sans-serif;
  padding: 2rem;
}

.simple-landing-container {
  max-width: 1200px;
  margin: 0 auto;
}

.simple-landing-header {
  text-align: center;
  margin-bottom: 4rem;
}

.simple-landing-title {
  font-size: 3rem;
  margin-bottom: 1rem;
  font-weight: bold;
}

.simple-landing-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 2rem;
}

.simple-landing-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.simple-landing-btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
}

.simple-landing-btn-beta {
  background: #FF9800;
  color: white;
}

.simple-landing-btn-beta:hover {
  background: #F57C00;
  transform: translateY(-2px);
}

.simple-landing-btn-live {
  background: #2196F3;
  color: white;
}

.simple-landing-btn-live:hover {
  background: #1976D2;
  transform: translateY(-2px);
}

/* Features Section */
.simple-landing-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.simple-landing-feature {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 10px;
  text-align: center;
  transition: transform 0.3s ease;
}

.simple-landing-feature:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
}

.simple-landing-feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.simple-landing-feature h3 {
  margin: 0 0 1rem 0;
  font-size: 1.3rem;
}

.simple-landing-feature p {
  margin: 0;
  opacity: 0.9;
  line-height: 1.5;
}

/* Stats Section */
.simple-landing-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.simple-landing-stat {
  text-align: center;
}

.simple-landing-stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: #FFD700;
  margin-bottom: 0.5rem;
}

.simple-landing-stat-label {
  font-size: 1rem;
  opacity: 0.9;
}

/* Pricing Section */
.simple-landing-pricing {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.simple-landing-price-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 10px;
  text-align: center;
  transition: transform 0.3s ease;
}

.simple-landing-price-card:hover {
  transform: translateY(-5px);
}

.simple-landing-price-card.featured {
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid #FFD700;
}

.simple-landing-price-card h3 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
}

.simple-landing-price {
  font-size: 2rem;
  font-weight: bold;
  color: #FFD700;
  margin-bottom: 1rem;
}

.simple-landing-price-description {
  margin-bottom: 1.5rem;
  opacity: 0.9;
}

.simple-landing-price-features {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
}

.simple-landing-price-features li {
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 5px;
}

.simple-landing-price-button {
  width: 100%;
  padding: 1rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
}

.simple-landing-price-button.beta {
  background: #FF9800;
  color: white;
}

.simple-landing-price-button.beta:hover {
  background: #F57C00;
}

.simple-landing-price-button.live {
  background: #2196F3;
  color: white;
}

.simple-landing-price-button.live:hover {
  background: #1976D2;
}

/* Footer */
.simple-landing-footer {
  text-align: center;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  margin-top: 3rem;
}

.simple-landing-footer h3 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
}

.simple-landing-footer p {
  margin: 0 0 1rem 0;
  opacity: 0.9;
}

.simple-landing-footer-contact {
  margin-top: 1rem;
}

.simple-landing-footer-contact span {
  margin: 0 1rem;
  display: inline-block;
}

.simple-landing-footer-copyright {
  margin-top: 1rem;
  opacity: 0.7;
}

/* Responsive Design */
@media (max-width: 768px) {
  .simple-landing {
    padding: 1rem;
  }
  
  .simple-landing-title {
    font-size: 2rem;
  }
  
  .simple-landing-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .simple-landing-btn {
    width: 100%;
    max-width: 300px;
  }
  
  .simple-landing-footer-contact span {
    display: block;
    margin: 0.5rem 0;
  }
}
