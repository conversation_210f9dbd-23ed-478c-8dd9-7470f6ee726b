import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  alpha,
  useTheme
} from '@mui/material';
import {
  Lock,
  Upgrade,
  Star,
  CheckCircle,
  Cancel
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

interface AccessControlProps {
  children: React.ReactNode;
  requiredPermission?: string;
  requiredRole?: 'beta' | 'professional' | 'enterprise' | 'admin';
  module?: string;
  fallback?: React.ReactNode;
  showUpgradePrompt?: boolean;
}

interface UpgradePromptProps {
  module: string;
  currentRole: string;
  onUpgrade: () => void;
  onClose: () => void;
}

const UpgradePrompt: React.FC<UpgradePromptProps> = ({ 
  module, 
  currentRole, 
  onUpgrade, 
  onClose 
}) => {
  const theme = useTheme();

  const moduleFeatures: Record<string, { 
    name: string; 
    description: string; 
    features: string[];
    requiredPlan: string;
    savings: string;
  }> = {
    breeding: {
      name: 'Breeding Management',
      description: 'Advanced breeding optimization and genetic tracking',
      features: [
        'Breeding schedule optimization',
        'Genetic lineage tracking',
        'Pregnancy monitoring',
        'Birth predictions',
        'Breeding performance analytics'
      ],
      requiredPlan: 'Professional',
      savings: 'Increase breeding efficiency by 35%'
    },
    financial: {
      name: 'Financial Management',
      description: 'Complete financial tracking and ROI analysis',
      features: [
        'Profit & Loss analysis',
        'ROI calculations',
        'Tax optimization',
        'Banking integration',
        'Financial forecasting'
      ],
      requiredPlan: 'Professional',
      savings: 'Save R15,000+ annually in tax optimization'
    },
    inventory: {
      name: 'Inventory Management',
      description: 'Smart inventory tracking and automated ordering',
      features: [
        'Real-time inventory tracking',
        'Automated reorder points',
        'Supplier management',
        'Cost optimization',
        'Waste reduction analytics'
      ],
      requiredPlan: 'Professional',
      savings: 'Reduce inventory costs by 25%'
    },
    commercial: {
      name: 'Commercial Operations',
      description: 'Market analysis and commercial optimization',
      features: [
        'Market price tracking',
        'Auction integration',
        'Sales optimization',
        'Customer management',
        'Revenue forecasting'
      ],
      requiredPlan: 'Enterprise',
      savings: 'Increase sales revenue by 20%'
    },
    compliance: {
      name: 'Compliance Management',
      description: 'Regulatory compliance and certification tracking',
      features: [
        'Regulatory compliance tracking',
        'Certification management',
        'Audit trail maintenance',
        'Document management',
        'Compliance reporting'
      ],
      requiredPlan: 'Enterprise',
      savings: 'Avoid compliance penalties and fines'
    },
    analytics: {
      name: 'Advanced Analytics',
      description: 'AI-powered insights and predictive analytics',
      features: [
        'AI-powered insights',
        'Predictive analytics',
        'Performance benchmarking',
        'Custom dashboards',
        'Advanced reporting'
      ],
      requiredPlan: 'Enterprise',
      savings: 'Improve decision-making efficiency by 40%'
    }
  };

  const feature = moduleFeatures[module] || {
    name: 'Premium Feature',
    description: 'Advanced functionality for professional farmers',
    features: ['Enhanced capabilities', 'Advanced analytics', 'Priority support'],
    requiredPlan: 'Professional',
    savings: 'Unlock advanced features'
  };

  return (
    <Card
      sx={{
        maxWidth: 600,
        mx: 'auto',
        mt: 4,
        borderRadius: 4,
        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.secondary.main, 0.05)})`,
        border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
      }}
    >
      <CardContent sx={{ p: 4 }}>
        <Box sx={{ textAlign: 'center', mb: 3 }}>
          <Box
            sx={{
              display: 'inline-flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: 80,
              height: 80,
              borderRadius: '50%',
              background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
              mb: 2
            }}
          >
            <Lock sx={{ fontSize: 40, color: 'white' }} />
          </Box>
          
          <Typography variant="h4" fontWeight="bold" sx={{ mb: 1 }}>
            {feature.name}
          </Typography>
          
          <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
            {feature.description}
          </Typography>

          <Chip
            label={`Requires ${feature.requiredPlan} Plan`}
            color="primary"
            sx={{ mb: 2 }}
          />
        </Box>

        <Alert severity="success" sx={{ mb: 3 }}>
          <Typography variant="body2" fontWeight="bold">
            {feature.savings}
          </Typography>
        </Alert>

        <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
          What you'll get:
        </Typography>

        <Grid container spacing={1} sx={{ mb: 3 }}>
          {feature.features.map((item, index) => (
            <Grid item xs={12} key={index}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CheckCircle sx={{ color: 'success.main', fontSize: 20 }} />
                <Typography variant="body2">{item}</Typography>
              </Box>
            </Grid>
          ))}
        </Grid>

        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
          <Button
            variant="contained"
            size="large"
            startIcon={<Upgrade />}
            onClick={onUpgrade}
            sx={{
              px: 4,
              py: 1.5,
              borderRadius: 3,
              background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
            }}
          >
            Upgrade Now
          </Button>
          
          <Button
            variant="outlined"
            size="large"
            onClick={onClose}
            sx={{ px: 4, py: 1.5, borderRadius: 3 }}
          >
            Maybe Later
          </Button>
        </Box>

        <Typography variant="caption" color="text.secondary" sx={{ display: 'block', textAlign: 'center', mt: 2 }}>
          14-day free trial • No credit card required • Cancel anytime
        </Typography>
      </CardContent>
    </Card>
  );
};

const AccessControl: React.FC<AccessControlProps> = ({
  children,
  requiredPermission,
  requiredRole,
  module,
  fallback,
  showUpgradePrompt = true
}) => {
  const { 
    user, 
    hasPermission, 
    canAccessModule, 
    getAccessLevel,
    isBetaUser,
    isAdminUser 
  } = useAuth();
  const navigate = useNavigate();
  const [showUpgrade, setShowUpgrade] = React.useState(false);

  // Check if user has access
  const hasAccess = React.useMemo(() => {
    if (!user) return false;

    // Check permission-based access
    if (requiredPermission && !hasPermission(requiredPermission)) {
      return false;
    }

    // Check role-based access
    if (requiredRole) {
      const userLevel = getAccessLevel();
      const roleHierarchy = ['beta', 'professional', 'enterprise', 'admin'];
      const requiredIndex = roleHierarchy.indexOf(requiredRole);
      const userIndex = roleHierarchy.indexOf(userLevel);
      
      if (userIndex < requiredIndex) {
        return false;
      }
    }

    // Check module-based access
    if (module && !canAccessModule(module)) {
      return false;
    }

    return true;
  }, [user, requiredPermission, requiredRole, module, hasPermission, canAccessModule, getAccessLevel]);

  const handleUpgrade = () => {
    navigate('/pricing');
    setShowUpgrade(false);
  };

  // If user has access, render children
  if (hasAccess) {
    return <>{children}</>;
  }

  // If custom fallback is provided, use it
  if (fallback) {
    return <>{fallback}</>;
  }

  // If upgrade prompt is disabled, show simple access denied
  if (!showUpgradePrompt) {
    return (
      <Alert severity="warning" sx={{ m: 2 }}>
        <Typography variant="body1" fontWeight="bold">
          Access Denied
        </Typography>
        <Typography variant="body2">
          You don't have permission to access this feature.
        </Typography>
      </Alert>
    );
  }

  // Show upgrade prompt for beta users
  if (isBetaUser() && module) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <UpgradePrompt
          module={module}
          currentRole={getAccessLevel()}
          onUpgrade={handleUpgrade}
          onClose={() => navigate('/dashboard')}
        />
      </motion.div>
    );
  }

  // Default access denied message
  return (
    <Alert severity="error" sx={{ m: 2 }}>
      <Typography variant="body1" fontWeight="bold">
        Access Denied
      </Typography>
      <Typography variant="body2">
        You don't have sufficient permissions to access this feature.
      </Typography>
    </Alert>
  );
};

export default AccessControl;
