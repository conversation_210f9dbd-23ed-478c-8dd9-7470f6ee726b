/* Landing Page Component Styles */

/* Basic Landing Page Styles */
.basic-landing-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #2E7D32 0%, #1B5E20 100%);
  color: white;
  padding: 2rem;
}

.basic-landing-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}

.basic-landing-title {
  margin: 0;
  font-size: 2rem;
}

.basic-landing-nav-button {
  padding: 0.8rem 1.5rem;
  margin: 0 0.5rem;
  color: white;
  border: none;
  border-radius: 5px;
  text-decoration: none;
  font-size: 1rem;
  font-weight: bold;
  display: inline-block;
}

.basic-landing-nav-button.beta {
  background: #FF9800;
}

.basic-landing-nav-button.live {
  background: #2196F3;
}

/* Button styles for interactive buttons */
.landing-button {
  padding: 0.8rem 1.5rem;
  margin: 0 0.5rem;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
}

.landing-button.beta {
  background: #FF9800;
}

.landing-button.live {
  background: #2196F3;
}

.landing-cta-button-large {
  padding: 1.5rem 3rem;
  margin: 1rem;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-size: 1.3rem;
  font-weight: bold;
}

.landing-cta-button-large.beta {
  background: #FF9800;
  color: white;
}

.landing-cta-button-large.live-outline {
  background: transparent;
  color: white;
  border: 2px solid white;
}

.landing-pricing-button-full {
  width: 100%;
  padding: 1rem;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
}

.landing-pricing-button-full.beta {
  background: #FF9800;
}

.landing-pricing-button-full.live {
  background: #2196F3;
}

.basic-landing-main {
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
}

.basic-landing-hero-title {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.basic-landing-hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 3rem;
  opacity: 0.9;
}

.basic-landing-action-buttons {
  margin-bottom: 4rem;
}

.basic-landing-cta-button {
  padding: 1.5rem 3rem;
  margin: 1rem;
  border: none;
  border-radius: 10px;
  text-decoration: none;
  font-size: 1.3rem;
  font-weight: bold;
  display: inline-block;
}

.basic-landing-cta-button.primary {
  background: #FF9800;
  color: white;
}

.basic-landing-cta-button.secondary {
  background: transparent;
  color: white;
  border: 2px solid white;
}

.basic-landing-features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.basic-landing-feature-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 10px;
  text-align: center;
}

.basic-landing-feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.basic-landing-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.basic-landing-stat-item {
  text-align: center;
}

.basic-landing-stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: #FFD700;
}

.basic-landing-pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.basic-landing-pricing-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 10px;
  text-align: center;
}

.basic-landing-pricing-card.featured {
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid #FFD700;
}

.basic-landing-pricing-price {
  font-size: 2rem;
  font-weight: bold;
  color: #FFD700;
}

.basic-landing-pricing-features {
  list-style: none;
  padding: 0;
}

.basic-landing-pricing-button {
  width: 80%;
  padding: 1rem;
  color: white;
  border: none;
  border-radius: 5px;
  text-decoration: none;
  font-weight: bold;
  display: inline-block;
  margin-top: 1rem;
}

.basic-landing-pricing-button.beta {
  background: #FF9800;
}

.basic-landing-pricing-button.live {
  background: #2196F3;
}

.basic-landing-footer {
  text-align: center;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  margin-top: 3rem;
}

.basic-landing-footer-contact {
  margin-top: 1rem;
}

.basic-landing-footer-contact span {
  margin: 0 1rem;
}

.basic-landing-footer-copyright {
  margin-top: 1rem;
  opacity: 0.7;
}

/* Simple Landing Page Styles */
.simple-landing-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #2E7D32 0%, #1B5E20 100%);
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 2rem;
}

.simple-landing-title {
  font-size: 4rem;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.simple-landing-subtitle {
  font-size: 1.5rem;
  margin-bottom: 3rem;
  opacity: 0.9;
}

.simple-landing-buttons {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
  justify-content: center;
}

.simple-landing-button {
  padding: 1.5rem 3rem;
  font-size: 1.3rem;
  font-weight: bold;
  border: none;
  border-radius: 10px;
  text-decoration: none;
  display: inline-block;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.simple-landing-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.simple-landing-button.beta {
  background: #FF9800;
  color: white;
}

.simple-landing-button.live {
  background: #2196F3;
  color: white;
}

/* Simple Login Page Styles */
.simple-login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #2E7D32 0%, #1B5E20 100%);
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

.simple-login-form {
  background: rgba(255, 255, 255, 0.1);
  padding: 3rem;
  border-radius: 15px;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  width: 100%;
  max-width: 400px;
}

/* Final Landing Page Styles */
.final-landing-hero-section {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #2E7D32 0%, #1B5E20 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem;
}

.final-landing-hero-content {
  max-width: 1200px;
  z-index: 2;
}

.final-landing-hero-title {
  font-size: 4rem;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.final-landing-hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 3rem;
  opacity: 0.9;
}

.final-landing-cta-buttons {
  display: flex;
  gap: 2rem;
  justify-content: center;
  flex-wrap: wrap;
}

.final-landing-cta-button {
  padding: 1.5rem 3rem;
  font-size: 1.3rem;
  font-weight: bold;
  border: none;
  border-radius: 10px;
  text-decoration: none;
  display: inline-block;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.final-landing-cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.final-landing-cta-button.beta {
  background: #FF9800;
  color: white;
}

.final-landing-cta-button.live {
  background: #2196F3;
  color: white;
}
