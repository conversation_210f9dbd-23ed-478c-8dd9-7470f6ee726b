import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import '../../styles/LoginPage.css';

const Register: React.FC = () => {
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const navigate = useNavigate();

    const handleRegister = (e: React.FormEvent) => {
        e.preventDefault();
        if (password !== confirmPassword) {
            alert("Passwords don't match!");
            return;
        }
        // In a real app, you'd have registration logic here.
        console.log('Attempting Registration with:', { email, password });
        // For simulation, navigate to the beta login page after registration.
        navigate('/login/beta');
    };

    return (
        <div className="login-page">
            <div className="login-container">
                <h1>Register for AgriIntel</h1>
                <p>Create your account to get started.</p>
                <form className="login-form" onSubmit={handleRegister}>
                    <div className="form-group">
                        <label htmlFor="email">Email</label>
                        <input
                            type="email"
                            id="email"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            required
                        />
                    </div>
                    <div className="form-group">
                        <label htmlFor="password">Password</label>
                        <input
                            type="password"
                            id="password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            required
                        />
                    </div>
                    <div className="form-group">
                        <label htmlFor="confirm-password">Confirm Password</label>
                        <input
                            type="password"
                            id="confirm-password"
                            value={confirmPassword}
                            onChange={(e) => setConfirmPassword(e.target.value)}
                            required
                        />
                    </div>
                    <button type="submit" className="login-button">Register</button>
                </form>
            </div>
        </div>
    );
};

export default Register;