import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
} from '@mui/material';
import {
  Add,
  Download,
  TrendingUp,
  TrendingDown,
  AccountBalance,
  Receipt,
  PieChart,
  Upgrade,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import * as XLSX from 'xlsx';

interface FinancialRecord {
  id: string;
  date: string;
  type: 'income' | 'expense';
  category: string;
  description: string;
  amount: number;
  relatedTo?: string;
}

const BetaFinancialModule: React.FC = () => {
  const [records, setRecords] = useState<FinancialRecord[]>([]);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [newRecord, setNewRecord] = useState<Partial<FinancialRecord>>({
    type: 'expense',
    category: '',
    description: '',
    amount: 0,
    date: new Date().toISOString().split('T')[0],
  });

  // Sample data for BETA demonstration
  useEffect(() => {
    const sampleData: FinancialRecord[] = [
      {
        id: '1',
        date: '2024-01-15',
        type: 'expense',
        category: 'Feed',
        description: 'Cattle feed purchase',
        amount: 2500,
        relatedTo: 'Feed Management'
      },
      {
        id: '2',
        date: '2024-01-10',
        type: 'income',
        category: 'Milk Sales',
        description: 'Weekly milk sales',
        amount: 4200,
      },
      {
        id: '3',
        date: '2024-01-08',
        type: 'expense',
        category: 'Veterinary',
        description: 'Vaccination costs',
        amount: 800,
        relatedTo: 'Health Management'
      },
    ];
    setRecords(sampleData);
  }, []);

  const handleAddRecord = () => {
    if (newRecord.category && newRecord.description && newRecord.amount) {
      const record: FinancialRecord = {
        id: Date.now().toString(),
        date: newRecord.date || new Date().toISOString().split('T')[0],
        type: newRecord.type || 'expense',
        category: newRecord.category,
        description: newRecord.description,
        amount: Number(newRecord.amount),
        relatedTo: newRecord.relatedTo,
      };
      setRecords([...records, record]);
      setNewRecord({
        type: 'expense',
        category: '',
        description: '',
        amount: 0,
        date: new Date().toISOString().split('T')[0],
      });
      setShowAddDialog(false);
    }
  };

  const exportToExcel = () => {
    const worksheet = XLSX.utils.json_to_sheet(records);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Financial Records');
    XLSX.writeFile(workbook, 'AgriIntel_Financial_Report.xlsx');
  };

  const totalIncome = records.filter(r => r.type === 'income').reduce((sum, r) => sum + r.amount, 0);
  const totalExpenses = records.filter(r => r.type === 'expense').reduce((sum, r) => sum + r.amount, 0);
  const netProfit = totalIncome - totalExpenses;

  const categories = [...new Set(records.map(r => r.category))];
  const expensesByCategory = categories.map(cat => ({
    category: cat,
    amount: records.filter(r => r.category === cat && r.type === 'expense').reduce((sum, r) => sum + r.amount, 0)
  })).filter(item => item.amount > 0);

  return (
    <Box sx={{ p: 3 }}>
      {/* BETA Limitation Alert */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>BETA Financial Module:</strong> Limited to basic income/expense tracking and simple Excel reports. 
          Upgrade to Professional for advanced analytics, budgeting, and forecasting.
        </Typography>
      </Alert>

      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" fontWeight="bold">
          Financial Management (BETA)
        </Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => setShowAddDialog(true)}
            sx={{ borderRadius: 2 }}
          >
            Add Record
          </Button>
          <Button
            variant="outlined"
            startIcon={<Download />}
            onClick={exportToExcel}
            sx={{ borderRadius: 2 }}
          >
            Export Excel
          </Button>
        </Box>
      </Box>

      {/* Financial Summary Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={3}>
          <motion.div whileHover={{ y: -4 }}>
            <Card sx={{ borderRadius: 3, background: 'linear-gradient(135deg, #4CAF50, #66BB6A)' }}>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" color="white" fontWeight="bold">
                      R{totalIncome.toLocaleString()}
                    </Typography>
                    <Typography variant="body2" color="rgba(255,255,255,0.8)">
                      Total Income
                    </Typography>
                  </Box>
                  <TrendingUp sx={{ fontSize: 40, color: 'rgba(255,255,255,0.8)' }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={3}>
          <motion.div whileHover={{ y: -4 }}>
            <Card sx={{ borderRadius: 3, background: 'linear-gradient(135deg, #F44336, #EF5350)' }}>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" color="white" fontWeight="bold">
                      R{totalExpenses.toLocaleString()}
                    </Typography>
                    <Typography variant="body2" color="rgba(255,255,255,0.8)">
                      Total Expenses
                    </Typography>
                  </Box>
                  <TrendingDown sx={{ fontSize: 40, color: 'rgba(255,255,255,0.8)' }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={3}>
          <motion.div whileHover={{ y: -4 }}>
            <Card sx={{ 
              borderRadius: 3, 
              background: netProfit >= 0 
                ? 'linear-gradient(135deg, #2196F3, #42A5F5)' 
                : 'linear-gradient(135deg, #FF9800, #FFB74D)'
            }}>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" color="white" fontWeight="bold">
                      R{netProfit.toLocaleString()}
                    </Typography>
                    <Typography variant="body2" color="rgba(255,255,255,0.8)">
                      Net Profit
                    </Typography>
                  </Box>
                  <AccountBalance sx={{ fontSize: 40, color: 'rgba(255,255,255,0.8)' }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={3}>
          <motion.div whileHover={{ y: -4 }}>
            <Card sx={{ borderRadius: 3, background: 'linear-gradient(135deg, #9C27B0, #BA68C8)' }}>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" color="white" fontWeight="bold">
                      {records.length}
                    </Typography>
                    <Typography variant="body2" color="rgba(255,255,255,0.8)">
                      Total Records
                    </Typography>
                  </Box>
                  <Receipt sx={{ fontSize: 40, color: 'rgba(255,255,255,0.8)' }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
      </Grid>

      {/* Simple Expense Breakdown */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={6}>
          <Card sx={{ borderRadius: 3, height: '100%' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" mb={2}>
                Expense Breakdown by Category
              </Typography>
              {expensesByCategory.map((item, index) => (
                <Box key={index} mb={2}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2">{item.category}</Typography>
                    <Typography variant="body2" fontWeight="bold">
                      R{item.amount.toLocaleString()}
                    </Typography>
                  </Box>
                  <Box
                    sx={{
                      height: 8,
                      backgroundColor: '#f0f0f0',
                      borderRadius: 4,
                      overflow: 'hidden'
                    }}
                  >
                    <Box
                      sx={{
                        height: '100%',
                        width: `${(item.amount / totalExpenses) * 100}%`,
                        backgroundColor: ['#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336'][index % 5],
                        borderRadius: 4,
                      }}
                    />
                  </Box>
                </Box>
              ))}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card sx={{ borderRadius: 3, height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="between" mb={2}>
                <Typography variant="h6" fontWeight="bold">
                  Upgrade to Professional
                </Typography>
                <Upgrade sx={{ color: '#FF9800' }} />
              </Box>
              <Typography variant="body2" color="text.secondary" mb={2}>
                Unlock advanced financial features:
              </Typography>
              <Box component="ul" sx={{ pl: 2, mb: 2 }}>
                <Typography component="li" variant="body2" mb={1}>
                  Advanced Analytics & Forecasting
                </Typography>
                <Typography component="li" variant="body2" mb={1}>
                  Budget Planning & Tracking
                </Typography>
                <Typography component="li" variant="body2" mb={1}>
                  Profit/Loss Analysis
                </Typography>
                <Typography component="li" variant="body2" mb={1}>
                  Custom Financial Reports
                </Typography>
              </Box>
              <Button
                variant="contained"
                fullWidth
                sx={{
                  background: 'linear-gradient(135deg, #FF9800, #FFB74D)',
                  borderRadius: 2,
                }}
              >
                Upgrade Now - R299/month
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Recent Records Table */}
      <Card sx={{ borderRadius: 3 }}>
        <CardContent>
          <Typography variant="h6" fontWeight="bold" mb={2}>
            Recent Financial Records
          </Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Date</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Category</TableCell>
                  <TableCell>Description</TableCell>
                  <TableCell align="right">Amount</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {records.slice(-10).reverse().map((record) => (
                  <TableRow key={record.id}>
                    <TableCell>{record.date}</TableCell>
                    <TableCell>
                      <Chip
                        label={record.type}
                        size="small"
                        color={record.type === 'income' ? 'success' : 'error'}
                      />
                    </TableCell>
                    <TableCell>{record.category}</TableCell>
                    <TableCell>{record.description}</TableCell>
                    <TableCell align="right">
                      <Typography
                        variant="body2"
                        fontWeight="bold"
                        color={record.type === 'income' ? 'success.main' : 'error.main'}
                      >
                        {record.type === 'income' ? '+' : '-'}R{record.amount.toLocaleString()}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Add Record Dialog */}
      <Dialog open={showAddDialog} onClose={() => setShowAddDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add Financial Record</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Type</InputLabel>
                <Select
                  value={newRecord.type || 'expense'}
                  onChange={(e) => setNewRecord({ ...newRecord, type: e.target.value as 'income' | 'expense' })}
                  label="Type"
                >
                  <MenuItem value="income">Income</MenuItem>
                  <MenuItem value="expense">Expense</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Date"
                type="date"
                value={newRecord.date}
                onChange={(e) => setNewRecord({ ...newRecord, date: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Category"
                value={newRecord.category}
                onChange={(e) => setNewRecord({ ...newRecord, category: e.target.value })}
                placeholder="e.g., Feed, Veterinary, Milk Sales"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                value={newRecord.description}
                onChange={(e) => setNewRecord({ ...newRecord, description: e.target.value })}
                placeholder="Brief description of the transaction"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Amount (R)"
                type="number"
                value={newRecord.amount}
                onChange={(e) => setNewRecord({ ...newRecord, amount: Number(e.target.value) })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowAddDialog(false)}>Cancel</Button>
          <Button variant="contained" onClick={handleAddRecord}>
            Add Record
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BetaFinancialModule;
