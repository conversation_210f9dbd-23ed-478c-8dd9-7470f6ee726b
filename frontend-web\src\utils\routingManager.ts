/**
 * Comprehensive Routing Manager for AgriIntel
 * Handles route conflicts, navigation guards, and proper route protection
 */

import { NavigateFunction } from 'react-router-dom';
import { User } from '../types/user';
import { validateRouteAccess, isBetaUser } from './enhancedAccessControl';

export interface RouteConfig {
  path: string;
  betaPath?: string;
  livePath?: string;
  requiresAuth: boolean;
  allowedRoles: string[];
  redirects?: {
    unauthorized?: string;
    beta?: string;
    live?: string;
  };
}

// Comprehensive route configurations
export const routeConfigurations: Record<string, RouteConfig> = {
  // Landing and Auth Routes
  landing: {
    path: '/',
    requiresAuth: false,
    allowedRoles: ['*']
  },
  
  betaLogin: {
    path: '/beta-login',
    requiresAuth: false,
    allowedRoles: ['*']
  },
  
  liveLogin: {
    path: '/login',
    requiresAuth: false,
    allowedRoles: ['*']
  },
  
  register: {
    path: '/register',
    requiresAuth: false,
    allowedRoles: ['*']
  },

  // Dashboard Routes
  betaDashboard: {
    path: '/beta-dashboard',
    requiresAuth: true,
    allowedRoles: ['beta'],
    redirects: {
      unauthorized: '/beta-login',
      live: '/dashboard'
    }
  },
  
  liveDashboard: {
    path: '/dashboard',
    requiresAuth: true,
    allowedRoles: ['professional', 'enterprise', 'admin'],
    redirects: {
      unauthorized: '/login',
      beta: '/beta-dashboard'
    }
  },

  // Module Routes (with BETA/Live separation)
  animals: {
    path: '/animals',
    betaPath: '/beta-dashboard/animals',
    livePath: '/dashboard/animals',
    requiresAuth: true,
    allowedRoles: ['beta', 'professional', 'enterprise', 'admin']
  },
  
  health: {
    path: '/health',
    betaPath: '/beta-dashboard/health',
    livePath: '/dashboard/health',
    requiresAuth: true,
    allowedRoles: ['beta', 'professional', 'enterprise', 'admin']
  },
  
  resources: {
    path: '/resources',
    betaPath: '/beta-dashboard/resources',
    livePath: '/dashboard/resources',
    requiresAuth: true,
    allowedRoles: ['beta', 'professional', 'enterprise', 'admin']
  },
  
  breeding: {
    path: '/breeding',
    betaPath: null, // Not available in BETA
    livePath: '/dashboard/breeding',
    requiresAuth: true,
    allowedRoles: ['professional', 'enterprise', 'admin'],
    redirects: {
      beta: '/beta-dashboard?upgrade=breeding'
    }
  },
  
  financial: {
    path: '/financial',
    betaPath: '/beta-dashboard/financial', // Now available in BETA with limitations
    livePath: '/dashboard/financial',
    requiresAuth: true,
    allowedRoles: ['beta', 'professional', 'enterprise', 'admin']
  },

  feeding: {
    path: '/feeding',
    betaPath: '/beta-dashboard/feeding', // Now available in BETA with limitations
    livePath: '/dashboard/feeding',
    requiresAuth: true,
    allowedRoles: ['beta', 'professional', 'enterprise', 'admin']
  },
  
  inventory: {
    path: '/inventory',
    betaPath: null,
    livePath: '/dashboard/inventory',
    requiresAuth: true,
    allowedRoles: ['professional', 'enterprise', 'admin'],
    redirects: {
      beta: '/beta-dashboard?upgrade=inventory'
    }
  },
  
  commercial: {
    path: '/commercial',
    betaPath: null,
    livePath: '/dashboard/commercial',
    requiresAuth: true,
    allowedRoles: ['enterprise', 'admin'],
    redirects: {
      beta: '/beta-dashboard?upgrade=commercial'
    }
  },
  
  analytics: {
    path: '/analytics',
    betaPath: null,
    livePath: '/dashboard/analytics',
    requiresAuth: true,
    allowedRoles: ['enterprise', 'admin'],
    redirects: {
      beta: '/beta-dashboard?upgrade=analytics'
    }
  },
  
  compliance: {
    path: '/compliance',
    betaPath: null,
    livePath: '/dashboard/compliance',
    requiresAuth: true,
    allowedRoles: ['enterprise', 'admin'],
    redirects: {
      beta: '/beta-dashboard?upgrade=compliance'
    }
  }
};

/**
 * Resolve route conflicts and get the correct path for user
 */
export const resolveRoute = (
  requestedPath: string, 
  user: User | null
): { path: string; shouldRedirect: boolean; reason?: string } => {
  
  // Normalize path
  const normalizedPath = requestedPath.replace(/\/+/g, '/').replace(/\/$/, '') || '/';
  
  // Handle root path
  if (normalizedPath === '/') {
    if (!user) {
      return { path: '/', shouldRedirect: false };
    }
    
    // Redirect authenticated users to appropriate dashboard
    const targetPath = isBetaUser(user) ? '/beta-dashboard' : '/dashboard';
    return { 
      path: targetPath, 
      shouldRedirect: true, 
      reason: 'Redirecting authenticated user to dashboard' 
    };
  }

  // Check for direct route matches
  for (const [routeKey, config] of Object.entries(routeConfigurations)) {
    if (normalizedPath === config.path || 
        normalizedPath === config.betaPath || 
        normalizedPath === config.livePath) {
      
      // Validate access
      const accessValidation = validateRouteAccess(user, normalizedPath);
      
      if (!accessValidation.allowed && accessValidation.redirectTo) {
        return {
          path: accessValidation.redirectTo,
          shouldRedirect: true,
          reason: accessValidation.reason
        };
      }
      
      return { path: normalizedPath, shouldRedirect: false };
    }
  }

  // Handle module-specific routing
  const pathSegments = normalizedPath.split('/').filter(Boolean);
  
  if (pathSegments.length >= 2) {
    const moduleSegment = pathSegments[pathSegments.length - 1];
    const config = routeConfigurations[moduleSegment];
    
    if (config) {
      const userIsBeta = isBetaUser(user);
      
      // Determine correct path based on user type
      if (userIsBeta && config.betaPath) {
        return { 
          path: config.betaPath, 
          shouldRedirect: normalizedPath !== config.betaPath,
          reason: 'Redirecting to BETA version'
        };
      } else if (!userIsBeta && config.livePath) {
        return { 
          path: config.livePath, 
          shouldRedirect: normalizedPath !== config.livePath,
          reason: 'Redirecting to Live version'
        };
      } else if (userIsBeta && !config.betaPath && config.redirects?.beta) {
        return {
          path: config.redirects.beta,
          shouldRedirect: true,
          reason: 'Module not available in BETA'
        };
      }
    }
  }

  // Default handling for unmatched routes
  if (!user) {
    return { 
      path: '/beta-login', 
      shouldRedirect: true, 
      reason: 'Authentication required' 
    };
  }

  const userIsBeta = isBetaUser(user);
  const fallbackPath = userIsBeta ? '/beta-dashboard' : '/dashboard';
  
  return { 
    path: fallbackPath, 
    shouldRedirect: true, 
    reason: 'Route not found, redirecting to dashboard' 
  };
};

/**
 * Navigation helper with conflict resolution
 */
export const navigateWithGuards = (
  navigate: NavigateFunction,
  targetPath: string,
  user: User | null,
  options?: { replace?: boolean; state?: any }
) => {
  const resolution = resolveRoute(targetPath, user);
  
  if (resolution.shouldRedirect) {
    console.log(`Navigation: ${targetPath} -> ${resolution.path} (${resolution.reason})`);
  }
  
  navigate(resolution.path, {
    replace: resolution.shouldRedirect || options?.replace,
    state: options?.state
  });
};

/**
 * Get navigation menu items based on user access
 */
export const getNavigationItems = (user: User | null) => {
  const userIsBeta = isBetaUser(user);
  const items = [];

  // Dashboard
  items.push({
    id: 'dashboard',
    name: 'Dashboard',
    path: userIsBeta ? '/beta-dashboard' : '/dashboard',
    icon: 'dashboard',
    available: true
  });

  // Core modules (available to all)
  const coreModules = ['animals', 'health', 'resources'];
  coreModules.forEach(module => {
    const config = routeConfigurations[module];
    if (config) {
      items.push({
        id: module,
        name: module.charAt(0).toUpperCase() + module.slice(1),
        path: userIsBeta ? config.betaPath : config.livePath,
        icon: module,
        available: true
      });
    }
  });

  // BETA modules (now available in BETA with limitations)
  if (userIsBeta) {
    const betaModules = ['financial', 'feeding'];
    betaModules.forEach(module => {
      const config = routeConfigurations[module];
      if (config && config.betaPath) {
        items.push({
          id: module,
          name: module.charAt(0).toUpperCase() + module.slice(1) + ' (BETA)',
          path: config.betaPath,
          icon: module,
          available: true,
          isBeta: true
        });
      }
    });
  }

  // Premium modules (not available to BETA users)
  if (!userIsBeta) {
    const premiumModules = ['breeding', 'financial', 'inventory'];
    premiumModules.forEach(module => {
      const config = routeConfigurations[module];
      if (config && config.livePath) {
        items.push({
          id: module,
          name: module.charAt(0).toUpperCase() + module.slice(1),
          path: config.livePath,
          icon: module,
          available: true
        });
      }
    });

    // Enterprise modules
    if (user && ['enterprise', 'admin'].includes(user.role)) {
      const enterpriseModules = ['commercial', 'analytics', 'compliance'];
      enterpriseModules.forEach(module => {
        const config = routeConfigurations[module];
        if (config && config.livePath) {
          items.push({
            id: module,
            name: module.charAt(0).toUpperCase() + module.slice(1),
            path: config.livePath,
            icon: module,
            available: true
          });
        }
      });
    }
  }

  return items.filter(item => item.path); // Remove items without valid paths
};

/**
 * Check if current route is valid for user
 */
export const isValidRoute = (currentPath: string, user: User | null): boolean => {
  const resolution = resolveRoute(currentPath, user);
  return !resolution.shouldRedirect;
};

/**
 * Get breadcrumb navigation
 */
export const getBreadcrumbs = (currentPath: string, user: User | null) => {
  const pathSegments = currentPath.split('/').filter(Boolean);
  const breadcrumbs = [];
  
  // Add dashboard root
  const userIsBeta = isBetaUser(user);
  breadcrumbs.push({
    name: 'Dashboard',
    path: userIsBeta ? '/beta-dashboard' : '/dashboard'
  });

  // Add module breadcrumbs
  if (pathSegments.length > 1) {
    const module = pathSegments[pathSegments.length - 1];
    const config = routeConfigurations[module];
    
    if (config) {
      breadcrumbs.push({
        name: module.charAt(0).toUpperCase() + module.slice(1),
        path: currentPath
      });
    }
  }

  return breadcrumbs;
};

export default {
  routeConfigurations,
  resolveRoute,
  navigateWithGuards,
  getNavigationItems,
  isValidRoute,
  getBreadcrumbs
};
