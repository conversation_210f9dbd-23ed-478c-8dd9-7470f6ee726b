import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { validateRouteAccess } from '../../utils/enhancedAccessControl';

interface LiveRouteGuardProps {
  children: React.ReactNode;
}

const LiveRouteGuard: React.FC<LiveRouteGuardProps> = ({ children }) => {
  const { user } = useAuth();
  const location = useLocation();

  // Validate route access using enhanced access control
  const accessValidation = validateRouteAccess(user, location.pathname);

  // If access is denied, redirect appropriately
  if (!accessValidation.allowed) {
    if (accessValidation.redirectTo) {
      return <Navigate to={accessValidation.redirectTo} replace />;
    }

    // Default fallback
    return <Navigate to="/login" replace />;
  }

  // Access granted - render children
  return <>{children}</>;
};

export default LiveRouteGuard;
