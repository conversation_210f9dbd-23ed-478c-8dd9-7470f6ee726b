/**
 * Unified Theme Context for AgriIntel
 * Consolidates all theme systems into a single, consistent provider
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { ThemeProvider as MuiThemeProvider, createTheme, Theme } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { 
  getUnifiedTheme, 
  applyUnifiedThemeToMui, 
  getModuleStyles, 
  applyThemeToDocument,
  UnifiedTheme 
} from '../utils/unifiedThemeSystem';

export type ThemeMode = 'light' | 'dark' | 'auto';
export type ThemeName = 'emerald' | 'ocean' | 'sunset' | 'purple' | 'forest' | 'crimson';

interface UnifiedThemeContextType {
  // Current theme state
  themeName: ThemeName;
  themeMode: ThemeMode;
  unifiedTheme: UnifiedTheme;
  muiTheme: Theme;
  
  // Theme actions
  setThemeName: (name: ThemeName) => void;
  setThemeMode: (mode: ThemeMode) => void;
  toggleMode: () => void;
  
  // Module-specific styling
  getModuleStyles: (module: string) => any;
  
  // Utility functions
  applyToDocument: () => void;
}

const UnifiedThemeContext = createContext<UnifiedThemeContextType | undefined>(undefined);

export const useUnifiedTheme = () => {
  const context = useContext(UnifiedThemeContext);
  if (context === undefined) {
    throw new Error('useUnifiedTheme must be used within a UnifiedThemeProvider');
  }
  return context;
};

interface UnifiedThemeProviderProps {
  children: ReactNode;
}

export const UnifiedThemeProvider: React.FC<UnifiedThemeProviderProps> = ({ children }) => {
  // Load saved preferences
  const [themeName, setThemeNameState] = useState<ThemeName>(() => {
    const saved = localStorage.getItem('agri-theme-name');
    return (saved as ThemeName) || 'emerald';
  });

  const [themeMode, setThemeModeState] = useState<ThemeMode>(() => {
    const saved = localStorage.getItem('agri-theme-mode');
    if (saved === 'auto') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return (saved as ThemeMode) || 'light';
  });

  // Generate unified theme
  const unifiedTheme = getUnifiedTheme(themeName);

  // Create MUI theme
  const baseMuiTheme = createTheme({
    palette: {
      mode: themeMode,
      primary: {
        main: unifiedTheme.colors.primary,
        dark: unifiedTheme.colors.primaryDark,
        light: unifiedTheme.colors.primaryLight,
        contrastText: unifiedTheme.colors.text,
      },
      secondary: {
        main: unifiedTheme.colors.secondary,
        dark: unifiedTheme.colors.secondaryDark,
        light: unifiedTheme.colors.secondaryLight,
        contrastText: unifiedTheme.colors.text,
      },
      background: {
        default: themeMode === 'dark' ? '#121212' : '#f5f5f5',
        paper: themeMode === 'dark' ? '#1e1e1e' : '#ffffff',
      },
      text: {
        primary: themeMode === 'dark' ? '#ffffff' : '#1e293b',
        secondary: themeMode === 'dark' ? 'rgba(255, 255, 255, 0.7)' : '#64748b',
      },
    },
    typography: {
      fontFamily: '"Inter", "Roboto", "Helvetica Neue", Arial, sans-serif',
      h1: {
        fontFamily: '"Poppins", "Inter", sans-serif',
        fontWeight: 700,
      },
      h2: {
        fontFamily: '"Poppins", "Inter", sans-serif',
        fontWeight: 600,
      },
      h3: {
        fontFamily: '"Poppins", "Inter", sans-serif',
        fontWeight: 600,
      },
      h4: {
        fontFamily: '"Poppins", "Inter", sans-serif',
        fontWeight: 500,
      },
      h5: {
        fontFamily: '"Poppins", "Inter", sans-serif',
        fontWeight: 500,
      },
      h6: {
        fontFamily: '"Poppins", "Inter", sans-serif',
        fontWeight: 500,
      },
    },
    shape: {
      borderRadius: 12,
    },
    components: {
      MuiCard: {
        styleOverrides: {
          root: {
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(8px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
            transition: 'all 0.3s ease',
            '&:hover': {
              transform: 'translateY(-4px)',
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
            },
          },
        },
      },
      MuiButton: {
        styleOverrides: {
          root: {
            borderRadius: 8,
            textTransform: 'none',
            fontWeight: 600,
            transition: 'all 0.3s ease',
            '&:hover': {
              transform: 'translateY(-2px)',
            },
          },
          contained: {
            background: `linear-gradient(135deg, ${unifiedTheme.colors.primary}, ${unifiedTheme.colors.primaryDark})`,
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            '&:hover': {
              background: `linear-gradient(135deg, ${unifiedTheme.colors.primaryDark}, ${unifiedTheme.colors.primary})`,
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
            },
          },
        },
      },
      MuiTextField: {
        styleOverrides: {
          root: {
            '& .MuiOutlinedInput-root': {
              borderRadius: 8,
              background: 'rgba(255, 255, 255, 0.9)',
              transition: 'all 0.3s ease',
              '&:hover': {
                background: 'rgba(255, 255, 255, 1)',
              },
              '&.Mui-focused': {
                background: 'rgba(255, 255, 255, 1)',
                boxShadow: `0 0 0 3px ${unifiedTheme.colors.primary}20`,
              },
            },
          },
        },
      },
    },
  });

  const muiTheme = applyUnifiedThemeToMui(baseMuiTheme, unifiedTheme);

  // Save preferences
  useEffect(() => {
    localStorage.setItem('agri-theme-name', themeName);
  }, [themeName]);

  useEffect(() => {
    localStorage.setItem('agri-theme-mode', themeMode);
  }, [themeMode]);

  // Apply theme to document
  useEffect(() => {
    applyThemeToDocument(unifiedTheme);
  }, [unifiedTheme]);

  // Theme actions
  const setThemeName = (name: ThemeName) => {
    setThemeNameState(name);
  };

  const setThemeMode = (mode: ThemeMode) => {
    setThemeModeState(mode);
  };

  const toggleMode = () => {
    setThemeMode(themeMode === 'light' ? 'dark' : 'light');
  };

  const getModuleStylesWrapper = (module: string) => {
    return getModuleStyles(module, unifiedTheme, muiTheme);
  };

  const applyToDocument = () => {
    applyThemeToDocument(unifiedTheme);
  };

  const contextValue: UnifiedThemeContextType = {
    themeName,
    themeMode,
    unifiedTheme,
    muiTheme,
    setThemeName,
    setThemeMode,
    toggleMode,
    getModuleStyles: getModuleStylesWrapper,
    applyToDocument,
  };

  return (
    <UnifiedThemeContext.Provider value={contextValue}>
      <MuiThemeProvider theme={muiTheme}>
        <CssBaseline />
        {children}
      </MuiThemeProvider>
    </UnifiedThemeContext.Provider>
  );
};

export default UnifiedThemeProvider;
