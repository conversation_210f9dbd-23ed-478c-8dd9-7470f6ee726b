import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Container,
  Alert,
  Chip,
  Paper,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  CheckCircle,
  Star,
  Agriculture,
  HealthAndSafety,
  Restaurant,
  Assessment,
  Support,
  PhoneAndroid,
  TrendingUp,
  Security,
  Analytics,
  Storefront,
  AutoAwesome,
  Groups
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import AgriIntelBrand from '../../components/branding/AgriIntelBrand';
import '../../styles/auth-pages.css';

const ProfessionalLogin: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { login } = useAuth();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!username.trim() || !password.trim()) {
      setError('Please enter both username and password');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      await login(username, password);
      // Navigate to professional dashboard
      navigate('/dashboard', { replace: true });
    } catch (err: any) {
      setError(err.message || 'Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const professionalFeatures = [
    { icon: <Agriculture />, title: 'Unlimited Animals', description: 'Manage unlimited livestock with advanced profiles' },
    { icon: <Storefront />, title: 'Service Marketplace', description: 'Connect with vets, suppliers, and auctioneers' },
    { icon: <AutoAwesome />, title: 'Auto-Task System', description: 'Automated alerts and task management' },
    { icon: <Analytics />, title: 'Advanced Analytics', description: 'Comprehensive reporting and insights' },
    { icon: <Groups />, title: 'Service Network', description: 'Access to professional service providers' },
    { icon: <Security />, title: 'Priority Support', description: '24/7 professional support in your language' }
  ];

  return (
    <Box className="auth-page professional-auth" sx={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(168, 85, 247, 0.1) 100%)',
      display: 'flex',
      alignItems: 'center',
      py: 4
    }}>
      <Container maxWidth="lg">
        <Grid container spacing={4} alignItems="center">
          {/* Login Form */}
          <Grid item xs={12} md={6}>
            <Card sx={{
              maxWidth: 500,
              mx: 'auto',
              boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
              borderRadius: 3,
              overflow: 'visible'
            }}>
              <CardContent sx={{ p: 4 }}>
                <Box sx={{ textAlign: 'center', mb: 4 }}>
                  <AgriIntelBrand variant="compact" size="large" color="primary" />
                  <Chip
                    label="PROFESSIONAL"
                    sx={{
                      mt: 2,
                      fontWeight: 600,
                      background: 'linear-gradient(45deg, #3B82F6, #1D4ED8)',
                      color: 'white'
                    }}
                  />
                </Box>

                <Typography variant="h4" align="center" sx={{ mb: 1, fontWeight: 600 }}>
                  Professional Access
                </Typography>
                <Typography variant="body1" align="center" color="text.secondary" sx={{ mb: 4 }}>
                  Unlock the full power of AgriIntel with professional features and marketplace access
                </Typography>

                {error && (
                  <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                  </Alert>
                )}

                <Box component="form" onSubmit={handleLogin}>
                  <TextField
                    fullWidth
                    label="Username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    margin="normal"
                    required
                    autoFocus
                    placeholder="Enter your professional username"
                  />
                  <TextField
                    fullWidth
                    label="Password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    margin="normal"
                    required
                    placeholder="Enter your password"
                  />

                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    size="large"
                    disabled={isLoading}
                    sx={{
                      mt: 3,
                      mb: 2,
                      py: 1.5,
                      background: 'linear-gradient(45deg, #3B82F6, #1D4ED8)',
                      fontWeight: 600,
                      fontSize: '1.1rem'
                    }}
                  >
                    {isLoading ? 'Signing In...' : 'Access Professional Dashboard'}
                  </Button>
                </Box>

                <Box sx={{ textAlign: 'center', mt: 3 }}>
                  <Typography variant="body2" color="text.secondary">
                    R699/month - Full feature access
                  </Typography>
                  <Button
                    variant="text"
                    onClick={() => navigate('/')}
                    sx={{ mt: 1 }}
                  >
                    Back to Home
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Features Overview */}
          <Grid item xs={12} md={6}>
            <Paper sx={{
              p: 4,
              background: 'linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7))',
              backdropFilter: 'blur(10px)',
              borderRadius: 3
            }}>
              <Typography variant="h4" sx={{ mb: 3, fontWeight: 600, color: '#3B82F6' }}>
                Professional Features
              </Typography>
              <Typography variant="body1" sx={{ mb: 4, color: 'text.secondary' }}>
                Everything in Beta plus advanced tools for serious farming operations
              </Typography>

              <List>
                {professionalFeatures.map((feature, index) => (
                  <ListItem key={index} sx={{ px: 0, py: 1 }}>
                    <ListItemIcon sx={{ color: '#3B82F6' }}>
                      {feature.icon}
                    </ListItemIcon>
                    <ListItemText
                      primary={feature.title}
                      secondary={feature.description}
                      primaryTypographyProps={{ fontWeight: 600 }}
                    />
                  </ListItem>
                ))}
              </List>

              <Box sx={{ mt: 4, p: 3, background: 'rgba(59, 130, 246, 0.1)', borderRadius: 2 }}>
                <Typography variant="h6" sx={{ mb: 1, fontWeight: 600 }}>
                  Marketplace Access
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Connect with veterinarians, suppliers, auctioneers, and security services through our integrated platform
                </Typography>
                <Button
                  variant="outlined"
                  onClick={() => navigate('/?tab=services')}
                  sx={{ borderColor: '#3B82F6', color: '#3B82F6' }}
                >
                  Learn More
                </Button>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default ProfessionalLogin;