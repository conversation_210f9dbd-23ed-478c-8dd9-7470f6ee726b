/**
 * Unified Theme System for AgriIntel
 * Consolidates all theme approaches into a single, consistent system
 */

import { Theme, alpha } from '@mui/material/styles';
import { gradientThemes, GradientTheme } from './gradientThemes';

export interface UnifiedTheme {
  name: string;
  // Gradient strings for backgrounds
  gradients: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
  };
  // Solid colors for MUI components
  colors: {
    primary: string;
    primaryDark: string;
    primaryLight: string;
    secondary: string;
    secondaryDark: string;
    secondaryLight: string;
    accent: string;
    accentDark: string;
    accentLight: string;
    surface: string;
    text: string;
    textSecondary: string;
  };
  // Module-specific variations
  modules: {
    [key: string]: {
      primary: string;
      secondary: string;
      accent: string;
    };
  };
}

/**
 * Convert gradient theme to unified theme format
 */
export const convertGradientToUnified = (gradientTheme: GradientTheme): UnifiedTheme => {
  return {
    name: gradientTheme.name,
    gradients: {
      primary: gradientTheme.primary,
      secondary: gradientTheme.secondary,
      accent: gradientTheme.accent,
      background: gradientTheme.background,
    },
    colors: {
      primary: gradientTheme.primaryColor,
      primaryDark: adjustColor(gradientTheme.primaryColor, -20),
      primaryLight: adjustColor(gradientTheme.primaryColor, 20),
      secondary: gradientTheme.secondaryColor,
      secondaryDark: adjustColor(gradientTheme.secondaryColor, -20),
      secondaryLight: adjustColor(gradientTheme.secondaryColor, 20),
      accent: gradientTheme.accentColor,
      accentDark: adjustColor(gradientTheme.accentColor, -20),
      accentLight: adjustColor(gradientTheme.accentColor, 20),
      surface: gradientTheme.surface,
      text: gradientTheme.text,
      textSecondary: gradientTheme.textSecondary,
    },
    modules: generateModuleVariations(gradientTheme.primaryColor, gradientTheme.secondaryColor),
  };
};

/**
 * Generate module-specific color variations
 */
const generateModuleVariations = (basePrimary: string, baseSecondary: string) => {
  return {
    animals: {
      primary: basePrimary,
      secondary: baseSecondary,
      accent: adjustColor(basePrimary, 10),
    },
    health: {
      primary: '#10b981', // Green for health
      secondary: '#34d399',
      accent: '#6ee7b7',
    },
    breeding: {
      primary: '#8b5cf6', // Purple for breeding
      secondary: '#a78bfa',
      accent: '#c4b5fd',
    },
    feeding: {
      primary: '#f59e0b', // Orange for feeding
      secondary: '#fbbf24',
      accent: '#fcd34d',
    },
    financial: {
      primary: '#059669', // Green for financial
      secondary: '#10b981',
      accent: '#34d399',
    },
    inventory: {
      primary: '#6366f1', // Indigo for inventory
      secondary: '#818cf8',
      accent: '#a5b4fc',
    },
    commercial: {
      primary: '#dc2626', // Red for commercial
      secondary: '#ef4444',
      accent: '#f87171',
    },
    reports: {
      primary: '#0891b2', // Cyan for reports
      secondary: '#06b6d4',
      accent: '#22d3ee',
    },
    analytics: {
      primary: '#7c3aed', // Violet for analytics
      secondary: '#8b5cf6',
      accent: '#a78bfa',
    },
    compliance: {
      primary: '#dc2626', // Red for compliance
      secondary: '#ef4444',
      accent: '#f87171',
    },
    resources: {
      primary: '#0d9488', // Teal for resources
      secondary: '#14b8a6',
      accent: '#5eead4',
    },
    settings: {
      primary: '#6b7280', // Gray for settings
      secondary: '#9ca3af',
      accent: '#d1d5db',
    },
  };
};

/**
 * Adjust color brightness
 */
const adjustColor = (color: string, percent: number): string => {
  // Simple color adjustment - in a real app, you'd use a proper color manipulation library
  const num = parseInt(color.replace('#', ''), 16);
  const amt = Math.round(2.55 * percent);
  const R = (num >> 16) + amt;
  const G = (num >> 8 & 0x00FF) + amt;
  const B = (num & 0x0000FF) + amt;
  
  return '#' + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
    (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
    (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
};

/**
 * Get unified theme by name
 */
export const getUnifiedTheme = (themeName: string): UnifiedTheme => {
  const gradientTheme = gradientThemes[themeName] || gradientThemes.emerald;
  return convertGradientToUnified(gradientTheme);
};

/**
 * Apply unified theme to MUI theme
 */
export const applyUnifiedThemeToMui = (muiTheme: Theme, unifiedTheme: UnifiedTheme): Theme => {
  return {
    ...muiTheme,
    palette: {
      ...muiTheme.palette,
      primary: {
        main: unifiedTheme.colors.primary,
        dark: unifiedTheme.colors.primaryDark,
        light: unifiedTheme.colors.primaryLight,
        contrastText: unifiedTheme.colors.text,
      },
      secondary: {
        main: unifiedTheme.colors.secondary,
        dark: unifiedTheme.colors.secondaryDark,
        light: unifiedTheme.colors.secondaryLight,
        contrastText: unifiedTheme.colors.text,
      },
      // Add custom palette extensions
      custom: {
        ...muiTheme.palette.custom,
        gradients: unifiedTheme.gradients,
        moduleColors: unifiedTheme.modules,
        accent: unifiedTheme.colors.accent,
        accentDark: unifiedTheme.colors.accentDark,
        accentLight: unifiedTheme.colors.accentLight,
      },
    },
  };
};

/**
 * Get module-specific styling
 */
export const getModuleStyles = (
  module: string, 
  unifiedTheme: UnifiedTheme, 
  muiTheme: Theme
) => {
  const moduleColors = unifiedTheme.modules[module] || unifiedTheme.modules.animals;
  
  return {
    colors: moduleColors,
    gradientBackground: `linear-gradient(135deg, ${moduleColors.primary} 0%, ${moduleColors.secondary} 100%)`,
    cardStyle: {
      background: alpha(moduleColors.primary, 0.1),
      backdropFilter: 'blur(20px)',
      border: `1px solid ${alpha(moduleColors.primary, 0.2)}`,
      borderRadius: muiTheme.shape.borderRadius,
      '&:hover': {
        background: alpha(moduleColors.primary, 0.15),
        transform: 'translateY(-2px)',
        boxShadow: muiTheme.shadows[8],
      },
    },
    buttonStyle: {
      background: `linear-gradient(135deg, ${moduleColors.primary}, ${moduleColors.secondary})`,
      color: unifiedTheme.colors.text,
      '&:hover': {
        background: `linear-gradient(135deg, ${moduleColors.secondary}, ${moduleColors.primary})`,
      },
    },
    chipStyle: {
      background: alpha(moduleColors.accent, 0.2),
      color: moduleColors.primary,
      border: `1px solid ${alpha(moduleColors.accent, 0.3)}`,
    },
  };
};

/**
 * Get all available unified themes
 */
export const getAllUnifiedThemes = (): Record<string, UnifiedTheme> => {
  const unifiedThemes: Record<string, UnifiedTheme> = {};
  
  Object.keys(gradientThemes).forEach(themeName => {
    unifiedThemes[themeName] = getUnifiedTheme(themeName);
  });
  
  return unifiedThemes;
};

/**
 * Apply theme to document root
 */
export const applyThemeToDocument = (unifiedTheme: UnifiedTheme): void => {
  const root = document.documentElement;
  
  // Apply CSS custom properties
  root.style.setProperty('--primary-gradient', unifiedTheme.gradients.primary);
  root.style.setProperty('--secondary-gradient', unifiedTheme.gradients.secondary);
  root.style.setProperty('--accent-gradient', unifiedTheme.gradients.accent);
  root.style.setProperty('--background-gradient', unifiedTheme.gradients.background);
  
  root.style.setProperty('--primary-color', unifiedTheme.colors.primary);
  root.style.setProperty('--secondary-color', unifiedTheme.colors.secondary);
  root.style.setProperty('--accent-color', unifiedTheme.colors.accent);
  root.style.setProperty('--text-color', unifiedTheme.colors.text);
  root.style.setProperty('--text-secondary-color', unifiedTheme.colors.textSecondary);
  
  // Apply theme class to body
  document.body.className = `agri-theme-${unifiedTheme.name.toLowerCase().replace(/\s+/g, '-')}`;
};

export default {
  getUnifiedTheme,
  applyUnifiedThemeToMui,
  getModuleStyles,
  getAllUnifiedThemes,
  applyThemeToDocument,
};
