/**
 * Modal Component Styles
 * Professional modal designs for AgriIntel platform
 */

/* ===== MODAL OVERLAY ===== */

.agri-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  padding: var(--spacing-lg);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.agri-modal-overlay.open {
  opacity: 1;
  visibility: visible;
}

/* ===== MODAL CONTAINER ===== */

.agri-modal {
  background: var(--color-background-primary);
  border-radius: var(--radius-xl);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  max-width: 90vw;
  max-height: 90vh;
  width: 100%;
  overflow: hidden;
  transform: scale(0.9) translateY(20px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.agri-modal-overlay.open .agri-modal {
  transform: scale(1) translateY(0);
}

/* ===== MODAL SIZES ===== */

.agri-modal-sm {
  max-width: 400px;
}

.agri-modal-md {
  max-width: 600px;
}

.agri-modal-lg {
  max-width: 800px;
}

.agri-modal-xl {
  max-width: 1200px;
}

.agri-modal-full {
  max-width: 95vw;
  max-height: 95vh;
}

/* ===== MODAL HEADER ===== */

.agri-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid var(--color-border);
  background: var(--color-background-secondary);
}

.agri-modal-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.agri-modal-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--radius-full);
  background: transparent;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: var(--transition-normal);
  flex-shrink: 0;
}

.agri-modal-close:hover {
  background: var(--color-background-hover);
  color: var(--color-text-primary);
}

.agri-modal-close:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}

/* ===== MODAL BODY ===== */

.agri-modal-body {
  padding: var(--spacing-xl);
  overflow-y: auto;
  max-height: calc(90vh - 140px);
}

.agri-modal-body::-webkit-scrollbar {
  width: 8px;
}

.agri-modal-body::-webkit-scrollbar-track {
  background: var(--color-background-secondary);
  border-radius: var(--radius-full);
}

.agri-modal-body::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: var(--radius-full);
}

.agri-modal-body::-webkit-scrollbar-thumb:hover {
  background: var(--agri-primary);
}

/* ===== MODAL FOOTER ===== */

.agri-modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-md);
  padding: var(--spacing-lg) var(--spacing-xl);
  border-top: 1px solid var(--color-border);
  background: var(--color-background-secondary);
}

.agri-modal-footer.center {
  justify-content: center;
}

.agri-modal-footer.start {
  justify-content: flex-start;
}

.agri-modal-footer.between {
  justify-content: space-between;
}

/* ===== MODAL VARIANTS ===== */

/* Glassmorphism Modal */
.agri-modal-glass {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
}

.agri-modal-glass .agri-modal-header,
.agri-modal-glass .agri-modal-footer {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

/* Success Modal */
.agri-modal-success .agri-modal-header {
  background: rgba(76, 175, 80, 0.1);
  border-color: rgba(76, 175, 80, 0.3);
}

.agri-modal-success .agri-modal-title {
  color: var(--color-success);
}

/* Warning Modal */
.agri-modal-warning .agri-modal-header {
  background: rgba(255, 152, 0, 0.1);
  border-color: rgba(255, 152, 0, 0.3);
}

.agri-modal-warning .agri-modal-title {
  color: var(--color-warning);
}

/* Error Modal */
.agri-modal-error .agri-modal-header {
  background: rgba(244, 67, 54, 0.1);
  border-color: rgba(244, 67, 54, 0.3);
}

.agri-modal-error .agri-modal-title {
  color: var(--color-error);
}

/* Info Modal */
.agri-modal-info .agri-modal-header {
  background: rgba(33, 150, 243, 0.1);
  border-color: rgba(33, 150, 243, 0.3);
}

.agri-modal-info .agri-modal-title {
  color: var(--color-info);
}

/* ===== CONFIRMATION MODAL ===== */

.agri-confirm-modal {
  text-align: center;
  max-width: 480px;
}

.agri-confirm-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--spacing-lg);
  padding: var(--spacing-lg);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-2xl);
}

.agri-confirm-icon.success {
  background: rgba(76, 175, 80, 0.1);
  color: var(--color-success);
}

.agri-confirm-icon.warning {
  background: rgba(255, 152, 0, 0.1);
  color: var(--color-warning);
}

.agri-confirm-icon.error {
  background: rgba(244, 67, 54, 0.1);
  color: var(--color-error);
}

.agri-confirm-icon.info {
  background: rgba(33, 150, 243, 0.1);
  color: var(--color-info);
}

.agri-confirm-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
}

.agri-confirm-message {
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-xl);
}

/* ===== LOADING MODAL ===== */

.agri-loading-modal {
  text-align: center;
  max-width: 320px;
}

.agri-loading-spinner {
  width: 48px;
  height: 48px;
  margin: 0 auto var(--spacing-lg);
  border: 4px solid var(--color-border);
  border-top: 4px solid var(--agri-primary);
  border-radius: var(--radius-full);
  animation: agri-modal-spin 1s linear infinite;
}

@keyframes agri-modal-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.agri-loading-message {
  color: var(--color-text-secondary);
  font-size: var(--font-size-lg);
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
  .agri-modal-overlay {
    padding: var(--spacing-md);
  }
  
  .agri-modal {
    max-width: 95vw;
    max-height: 95vh;
  }
  
  .agri-modal-header,
  .agri-modal-body,
  .agri-modal-footer {
    padding: var(--spacing-lg);
  }
  
  .agri-modal-body {
    max-height: calc(95vh - 120px);
  }
  
  .agri-modal-footer {
    flex-direction: column;
    align-items: stretch;
  }
  
  .agri-modal-footer .agri-btn {
    width: 100%;
    justify-content: center;
  }
  
  .agri-modal-title {
    font-size: var(--font-size-lg);
  }
}

@media (max-width: 480px) {
  .agri-modal-overlay {
    padding: var(--spacing-sm);
  }
  
  .agri-modal-header,
  .agri-modal-body,
  .agri-modal-footer {
    padding: var(--spacing-md);
  }
  
  .agri-modal-body {
    max-height: calc(95vh - 100px);
  }
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  .agri-modal-overlay,
  .agri-modal {
    transition: none;
  }
  
  .agri-modal-overlay.open .agri-modal {
    transform: none;
  }
  
  .agri-loading-spinner {
    animation: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .agri-modal {
    border: 2px solid var(--color-text-primary);
  }
  
  .agri-modal-header,
  .agri-modal-footer {
    border-color: var(--color-text-primary);
  }
  
  .agri-modal-glass {
    background: var(--color-background-primary);
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
  }
}

/* ===== FOCUS MANAGEMENT ===== */

.agri-modal-overlay[aria-hidden="true"] {
  display: none;
}

.agri-modal[role="dialog"] {
  outline: none;
}

.agri-modal-overlay:focus {
  outline: none;
}

/* Ensure modal content is focusable */
.agri-modal-body *:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}
