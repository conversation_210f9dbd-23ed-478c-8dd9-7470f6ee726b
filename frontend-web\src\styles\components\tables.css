/**
 * Table Component Styles
 * Professional table designs for AgriIntel platform
 */

/* ===== BASE TABLE STYLES ===== */

.agri-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--color-background-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-lg);
}

.agri-table-container {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  overflow-x: auto;
}

/* ===== TABLE HEADER ===== */

.agri-table thead {
  background: var(--agri-pro-gradient);
}

.agri-table th {
  padding: var(--spacing-md) var(--spacing-lg);
  text-align: left;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
  color: var(--color-white);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
  border: none;
  position: relative;
}

.agri-table th:first-child {
  border-radius: var(--radius-lg) 0 0 0;
}

.agri-table th:last-child {
  border-radius: 0 var(--radius-lg) 0 0;
}

/* Sortable Headers */
.agri-table th.sortable {
  cursor: pointer;
  transition: var(--transition-normal);
}

.agri-table th.sortable:hover {
  background: rgba(255, 255, 255, 0.1);
}

.agri-table th.sortable::after {
  content: '↕';
  position: absolute;
  right: var(--spacing-sm);
  opacity: 0.5;
  font-size: var(--font-size-xs);
}

.agri-table th.sortable.asc::after {
  content: '↑';
  opacity: 1;
}

.agri-table th.sortable.desc::after {
  content: '↓';
  opacity: 1;
}

/* ===== TABLE BODY ===== */

.agri-table tbody tr {
  border-bottom: 1px solid var(--color-border);
  transition: var(--transition-normal);
}

.agri-table tbody tr:hover {
  background: var(--color-background-hover);
}

.agri-table tbody tr:last-child {
  border-bottom: none;
}

.agri-table td {
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  border: none;
  vertical-align: middle;
}

/* ===== TABLE VARIANTS ===== */

/* Striped Table */
.agri-table-striped tbody tr:nth-child(even) {
  background: var(--color-background-secondary);
}

.agri-table-striped tbody tr:nth-child(even):hover {
  background: var(--color-background-hover);
}

/* Bordered Table */
.agri-table-bordered {
  border: 1px solid var(--color-border);
}

.agri-table-bordered th,
.agri-table-bordered td {
  border: 1px solid var(--color-border);
}

/* Compact Table */
.agri-table-compact th,
.agri-table-compact td {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
}

/* ===== TABLE CELLS ===== */

/* Numeric Cells */
.agri-table td.numeric {
  text-align: right;
  font-family: var(--font-mono);
  font-weight: var(--font-weight-medium);
}

/* Status Cells */
.agri-table td.status {
  text-align: center;
}

.agri-status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
}

.agri-status-badge.success {
  background: rgba(76, 175, 80, 0.1);
  color: var(--color-success);
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.agri-status-badge.warning {
  background: rgba(255, 152, 0, 0.1);
  color: var(--color-warning);
  border: 1px solid rgba(255, 152, 0, 0.3);
}

.agri-status-badge.error {
  background: rgba(244, 67, 54, 0.1);
  color: var(--color-error);
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.agri-status-badge.info {
  background: rgba(33, 150, 243, 0.1);
  color: var(--color-info);
  border: 1px solid rgba(33, 150, 243, 0.3);
}

/* Action Cells */
.agri-table td.actions {
  text-align: center;
  white-space: nowrap;
}

.agri-table-actions {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.agri-table-action {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius-md);
  background: transparent;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: var(--transition-normal);
}

.agri-table-action:hover {
  background: var(--color-background-hover);
  color: var(--agri-primary);
}

.agri-table-action.edit:hover {
  color: var(--color-info);
}

.agri-table-action.delete:hover {
  color: var(--color-error);
}

.agri-table-action.view:hover {
  color: var(--color-success);
}

/* ===== TABLE FOOTER ===== */

.agri-table tfoot {
  background: var(--color-background-secondary);
  border-top: 2px solid var(--color-border);
}

.agri-table tfoot td {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

/* ===== PAGINATION ===== */

.agri-table-pagination {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md) 0;
}

.agri-pagination-info {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

.agri-pagination-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.agri-pagination-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  background: var(--color-background-primary);
  color: var(--color-text-primary);
  cursor: pointer;
  transition: var(--transition-normal);
  font-size: var(--font-size-sm);
}

.agri-pagination-btn:hover {
  background: var(--color-background-hover);
  border-color: var(--agri-primary);
  color: var(--agri-primary);
}

.agri-pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.agri-pagination-btn.active {
  background: var(--agri-primary);
  border-color: var(--agri-primary);
  color: var(--color-white);
}

/* ===== LOADING STATE ===== */

.agri-table-loading {
  position: relative;
  opacity: 0.6;
  pointer-events: none;
}

.agri-table-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  margin: -20px 0 0 -20px;
  border: 3px solid var(--color-border);
  border-top: 3px solid var(--agri-primary);
  border-radius: var(--radius-full);
  animation: agri-table-spin 1s linear infinite;
}

@keyframes agri-table-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== EMPTY STATE ===== */

.agri-table-empty {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--color-text-secondary);
}

.agri-table-empty-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

.agri-table-empty-message {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-sm);
}

.agri-table-empty-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-disabled);
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
  .agri-table-container {
    padding: var(--spacing-md);
  }
  
  .agri-table th,
  .agri-table td {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
  }
  
  .agri-table-pagination {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  
  .agri-pagination-controls {
    justify-content: center;
  }
  
  /* Hide less important columns on mobile */
  .agri-table th.hide-mobile,
  .agri-table td.hide-mobile {
    display: none;
  }
}

@media (max-width: 480px) {
  .agri-table th,
  .agri-table td {
    padding: var(--spacing-xs) var(--spacing-sm);
  }
  
  .agri-table-actions {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
  
  .agri-table-action {
    width: 28px;
    height: 28px;
  }
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  .agri-table-loading::after {
    animation: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .agri-table {
    border: 2px solid var(--color-text-primary);
  }
  
  .agri-table th {
    background: var(--color-text-primary);
    color: var(--color-background-primary);
  }
  
  .agri-table tbody tr {
    border-bottom: 1px solid var(--color-text-primary);
  }
  
  .agri-status-badge {
    border-width: 2px;
  }
}
