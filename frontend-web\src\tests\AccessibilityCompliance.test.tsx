/**
 * Accessibility Compliance Test Suite
 * WCAG 2.1 AA compliance testing for AgriIntel redesign
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';

// Import components
import AgriIntelLanding from '../pages/AgriIntelLanding';
import BetaV1Login from '../pages/BetaV1Login';
import ProfessionalV1Login from '../pages/ProfessionalV1Login';
import { AuthProvider } from '../contexts/AuthContext';
import { ThemeContextProvider } from '../contexts/ThemeContext';
import { LanguageProvider } from '../contexts/LanguageContext';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock contexts
jest.mock('../contexts/AuthContext', () => ({
  useAuth: () => ({
    user: null,
    login: jest.fn(),
    logout: jest.fn(),
    isAuthenticated: false,
    isLoading: false,
    error: null,
  }),
  AuthProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const theme = createTheme();
  
  return (
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        <ThemeContextProvider>
          <LanguageProvider>
            <AuthProvider>
              {children}
            </AuthProvider>
          </LanguageProvider>
        </ThemeContextProvider>
      </ThemeProvider>
    </BrowserRouter>
  );
};

describe('Accessibility Compliance', () => {
  describe('WCAG 2.1 AA Compliance', () => {
    test('Landing page should have no accessibility violations', async () => {
      const { container } = render(
        <TestWrapper>
          <AgriIntelLanding />
        </TestWrapper>
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('BETA V1 login should have no accessibility violations', async () => {
      const { container } = render(
        <TestWrapper>
          <BetaV1Login />
        </TestWrapper>
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('Professional V1 login should have no accessibility violations', async () => {
      const { container } = render(
        <TestWrapper>
          <ProfessionalV1Login />
        </TestWrapper>
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  describe('Semantic HTML', () => {
    test('should use proper heading hierarchy', () => {
      render(
        <TestWrapper>
          <AgriIntelLanding />
        </TestWrapper>
      );

      // Check for proper heading structure
      const h1 = screen.getByRole('heading', { level: 1 });
      expect(h1).toBeInTheDocument();
      
      // Should have logical heading progression
      const headings = screen.getAllByRole('heading');
      expect(headings.length).toBeGreaterThan(1);
    });

    test('should have proper main landmark', () => {
      render(
        <TestWrapper>
          <AgriIntelLanding />
        </TestWrapper>
      );

      const main = screen.getByRole('main');
      expect(main).toBeInTheDocument();
    });

    test('should have proper navigation landmark', () => {
      render(
        <TestWrapper>
          <AgriIntelLanding />
        </TestWrapper>
      );

      const nav = screen.getByRole('navigation');
      expect(nav).toBeInTheDocument();
    });
  });

  describe('Form Accessibility', () => {
    test('form inputs should have proper labels', () => {
      render(
        <TestWrapper>
          <BetaV1Login />
        </TestWrapper>
      );

      const usernameInput = screen.getByLabelText('Username');
      const passwordInput = screen.getByLabelText('Password');

      expect(usernameInput).toBeInTheDocument();
      expect(passwordInput).toBeInTheDocument();
      expect(passwordInput).toHaveAttribute('type', 'password');
    });

    test('form should have proper submit button', () => {
      render(
        <TestWrapper>
          <BetaV1Login />
        </TestWrapper>
      );

      const submitButton = screen.getByRole('button', { name: /start free trial/i });
      expect(submitButton).toHaveAttribute('type', 'submit');
    });

    test('required fields should be marked as required', () => {
      render(
        <TestWrapper>
          <BetaV1Login />
        </TestWrapper>
      );

      const usernameInput = screen.getByLabelText('Username');
      const passwordInput = screen.getByLabelText('Password');

      expect(usernameInput).toHaveAttribute('required');
      expect(passwordInput).toHaveAttribute('required');
    });
  });

  describe('Button Accessibility', () => {
    test('buttons should have descriptive accessible names', () => {
      render(
        <TestWrapper>
          <AgriIntelLanding />
        </TestWrapper>
      );

      const betaButton = screen.getByRole('button', { name: /start beta v1 free trial/i });
      const proButton = screen.getByRole('button', { name: /access professional v1 features/i });

      expect(betaButton).toBeInTheDocument();
      expect(proButton).toBeInTheDocument();
    });

    test('interactive elements should be keyboard accessible', () => {
      render(
        <TestWrapper>
          <AgriIntelLanding />
        </TestWrapper>
      );

      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).not.toHaveAttribute('tabindex', '-1');
      });
    });
  });

  describe('Image Accessibility', () => {
    test('images should have proper alt text', () => {
      render(
        <TestWrapper>
          <BetaV1Login />
        </TestWrapper>
      );

      const logo = screen.getByAltText('AgriIntel Logo');
      expect(logo).toBeInTheDocument();
    });

    test('decorative images should have empty alt text or be hidden from screen readers', () => {
      render(
        <TestWrapper>
          <AgriIntelLanding />
        </TestWrapper>
      );

      // Background images should not be exposed to screen readers
      // This is handled through CSS background-image property
      const decorativeImages = screen.queryAllByRole('img', { hidden: true });
      decorativeImages.forEach(img => {
        expect(img).toHaveAttribute('alt', '');
      });
    });
  });

  describe('Color Contrast', () => {
    test('should meet WCAG AA color contrast requirements', () => {
      // This would typically be tested with automated tools like axe
      // or manual testing with contrast checkers
      render(
        <TestWrapper>
          <AgriIntelLanding />
        </TestWrapper>
      );

      // Check that text elements exist (contrast would be tested by axe)
      const textElements = screen.getAllByText(/agriintel/i);
      expect(textElements.length).toBeGreaterThan(0);
    });
  });

  describe('Focus Management', () => {
    test('interactive elements should be focusable', () => {
      render(
        <TestWrapper>
          <BetaV1Login />
        </TestWrapper>
      );

      const usernameInput = screen.getByLabelText('Username');
      const passwordInput = screen.getByLabelText('Password');
      const submitButton = screen.getByRole('button', { name: /start free trial/i });

      expect(usernameInput).not.toHaveAttribute('tabindex', '-1');
      expect(passwordInput).not.toHaveAttribute('tabindex', '-1');
      expect(submitButton).not.toHaveAttribute('tabindex', '-1');
    });

    test('should have logical tab order', () => {
      render(
        <TestWrapper>
          <BetaV1Login />
        </TestWrapper>
      );

      const focusableElements = [
        screen.getByLabelText('Username'),
        screen.getByLabelText('Password'),
        screen.getByRole('button', { name: /start free trial/i })
      ];

      // Elements should be in logical order (no explicit tabindex manipulation)
      focusableElements.forEach(element => {
        expect(element).not.toHaveAttribute('tabindex');
      });
    });
  });

  describe('Screen Reader Support', () => {
    test('should have proper ARIA labels where needed', () => {
      render(
        <TestWrapper>
          <AgriIntelLanding />
        </TestWrapper>
      );

      const betaButton = screen.getByLabelText(/start beta v1 free trial/i);
      const proButton = screen.getByLabelText(/access professional v1 features/i);

      expect(betaButton).toBeInTheDocument();
      expect(proButton).toBeInTheDocument();
    });

    test('should have proper role attributes', () => {
      render(
        <TestWrapper>
          <AgriIntelLanding />
        </TestWrapper>
      );

      const main = screen.getByRole('main');
      const navigation = screen.getByRole('navigation');
      const buttons = screen.getAllByRole('button');

      expect(main).toBeInTheDocument();
      expect(navigation).toBeInTheDocument();
      expect(buttons.length).toBeGreaterThan(0);
    });
  });

  describe('Responsive Design Accessibility', () => {
    test('should maintain accessibility on mobile viewports', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(
        <TestWrapper>
          <BetaV1Login />
        </TestWrapper>
      );

      // Touch targets should be at least 44px (this would be tested in CSS)
      const buttons = screen.getAllByRole('button');
      expect(buttons.length).toBeGreaterThan(0);
    });
  });

  describe('Error Handling Accessibility', () => {
    test('error messages should be associated with form fields', () => {
      render(
        <TestWrapper>
          <BetaV1Login />
        </TestWrapper>
      );

      // When errors are shown, they should be properly associated
      // This would be tested with actual error states
      const usernameInput = screen.getByLabelText('Username');
      expect(usernameInput).toBeInTheDocument();
    });
  });

  describe('Language Support', () => {
    test('should have proper lang attribute', () => {
      render(
        <TestWrapper>
          <AgriIntelLanding />
        </TestWrapper>
      );

      // The html element should have lang attribute (set in index.html)
      // This test verifies the content is properly structured
      const headings = screen.getAllByRole('heading');
      expect(headings.length).toBeGreaterThan(0);
    });
  });
});
