/**
 * Modern UI Animations for AgriIntel
 * Smooth, professional animations with performance optimization
 */

/* ===== ANIMATION VARIABLES ===== */
:root {
  --animation-duration-fast: 0.15s;
  --animation-duration-normal: 0.3s;
  --animation-duration-slow: 0.5s;
  --animation-duration-slower: 0.8s;
  
  --animation-easing-ease: ease;
  --animation-easing-ease-in: ease-in;
  --animation-easing-ease-out: ease-out;
  --animation-easing-ease-in-out: ease-in-out;
  --animation-easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --animation-easing-smooth: cubic-bezier(0.4, 0, 0.2, 1);
  --animation-easing-sharp: cubic-bezier(0.4, 0, 0.6, 1);
}

/* ===== FADE ANIMATIONS ===== */

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* ===== SCALE ANIMATIONS ===== */

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.8);
  }
}

@keyframes scaleInBounce {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* ===== SLIDE ANIMATIONS ===== */

@keyframes slideInUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

/* ===== ROTATION ANIMATIONS ===== */

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes rotateIn {
  from {
    opacity: 0;
    transform: rotate(-200deg);
  }
  to {
    opacity: 1;
    transform: rotate(0deg);
  }
}

/* ===== SPECIAL EFFECTS ===== */

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.1);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.1);
  }
  70% {
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translateY(0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translateY(-30px);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translateY(-15px);
  }
  90% {
    transform: translateY(-4px);
  }
}

/* ===== UTILITY CLASSES ===== */

.animate-fade-in {
  animation: fadeIn var(--animation-duration-normal) var(--animation-easing-ease-out);
}

.animate-fade-in-up {
  animation: fadeInUp var(--animation-duration-normal) var(--animation-easing-smooth);
}

.animate-fade-in-down {
  animation: fadeInDown var(--animation-duration-normal) var(--animation-easing-smooth);
}

.animate-fade-in-left {
  animation: fadeInLeft var(--animation-duration-normal) var(--animation-easing-smooth);
}

.animate-fade-in-right {
  animation: fadeInRight var(--animation-duration-normal) var(--animation-easing-smooth);
}

.animate-scale-in {
  animation: scaleIn var(--animation-duration-normal) var(--animation-easing-smooth);
}

.animate-scale-in-bounce {
  animation: scaleInBounce var(--animation-duration-slow) var(--animation-easing-bounce);
}

.animate-slide-in-up {
  animation: slideInUp var(--animation-duration-normal) var(--animation-easing-smooth);
}

.animate-slide-in-down {
  animation: slideInDown var(--animation-duration-normal) var(--animation-easing-smooth);
}

.animate-rotate-in {
  animation: rotateIn var(--animation-duration-slow) var(--animation-easing-smooth);
}

.animate-pulse {
  animation: pulse 2s var(--animation-easing-ease-in-out) infinite;
}

.animate-float {
  animation: float 3s var(--animation-easing-ease-in-out) infinite;
}

.animate-bounce {
  animation: bounce 1s var(--animation-easing-ease-out);
}

.animate-heartbeat {
  animation: heartbeat 1.5s var(--animation-easing-ease-in-out) infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* ===== HOVER ANIMATIONS ===== */

.hover-lift {
  transition: transform var(--animation-duration-normal) var(--animation-easing-smooth);
}

.hover-lift:hover {
  transform: translateY(-8px);
}

.hover-scale {
  transition: transform var(--animation-duration-normal) var(--animation-easing-smooth);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-rotate {
  transition: transform var(--animation-duration-normal) var(--animation-easing-smooth);
}

.hover-rotate:hover {
  transform: rotate(5deg);
}

.hover-glow {
  transition: box-shadow var(--animation-duration-normal) var(--animation-easing-smooth);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(46, 125, 50, 0.4);
}

.hover-beta-glow:hover {
  box-shadow: 0 0 20px rgba(245, 124, 0, 0.4);
}

.hover-pro-glow:hover {
  box-shadow: 0 0 20px rgba(46, 125, 50, 0.4);
}

/* ===== LOADING ANIMATIONS ===== */

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes dots {
  0%, 20% {
    color: rgba(255, 255, 255, 0);
    text-shadow: 0.25em 0 0 rgba(255, 255, 255, 0),
                 0.5em 0 0 rgba(255, 255, 255, 0);
  }
  40% {
    color: white;
    text-shadow: 0.25em 0 0 rgba(255, 255, 255, 0),
                 0.5em 0 0 rgba(255, 255, 255, 0);
  }
  60% {
    text-shadow: 0.25em 0 0 white,
                 0.5em 0 0 rgba(255, 255, 255, 0);
  }
  80%, 100% {
    text-shadow: 0.25em 0 0 white,
                 0.5em 0 0 white;
  }
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

.loading-dots::after {
  content: '...';
  animation: dots 1.5s steps(5, end) infinite;
}

/* ===== STAGGER ANIMATIONS ===== */

.stagger-children > * {
  animation-delay: calc(var(--stagger-delay, 0.1s) * var(--index, 0));
}

.stagger-fade-in-up > * {
  animation: fadeInUp var(--animation-duration-normal) var(--animation-easing-smooth) both;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-auto {
  will-change: auto;
}

/* Force hardware acceleration */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* ===== RESPONSIVE ANIMATIONS ===== */

@media (max-width: 768px) {
  :root {
    --animation-duration-fast: 0.1s;
    --animation-duration-normal: 0.2s;
    --animation-duration-slow: 0.3s;
  }
  
  .hover-lift:hover {
    transform: translateY(-4px);
  }
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .animate-pulse,
  .animate-float,
  .animate-heartbeat,
  .animate-shimmer,
  .loading-spinner,
  .loading-dots::after {
    animation: none !important;
  }
  
  .hover-lift:hover,
  .hover-scale:hover,
  .hover-rotate:hover {
    transform: none !important;
  }
}
