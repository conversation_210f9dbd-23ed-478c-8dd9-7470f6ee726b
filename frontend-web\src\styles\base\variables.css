/**
 * CSS Variables - AgriIntel Design System
 * Centralized design tokens for consistent styling
 */

:root {
  /* ===== COLORS ===== */
  
  /* Primary Colors - Deep Agricultural Blues */
  --agri-primary-dark: #1565C0;
  --agri-primary: #1976D2;
  --agri-primary-light: #42A5F5;

  /* Secondary Colors - Emerald Greens */
  --agri-secondary-dark: #2E7D32;
  --agri-secondary: #388E3C;
  --agri-secondary-light: #66BB6A;

  /* Accent Colors - Warm Golds */
  --agri-accent-dark: #F57C00;
  --agri-accent: #FF8F00;
  --agri-accent-light: #FFB74D;

  /* BETA Tier Colors - Yellow Accents */
  --agri-beta-primary: #F57C00;
  --agri-beta-secondary: #FFB74D;
  --agri-beta-gradient: linear-gradient(135deg, #F57C00 0%, #FFB74D 100%);

  /* Professional Tier Colors - Green Accents */
  --agri-pro-primary: #2E7D32;
  --agri-pro-secondary: #66BB6A;
  --agri-pro-gradient: linear-gradient(135deg, #2E7D32 0%, #66BB6A 100%);
  
  /* Neutral Colors */
  --color-white: #FFFFFF;
  --color-black: #000000;
  --color-gray-50: #F8F9FA;
  --color-gray-100: #F1F3F4;
  --color-gray-200: #E8EAED;
  --color-gray-300: #DADCE0;
  --color-gray-400: #BDC1C6;
  --color-gray-500: #9AA0A6;
  --color-gray-600: #80868B;
  --color-gray-700: #5F6368;
  --color-gray-800: #3C4043;
  --color-gray-900: #202124;

  /* Semantic Colors */
  --color-success: #2E7D32;
  --color-warning: #F57C00;
  --color-error: #D32F2F;
  --color-info: #1976D2;

  /* Text Colors */
  --color-text-primary: #202124;
  --color-text-secondary: #5F6368;
  --color-text-disabled: #9AA0A6;
  --color-text-inverse: #FFFFFF;

  /* Background Colors */
  --color-background-primary: #FFFFFF;
  --color-background-secondary: #F8F9FA;
  --color-background-tertiary: #F1F3F4;
  --color-background-hover: rgba(0, 0, 0, 0.04);
  --color-background-active: rgba(0, 0, 0, 0.08);
  --color-background-disabled: #F1F3F4;

  /* Border Colors */
  --color-border: #DADCE0;
  --color-border-hover: #BDC1C6;
  --color-border-focus: #1976D2;
  --color-border-error: #D32F2F;

  /* Focus Colors */
  --color-focus: #1976D2;
  --color-focus-ring: rgba(25, 118, 210, 0.2);

  /* ===== GLASSMORPHISM ===== */
  --glass-bg: rgba(255, 255, 255, 0.15);
  --glass-border: rgba(255, 255, 255, 0.25);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  --glass-backdrop: blur(20px);

  /* ===== TYPOGRAPHY ===== */
  
  /* Font Families */
  --font-family-primary: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-secondary: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'Fira Code', 'Monaco', 'Consolas', monospace;

  /* Font Sizes */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1.125rem; /* 18px - Large readable body text */
  --font-size-lg: 1.25rem;    /* 20px */
  --font-size-xl: 1.5rem;     /* 24px */
  --font-size-2xl: 1.875rem;  /* 30px */
  --font-size-3xl: 2.25rem;   /* 36px */
  --font-size-4xl: 3rem;      /* 48px - Large headings */
  --font-size-5xl: 3.75rem;   /* 60px */
  --font-size-6xl: 4.5rem;    /* 72px */

  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  --line-height-base: 1.6;

  /* Letter Spacing */
  --letter-spacing-tighter: -0.05em;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;

  /* ===== SPACING ===== */
  --spacing-0: 0;
  --spacing-px: 1px;
  --spacing-0-5: 0.125rem;  /* 2px */
  --spacing-1: 0.25rem;     /* 4px */
  --spacing-1-5: 0.375rem;  /* 6px */
  --spacing-2: 0.5rem;      /* 8px */
  --spacing-2-5: 0.625rem;  /* 10px */
  --spacing-3: 0.75rem;     /* 12px */
  --spacing-3-5: 0.875rem;  /* 14px */
  --spacing-4: 1rem;        /* 16px */
  --spacing-5: 1.25rem;     /* 20px */
  --spacing-6: 1.5rem;      /* 24px */
  --spacing-7: 1.75rem;     /* 28px */
  --spacing-8: 2rem;        /* 32px */
  --spacing-9: 2.25rem;     /* 36px */
  --spacing-10: 2.5rem;     /* 40px */
  --spacing-11: 2.75rem;    /* 44px */
  --spacing-12: 3rem;       /* 48px */
  --spacing-14: 3.5rem;     /* 56px */
  --spacing-16: 4rem;       /* 64px */
  --spacing-20: 5rem;       /* 80px */
  --spacing-24: 6rem;       /* 96px */
  --spacing-28: 7rem;       /* 112px */
  --spacing-32: 8rem;       /* 128px */
  --spacing-36: 9rem;       /* 144px */
  --spacing-40: 10rem;      /* 160px */
  --spacing-44: 11rem;      /* 176px */
  --spacing-48: 12rem;      /* 192px */
  --spacing-52: 13rem;      /* 208px */
  --spacing-56: 14rem;      /* 224px */
  --spacing-60: 15rem;      /* 240px */
  --spacing-64: 16rem;      /* 256px */
  --spacing-72: 18rem;      /* 288px */
  --spacing-80: 20rem;      /* 320px */
  --spacing-96: 24rem;      /* 384px */

  /* Semantic Spacing */
  --spacing-xs: var(--spacing-1);
  --spacing-sm: var(--spacing-2);
  --spacing-md: var(--spacing-4);
  --spacing-lg: var(--spacing-6);
  --spacing-xl: var(--spacing-8);
  --spacing-2xl: var(--spacing-12);
  --spacing-3xl: var(--spacing-16);
  --spacing-4xl: var(--spacing-24);

  /* ===== BORDER RADIUS ===== */
  --radius-none: 0;
  --radius-sm: 0.125rem;     /* 2px */
  --radius-base: 0.25rem;    /* 4px */
  --radius-md: 0.375rem;     /* 6px */
  --radius-lg: 0.5rem;       /* 8px */
  --radius-xl: 0.75rem;      /* 12px */
  --radius-2xl: 1rem;        /* 16px */
  --radius-3xl: 1.5rem;      /* 24px */
  --radius-full: 9999px;

  /* ===== SHADOWS ===== */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* ===== TRANSITIONS ===== */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
  --transition-all: all var(--transition-normal);

  /* ===== Z-INDEX ===== */
  --z-auto: auto;
  --z-0: 0;
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  --z-navigation: 1090;

  /* ===== LAYOUT ===== */
  --container-max-width: 1200px;
  --sidebar-width: 280px;
  --sidebar-width-collapsed: 70px;
  --header-height: 64px;
  --footer-height: 60px;

  /* ===== BREAKPOINTS ===== */
  --breakpoint-xs: 480px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* Dark Mode Variables */
@media (prefers-color-scheme: dark) {
  :root {
    --color-text-primary: #E8EAED;
    --color-text-secondary: #9AA0A6;
    --color-text-disabled: #5F6368;
    --color-text-inverse: #202124;

    --color-background-primary: #202124;
    --color-background-secondary: #303134;
    --color-background-tertiary: #3C4043;
    --color-background-hover: rgba(255, 255, 255, 0.04);
    --color-background-active: rgba(255, 255, 255, 0.08);
    --color-background-disabled: #3C4043;

    --color-border: #5F6368;
    --color-border-hover: #80868B;

    --glass-bg: rgba(0, 0, 0, 0.25);
    --glass-border: rgba(255, 255, 255, 0.15);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --color-border: #000000;
    --color-text-primary: #000000;
    --color-background-primary: #FFFFFF;
    --glass-bg: rgba(255, 255, 255, 0.9);
    --glass-border: #000000;
  }
}
