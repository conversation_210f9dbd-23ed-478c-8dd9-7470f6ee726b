/**
 * Image Optimization and Management System
 * Handles authentic South African livestock photography with proper optimization
 */

export interface ImageAsset {
  id: string;
  src: string;
  alt: string;
  category: 'cattle' | 'sheep' | 'goats' | 'farm' | 'dashboard' | 'hero';
  size: 'thumbnail' | 'medium' | 'large' | 'hero';
  optimized: boolean;
  webp?: string;
  avif?: string;
  placeholder?: string;
}

export interface BackgroundImage {
  id: string;
  src: string;
  alt: string;
  overlay: string;
  position: string;
  size: string;
  theme: 'beta' | 'professional' | 'landing';
}

// Authentic South African livestock photography catalog
export const livestockImages: ImageAsset[] = [
  // Cattle Images
  {
    id: 'cattle-1',
    src: '/images/animals/cattle-1.jpeg',
    alt: 'South African cattle grazing in natural pasture',
    category: 'cattle',
    size: 'large',
    optimized: true
  },
  {
    id: 'cattle-2',
    src: '/images/animals/cattle-2.avif',
    alt: 'Healthy cattle herd in South African farmland',
    category: 'cattle',
    size: 'hero',
    optimized: true,
    avif: '/images/animals/cattle-2.avif'
  },
  {
    id: 'cattle-3',
    src: '/images/animals/cattle-3.jpeg',
    alt: 'Premium beef cattle in managed grazing system',
    category: 'cattle',
    size: 'large',
    optimized: true
  },
  {
    id: 'cattle-4',
    src: '/images/animals/cattle-4.jpeg',
    alt: 'Dairy cattle in modern South African farm',
    category: 'cattle',
    size: 'medium',
    optimized: true
  },
  {
    id: 'cattle-5',
    src: '/images/animals/cattle-5.avif',
    alt: 'Cattle management in sustainable farming operation',
    category: 'cattle',
    size: 'hero',
    optimized: true,
    avif: '/images/animals/cattle-5.avif'
  },
  {
    id: 'cattle-6',
    src: '/images/animals/cattle-6.jpeg',
    alt: 'Traditional cattle farming in South Africa',
    category: 'cattle',
    size: 'large',
    optimized: true
  },
  {
    id: 'cows-1',
    src: '/images/animals/Cows 1.jpeg',
    alt: 'Dairy cows in professional farming environment',
    category: 'cattle',
    size: 'medium',
    optimized: true
  },
  {
    id: 'cows-2',
    src: '/images/animals/cows 2.avif',
    alt: 'Modern dairy operation with healthy cows',
    category: 'cattle',
    size: 'hero',
    optimized: true,
    avif: '/images/animals/cows 2.avif'
  },
  {
    id: 'cows-3',
    src: '/images/animals/cows 3.jpeg',
    alt: 'Cattle in natural South African landscape',
    category: 'cattle',
    size: 'large',
    optimized: true
  },
  {
    id: 'cows-4',
    src: '/images/animals/cows 4.jpeg',
    alt: 'Well-managed cattle herd in pasture',
    category: 'cattle',
    size: 'medium',
    optimized: true
  },
  {
    id: 'cows-5',
    src: '/images/animals/cows 5.avif',
    alt: 'Premium cattle breeding operation',
    category: 'cattle',
    size: 'hero',
    optimized: true,
    avif: '/images/animals/cows 5.avif'
  },

  // Sheep Images
  {
    id: 'sheep-1',
    src: '/images/animals/sheep-1.jpeg',
    alt: 'South African sheep flock in natural grazing',
    category: 'sheep',
    size: 'large',
    optimized: true
  },
  {
    id: 'sheep-2',
    src: '/images/animals/sheep-2.jpeg',
    alt: 'Healthy sheep herd in managed pasture system',
    category: 'sheep',
    size: 'medium',
    optimized: true
  },

  // Goat Images
  {
    id: 'goat-1',
    src: '/images/animals/goat-1.jpeg',
    alt: 'South African goats in sustainable farming system',
    category: 'goats',
    size: 'medium',
    optimized: true
  },
  {
    id: 'goat-2',
    src: '/images/animals/goat-2.jpeg',
    alt: 'Goat farming operation in South Africa',
    category: 'goats',
    size: 'large',
    optimized: true
  }
];

// Background images for different sections
export const backgroundImages: BackgroundImage[] = [
  // Landing Page Backgrounds
  {
    id: 'hero-cattle-1',
    src: '/images/animals/cattle-2.avif',
    alt: 'South African cattle farm landscape',
    overlay: 'linear-gradient(135deg, rgba(21, 101, 192, 0.85) 0%, rgba(46, 125, 50, 0.75) 50%, rgba(245, 124, 0, 0.65) 100%)',
    position: 'center',
    size: 'cover',
    theme: 'landing'
  },
  {
    id: 'hero-cattle-2',
    src: '/images/animals/cattle-5.avif',
    alt: 'Modern livestock management facility',
    overlay: 'linear-gradient(135deg, rgba(21, 101, 192, 0.85) 0%, rgba(46, 125, 50, 0.75) 50%, rgba(245, 124, 0, 0.65) 100%)',
    position: 'center',
    size: 'cover',
    theme: 'landing'
  },
  {
    id: 'hero-cows-1',
    src: '/images/animals/cows 2.avif',
    alt: 'Professional dairy operation',
    overlay: 'linear-gradient(135deg, rgba(21, 101, 192, 0.85) 0%, rgba(46, 125, 50, 0.75) 50%, rgba(245, 124, 0, 0.65) 100%)',
    position: 'center',
    size: 'cover',
    theme: 'landing'
  },

  // BETA Login Backgrounds
  {
    id: 'beta-login-1',
    src: '/images/animals/cattle-1.jpeg',
    alt: 'Traditional cattle farming for BETA users',
    overlay: 'linear-gradient(135deg, rgba(245, 124, 0, 0.85) 0%, rgba(255, 183, 77, 0.75) 50%, rgba(245, 124, 0, 0.85) 100%)',
    position: 'center',
    size: 'cover',
    theme: 'beta'
  },
  {
    id: 'beta-login-2',
    src: '/images/animals/cows 3.jpeg',
    alt: 'Small-scale farming operation',
    overlay: 'linear-gradient(135deg, rgba(245, 124, 0, 0.85) 0%, rgba(255, 183, 77, 0.75) 50%, rgba(245, 124, 0, 0.85) 100%)',
    position: 'center',
    size: 'cover',
    theme: 'beta'
  },

  // Professional Login Backgrounds
  {
    id: 'pro-login-1',
    src: '/images/animals/cattle-2.avif',
    alt: 'Advanced livestock management for professionals',
    overlay: 'linear-gradient(135deg, rgba(46, 125, 50, 0.85) 0%, rgba(102, 187, 106, 0.75) 50%, rgba(46, 125, 50, 0.85) 100%)',
    position: 'center',
    size: 'cover',
    theme: 'professional'
  },
  {
    id: 'pro-login-2',
    src: '/images/animals/cows 5.avif',
    alt: 'Premium cattle breeding operation',
    overlay: 'linear-gradient(135deg, rgba(46, 125, 50, 0.85) 0%, rgba(102, 187, 106, 0.75) 50%, rgba(46, 125, 50, 0.85) 100%)',
    position: 'center',
    size: 'cover',
    theme: 'professional'
  }
];

/**
 * Get random background image for theme
 */
export const getRandomBackgroundImage = (theme: 'beta' | 'professional' | 'landing'): BackgroundImage => {
  const themeImages = backgroundImages.filter(img => img.theme === theme);
  const randomIndex = Math.floor(Math.random() * themeImages.length);
  return themeImages[randomIndex] || backgroundImages[0];
};

/**
 * Get optimized image source with fallbacks
 */
export const getOptimizedImageSrc = (imageId: string): string => {
  const image = livestockImages.find(img => img.id === imageId);
  if (!image) return '/images/animals/cattle-1.jpeg'; // fallback
  
  // Return AVIF if available and supported
  if (image.avif && supportsAVIF()) {
    return image.avif;
  }
  
  // Return WebP if available and supported
  if (image.webp && supportsWebP()) {
    return image.webp;
  }
  
  // Return original source
  return image.src;
};

/**
 * Check if browser supports AVIF
 */
export const supportsAVIF = (): boolean => {
  const canvas = document.createElement('canvas');
  canvas.width = 1;
  canvas.height = 1;
  return canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0;
};

/**
 * Check if browser supports WebP
 */
export const supportsWebP = (): boolean => {
  const canvas = document.createElement('canvas');
  canvas.width = 1;
  canvas.height = 1;
  return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
};

/**
 * Generate CSS background style with optimization
 */
export const generateBackgroundStyle = (backgroundId: string): React.CSSProperties => {
  const background = backgroundImages.find(bg => bg.id === backgroundId);
  if (!background) return {};
  
  return {
    backgroundImage: `${background.overlay}, url('${background.src}')`,
    backgroundPosition: background.position,
    backgroundSize: background.size,
    backgroundRepeat: 'no-repeat',
    backgroundAttachment: 'fixed'
  };
};

/**
 * Preload critical images for better performance
 */
export const preloadCriticalImages = (): void => {
  const criticalImages = [
    '/images/animals/cattle-2.avif',
    '/images/animals/cattle-5.avif',
    '/images/animals/cows 2.avif',
    '/images/animals/cattle-1.jpeg'
  ];
  
  criticalImages.forEach(src => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = src;
    document.head.appendChild(link);
  });
};

/**
 * Lazy load images with intersection observer
 */
export const setupLazyLoading = (): void => {
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          img.src = img.dataset.src || '';
          img.classList.remove('lazy');
          observer.unobserve(img);
        }
      });
    });
    
    document.querySelectorAll('img[data-src]').forEach(img => {
      imageObserver.observe(img);
    });
  }
};

/**
 * Get image by category
 */
export const getImagesByCategory = (category: ImageAsset['category']): ImageAsset[] => {
  return livestockImages.filter(img => img.category === category);
};

/**
 * Get hero images
 */
export const getHeroImages = (): ImageAsset[] => {
  return livestockImages.filter(img => img.size === 'hero');
};
