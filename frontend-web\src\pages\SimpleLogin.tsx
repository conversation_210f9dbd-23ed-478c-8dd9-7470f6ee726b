import React, { useEffect } from 'react';
import '../styles/simple-login.css';

const SimpleLogin: React.FC = () => {
  useEffect(() => {
    // Auto redirect to dashboard after 2 seconds
    const timer = setTimeout(() => {
      window.location.href = '/dashboard';
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="simple-login">
      <div className="simple-login-container">
        <h1 className="simple-login-title">🌾 AgriIntel Login</h1>

        <div className="simple-login-success">
          <div className="simple-login-success-icon">✅</div>
          <h2>Login Successful!</h2>
          <p>Redirecting to dashboard...</p>
        </div>

        <div className="simple-login-loading">
          <div className="simple-login-dot"></div>
          <div className="simple-login-dot"></div>
          <div className="simple-login-dot"></div>
        </div>

        <a
          href="/dashboard"
          className="simple-login-button"
        >
          Go to Dashboard Now
        </a>

        <div className="simple-login-welcome">
          <p>Welcome to AgriIntel!</p>
          <p>Your livestock management platform</p>
        </div>
      </div>
    </div>
  );
};

export default SimpleLogin;
