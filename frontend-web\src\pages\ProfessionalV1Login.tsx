import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  TextField,
  Button,
  Typography,
  Alert,
  Chip,
  useTheme,
  alpha,
  InputAdornment,
  IconButton,
  CircularProgress,
  Divider
} from '@mui/material';
import {
  Person,
  Lock,
  Visibility,
  VisibilityOff,
  Business,
  CheckCircle
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import UnifiedLayout from '../components/layout/UnifiedLayout';
import LanguageSelector from '../components/common/LanguageSelector';

const ProfessionalV1Login: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { login } = useAuth();
  const theme = useTheme();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!username.trim() || !password.trim()) {
      setError('Please enter both username and password');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      await login(username, password);
      // Navigate to main dashboard (Professional users will see all modules)
      navigate('/dashboard', { replace: true });
    } catch (err: any) {
      setError(err.message || 'Login failed. Please check your credentials.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <UnifiedLayout
      title="Welcome to AgriIntel Professional V1"
      subtitle="Smart Farming, Smarter Decisions - Professional Access"
      backgroundImage="/images/modules/animals/cattle-2.jpeg"
      backgroundPosition="right"
      showBackground={true}
    >
      {/* Language Selector */}
      <Box display="flex" justifyContent="flex-end" mb={2}>
        <LanguageSelector variant="compact" showLabel={false} size="small" />
      </Box>

      <Box textAlign="center" mb={4}>
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.5, type: "spring", stiffness: 200 }}
        >
          <Box
            sx={{
              width: 80,
              height: 80,
              borderRadius: '50%',
              background: 'linear-gradient(135deg, #2E7D32, #4CAF50)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 16px',
              boxShadow: '0 8px 32px rgba(46, 125, 50, 0.3)',
            }}
          >
            <Business sx={{ fontSize: 40, color: 'white' }} />
          </Box>
        </motion.div>

        <Chip
          label="PROFESSIONAL V1 - R699/MONTH"
          sx={{
            mb: 2,
            fontWeight: 600,
            background: 'linear-gradient(45deg, #2E7D32, #4CAF50)',
            color: 'white',
            fontSize: '0.875rem'
          }}
        />

        <Typography
          variant="body2"
          sx={{
            color: theme.palette.text.secondary,
            mb: 2,
          }}
        >
          Unlock all modules, marketplace access, and AI automation
        </Typography>

        {/* Test Credentials */}
        <Box display="flex" gap={1} justifyContent="center" flexWrap="wrap" mb={2}>
          <Chip
            label="Pro: Pro/123"
            size="small"
            variant="outlined"
            onClick={() => { setUsername('Pro'); setPassword('123'); }}
            sx={{ cursor: 'pointer', '&:hover': { backgroundColor: alpha(theme.palette.success.main, 0.1) } }}
          />
        </Box>
      </Box>

      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Alert
              severity="error"
              sx={{
                mb: 3,
                borderRadius: 2,
                '& .MuiAlert-icon': {
                  fontSize: 20,
                }
              }}
            >
              {error}
            </Alert>
          </motion.div>
        )}
      </AnimatePresence>

      <form onSubmit={handleLogin}>
                <Box sx={{ textAlign: 'center', mb: 4 }}>
                  <img
                    src={`${process.env.PUBLIC_URL}/images/logo/AgriIntel Logo with Bright Accents and Livestock.png`}
                    alt="AgriIntel Logo"
                    className="agri-logo-pro"
                  />
                  <Chip
                    label="PROFESSIONAL V1"
                    sx={{
                      background: 'var(--agri-pro-gradient)',
                      color: 'white',
                      fontWeight: 700,
                      fontSize: '1rem',
                      mb: 2
                    }}
                  />
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'var(--agri-white)', mb: 1 }}>
                    Professional Access
                  </Typography>
                  <Typography variant="body1" sx={{ color: 'var(--agri-white)', opacity: 0.9 }}>
                    Complete Livestock Management with AI Automation
                  </Typography>
                </Box>

                {/* Login Form */}
                <form onSubmit={handleLogin}>
                  <Stack spacing={3}>
                    {error && (
                      <Alert severity="error" sx={{ borderRadius: 'var(--radius-lg)' }}>
                        {error}
                      </Alert>
                    )}

                    <TextField
                      fullWidth
                      label="Username"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      variant="outlined"
                      required
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'rgba(255, 255, 255, 0.1)',
                          '& fieldset': { borderColor: 'var(--glass-border)' },
                          '&:hover fieldset': { borderColor: 'var(--agri-pro-primary)' },
                          '&.Mui-focused fieldset': { borderColor: 'var(--agri-pro-primary)' }
                        },
                        '& .MuiInputLabel-root': { color: 'var(--agri-white)' },
                        '& .MuiInputBase-input': { color: 'var(--agri-white)' }
                      }}
                    />

                    <TextField
                      fullWidth
                      label="Password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      variant="outlined"
                      required
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'rgba(255, 255, 255, 0.1)',
                          '& fieldset': { borderColor: 'var(--glass-border)' },
                          '&:hover fieldset': { borderColor: 'var(--agri-pro-primary)' },
                          '&.Mui-focused fieldset': { borderColor: 'var(--agri-pro-primary)' }
                        },
                        '& .MuiInputLabel-root': { color: 'var(--agri-white)' },
                        '& .MuiInputBase-input': { color: 'var(--agri-white)' }
                      }}
                    />

                    <Button
                      type="submit"
                      fullWidth
                      variant="contained"
                      size="large"
                      disabled={isLoading}
                      sx={{
                        background: 'var(--agri-pro-gradient)',
                        color: 'white',
                        fontWeight: 600,
                        fontSize: '1.1rem',
                        py: 1.5,
                        borderRadius: 'var(--radius-lg)',
                        textTransform: 'none',
                        '&:hover': {
                          background: 'var(--agri-pro-gradient)',
                          transform: 'translateY(-2px)',
                          boxShadow: '0 8px 25px rgba(46, 125, 50, 0.3)'
                        }
                      }}
                    >
                      {isLoading ? 'Signing In...' : 'Access Professional Dashboard'}
                    </Button>
                  </Stack>
                </form>

                <Divider sx={{ my: 3, borderColor: 'var(--glass-border)' }} />

                {/* Demo Credentials */}
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="body2" sx={{ color: 'var(--agri-white)', opacity: 0.8, mb: 1 }}>
                    Demo Credentials:
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'var(--agri-pro-primary)', fontWeight: 600 }}>
                    Username: Pro | Password: 123
                  </Typography>
                </Box>

                {/* BETA Option */}
                <Box sx={{ mt: 3, textAlign: 'center' }}>
                  <Typography variant="body2" sx={{ color: 'var(--agri-white)', opacity: 0.8, mb: 2 }}>
                    Want to try first?
                  </Typography>
                  <Button
                    variant="outlined"
                    onClick={() => navigate('/login-beta')}
                    sx={{
                      color: 'var(--agri-beta-primary)',
                      borderColor: 'var(--agri-beta-primary)',
                      '&:hover': {
                        backgroundColor: 'rgba(245, 124, 0, 0.1)',
                        borderColor: 'var(--agri-beta-primary)'
                      }
                    }}
                  >
                    Start with BETA V1 (Free Trial)
                  </Button>
                </Box>
              </Paper>
            </motion.div>
          </Grid>

          {/* Right Side - Features Overview */}
          <Grid item xs={12} md={6}>
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Box sx={{ color: 'var(--agri-white)' }}>
                <Typography variant="h3" sx={{ fontWeight: 700, mb: 2 }}>
                  Professional V1 Features
                </Typography>
                <Typography variant="h6" sx={{ opacity: 0.9, mb: 4, fontSize: 'var(--font-size-base)' }}>
                  Complete livestock management with AI automation and marketplace connections
                </Typography>

                {/* Professional Features */}
                <Paper className="glass-card-pro" sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" sx={{ color: 'var(--agri-white)', mb: 2, fontWeight: 600 }}>
                    🚀 Professional Features
                  </Typography>
                  <List dense>
                    {professionalFeatures.map((feature, index) => (
                      <ListItem key={index} sx={{ px: 0 }}>
                        <ListItemIcon sx={{ color: 'var(--agri-pro-primary)', minWidth: 36 }}>
                          {feature.icon}
                        </ListItemIcon>
                        <ListItemText
                          primary={feature.text}
                          sx={{ '& .MuiListItemText-primary': { color: 'var(--agri-white)', fontSize: 'var(--font-size-base)' } }}
                        />
                        <CheckCircle sx={{ color: 'var(--agri-pro-primary)', ml: 1 }} />
                      </ListItem>
                    ))}
                  </List>
                </Paper>

                {/* Marketplace Features */}
                <Paper className="glass-card-pro" sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ color: 'var(--agri-white)', mb: 2, fontWeight: 600 }}>
                    🏪 Marketplace Connections (Uber-style)
                  </Typography>
                  <List dense>
                    {marketplaceFeatures.map((feature, index) => (
                      <ListItem key={index} sx={{ px: 0 }}>
                        <ListItemIcon sx={{ color: 'var(--agri-pro-primary)', minWidth: 36 }}>
                          <Store />
                        </ListItemIcon>
                        <ListItemText
                          primary={feature}
                          sx={{ '& .MuiListItemText-primary': { color: 'var(--agri-white)', fontSize: 'var(--font-size-base)' } }}
                        />
                      </ListItem>
                    ))}
                  </List>
                  
                  <Box sx={{ mt: 2, p: 2, background: 'rgba(46, 125, 50, 0.2)', borderRadius: 'var(--radius-lg)' }}>
                    <Typography variant="body2" sx={{ color: 'var(--agri-white)', fontWeight: 600 }}>
                      💰 R699/month - Increase revenue by 25% with marketplace connections
                    </Typography>
                  </Box>
                </Paper>
              </Box>
            </motion.div>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default ProfessionalV1Login;
