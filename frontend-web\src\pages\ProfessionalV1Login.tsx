import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Container,
  Alert,
  Chip,
  Paper,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Stack,
  Divider,
  useTheme,
  alpha
} from '@mui/material';
import {
  CheckCircle,
  Star,
  Agriculture,
  HealthAndSafety,
  Restaurant,
  Assessment,
  Support,
  PhoneAndroid,
  AutoAwesome,
  Store,
  Analytics,
  Dashboard,
  TrendingUp,
  Security,
  Smartphone
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import '../styles/professional-v1-login.css';

const ProfessionalV1Login: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { login } = useAuth();
  const theme = useTheme();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!username.trim() || !password.trim()) {
      setError('Please enter both username and password');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      await login(username, password);
      // Navigate to professional dashboard
      navigate('/dashboard', { replace: true });
    } catch (err: any) {
      setError(err.message || 'Login failed. Please check your credentials.');
    } finally {
      setIsLoading(false);
    }
  };

  const professionalFeatures = [
    { icon: <Dashboard />, text: 'Advanced Dashboard & Analytics', premium: true },
    { icon: <Agriculture />, text: 'Unlimited Animal Management', premium: true },
    { icon: <HealthAndSafety />, text: 'AI-Powered Health Monitoring', premium: true },
    { icon: <Restaurant />, text: 'Smart Feed Optimization', premium: true },
    { icon: <TrendingUp />, text: 'Advanced Financial Analytics', premium: true },
    { icon: <Assessment />, text: 'Custom Reports & Insights', premium: true },
    { icon: <Store />, text: 'Marketplace Connections', premium: true },
    { icon: <AutoAwesome />, text: 'AI Automation & Predictions', premium: true },
    { icon: <Smartphone />, text: 'Full Mobile App Access', premium: true },
    { icon: <Security />, text: 'Priority Support (2hr response)', premium: true }
  ];

  const marketplaceFeatures = [
    'Veterinarian Network Access',
    'Supplier Connections (Uber-style)',
    'Auctioneer Partnerships',
    'Security Service Providers',
    'In-app Payment System',
    'Revenue Optimization Tools'
  ];

  return (
    <Box className="professional-v1-login-container">
      {/* Background with Authentic South African Livestock Photography */}
      <div className="login-background-pro login-background-pro-dynamic">
        <div className="background-overlay-pro" />
      </div>

      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1, py: 4 }}>
        <Grid container spacing={4} alignItems="center" sx={{ minHeight: '100vh' }}>
          {/* Left Side - Login Form */}
          <Grid item xs={12} md={6}>
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Paper className="glass-card-pro" sx={{ p: 4, maxWidth: 480, mx: 'auto' }}>
                {/* Professional V1 Branding */}
                <Box sx={{ textAlign: 'center', mb: 4 }}>
                  <img
                    src={`${process.env.PUBLIC_URL}/images/logo/AgriIntel Logo with Bright Accents and Livestock.png`}
                    alt="AgriIntel Logo"
                    className="agri-logo-pro"
                  />
                  <Chip
                    label="PROFESSIONAL V1"
                    sx={{
                      background: 'var(--agri-pro-gradient)',
                      color: 'white',
                      fontWeight: 700,
                      fontSize: '1rem',
                      mb: 2
                    }}
                  />
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'var(--agri-white)', mb: 1 }}>
                    Professional Access
                  </Typography>
                  <Typography variant="body1" sx={{ color: 'var(--agri-white)', opacity: 0.9 }}>
                    Complete Livestock Management with AI Automation
                  </Typography>
                </Box>

                {/* Login Form */}
                <form onSubmit={handleLogin}>
                  <Stack spacing={3}>
                    {error && (
                      <Alert severity="error" sx={{ borderRadius: 'var(--radius-lg)' }}>
                        {error}
                      </Alert>
                    )}

                    <TextField
                      fullWidth
                      label="Username"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      variant="outlined"
                      required
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'rgba(255, 255, 255, 0.1)',
                          '& fieldset': { borderColor: 'var(--glass-border)' },
                          '&:hover fieldset': { borderColor: 'var(--agri-pro-primary)' },
                          '&.Mui-focused fieldset': { borderColor: 'var(--agri-pro-primary)' }
                        },
                        '& .MuiInputLabel-root': { color: 'var(--agri-white)' },
                        '& .MuiInputBase-input': { color: 'var(--agri-white)' }
                      }}
                    />

                    <TextField
                      fullWidth
                      label="Password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      variant="outlined"
                      required
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'rgba(255, 255, 255, 0.1)',
                          '& fieldset': { borderColor: 'var(--glass-border)' },
                          '&:hover fieldset': { borderColor: 'var(--agri-pro-primary)' },
                          '&.Mui-focused fieldset': { borderColor: 'var(--agri-pro-primary)' }
                        },
                        '& .MuiInputLabel-root': { color: 'var(--agri-white)' },
                        '& .MuiInputBase-input': { color: 'var(--agri-white)' }
                      }}
                    />

                    <Button
                      type="submit"
                      fullWidth
                      variant="contained"
                      size="large"
                      disabled={isLoading}
                      sx={{
                        background: 'var(--agri-pro-gradient)',
                        color: 'white',
                        fontWeight: 600,
                        fontSize: '1.1rem',
                        py: 1.5,
                        borderRadius: 'var(--radius-lg)',
                        textTransform: 'none',
                        '&:hover': {
                          background: 'var(--agri-pro-gradient)',
                          transform: 'translateY(-2px)',
                          boxShadow: '0 8px 25px rgba(46, 125, 50, 0.3)'
                        }
                      }}
                    >
                      {isLoading ? 'Signing In...' : 'Access Professional Dashboard'}
                    </Button>
                  </Stack>
                </form>

                <Divider sx={{ my: 3, borderColor: 'var(--glass-border)' }} />

                {/* Demo Credentials */}
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="body2" sx={{ color: 'var(--agri-white)', opacity: 0.8, mb: 1 }}>
                    Demo Credentials:
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'var(--agri-pro-primary)', fontWeight: 600 }}>
                    Username: Pro | Password: 123
                  </Typography>
                </Box>

                {/* BETA Option */}
                <Box sx={{ mt: 3, textAlign: 'center' }}>
                  <Typography variant="body2" sx={{ color: 'var(--agri-white)', opacity: 0.8, mb: 2 }}>
                    Want to try first?
                  </Typography>
                  <Button
                    variant="outlined"
                    onClick={() => navigate('/login-beta')}
                    sx={{
                      color: 'var(--agri-beta-primary)',
                      borderColor: 'var(--agri-beta-primary)',
                      '&:hover': {
                        backgroundColor: 'rgba(245, 124, 0, 0.1)',
                        borderColor: 'var(--agri-beta-primary)'
                      }
                    }}
                  >
                    Start with BETA V1 (Free Trial)
                  </Button>
                </Box>
              </Paper>
            </motion.div>
          </Grid>

          {/* Right Side - Features Overview */}
          <Grid item xs={12} md={6}>
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Box sx={{ color: 'var(--agri-white)' }}>
                <Typography variant="h3" sx={{ fontWeight: 700, mb: 2 }}>
                  Professional V1 Features
                </Typography>
                <Typography variant="h6" sx={{ opacity: 0.9, mb: 4, fontSize: 'var(--font-size-base)' }}>
                  Complete livestock management with AI automation and marketplace connections
                </Typography>

                {/* Professional Features */}
                <Paper className="glass-card-pro" sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" sx={{ color: 'var(--agri-white)', mb: 2, fontWeight: 600 }}>
                    🚀 Professional Features
                  </Typography>
                  <List dense>
                    {professionalFeatures.map((feature, index) => (
                      <ListItem key={index} sx={{ px: 0 }}>
                        <ListItemIcon sx={{ color: 'var(--agri-pro-primary)', minWidth: 36 }}>
                          {feature.icon}
                        </ListItemIcon>
                        <ListItemText
                          primary={feature.text}
                          sx={{ '& .MuiListItemText-primary': { color: 'var(--agri-white)', fontSize: 'var(--font-size-base)' } }}
                        />
                        <CheckCircle sx={{ color: 'var(--agri-pro-primary)', ml: 1 }} />
                      </ListItem>
                    ))}
                  </List>
                </Paper>

                {/* Marketplace Features */}
                <Paper className="glass-card-pro" sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ color: 'var(--agri-white)', mb: 2, fontWeight: 600 }}>
                    🏪 Marketplace Connections (Uber-style)
                  </Typography>
                  <List dense>
                    {marketplaceFeatures.map((feature, index) => (
                      <ListItem key={index} sx={{ px: 0 }}>
                        <ListItemIcon sx={{ color: 'var(--agri-pro-primary)', minWidth: 36 }}>
                          <Store />
                        </ListItemIcon>
                        <ListItemText
                          primary={feature}
                          sx={{ '& .MuiListItemText-primary': { color: 'var(--agri-white)', fontSize: 'var(--font-size-base)' } }}
                        />
                      </ListItem>
                    ))}
                  </List>
                  
                  <Box sx={{ mt: 2, p: 2, background: 'rgba(46, 125, 50, 0.2)', borderRadius: 'var(--radius-lg)' }}>
                    <Typography variant="body2" sx={{ color: 'var(--agri-white)', fontWeight: 600 }}>
                      💰 R699/month - Increase revenue by 25% with marketplace connections
                    </Typography>
                  </Box>
                </Paper>
              </Box>
            </motion.div>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default ProfessionalV1Login;
