import React from 'react';
import { Box, Typography, useTheme, alpha } from '@mui/material';
import { useThemeContext } from '../../contexts/ThemeContext';
import '../../styles/components/agriintel-branding.css';

interface AgriIntelBrandProps {
  variant?: 'full' | 'compact' | 'logo-only' | 'consciousness' | 'quantum';
  size?: 'small' | 'medium' | 'large' | 'extra-large' | 'consciousness';
  showSlogan?: boolean;
  color?: 'primary' | 'secondary' | 'white' | 'inherit' | 'consciousness' | 'quantum';
  orientation?: 'horizontal' | 'vertical';
  animated?: boolean;
  consciousness?: boolean;
  quantum?: boolean;
  sx?: any;
}

const AgriIntelBrand: React.FC<AgriIntelBrandProps> = ({
  variant = 'consciousness',
  size = 'consciousness',
  showSlogan = true,
  color = 'consciousness',
  orientation = 'horizontal',
  animated = true,
  consciousness = true,
  quantum = true,
  sx = {}
}) => {
  const theme = useTheme();
  const { currentColor, availableColors } = useThemeContext();

  const themeColor = availableColors[currentColor];

  // 2029 Quantum Consciousness Size configurations
  const sizeConfig = {
    small: {
      logoSize: 40,
      fontSize: '1.5rem',
      sloganSize: '0.8rem',
      spacing: 1.2
    },
    medium: {
      logoSize: 56,
      fontSize: '2.2rem',
      sloganSize: '1rem',
      spacing: 1.8
    },
    large: {
      logoSize: 80,
      fontSize: '3.2rem',
      sloganSize: '1.3rem',
      spacing: 2.4
    },
    'extra-large': {
      logoSize: 112,
      fontSize: '4.5rem',
      sloganSize: '1.6rem',
      spacing: 3.2
    },
    consciousness: {
      logoSize: 96,
      fontSize: '3.8rem',
      sloganSize: '1.4rem',
      spacing: 2.8
    }
  };

  const config = sizeConfig[size];

  // 2029 Quantum Consciousness Color configurations
  const getColorStyle = () => {
    switch (color) {
      case 'consciousness':
        return {
          primary: '#00FFFF',
          secondary: '#FF00FF',
          gradient: 'conic-gradient(from 0deg at 50% 50%, #00ffff 0deg, #ff00ff 72deg, #ffff00 144deg, #00ff00 216deg, #ff0000 288deg, #00ffff 360deg)',
          textGradient: 'conic-gradient(from 45deg, #00ffff, #ff00ff, #ffff00, #00ff00, #ff0000, #00ffff)',
          logoGradient: 'radial-gradient(ellipse at center, #00f5ff 0%, #0066ff 25%, #6600ff 50%, #ff0066 75%, #ff6600 100%)'
        };
      case 'quantum':
        return {
          primary: '#00F5FF',
          secondary: '#FF0066',
          gradient: 'radial-gradient(ellipse at center, #00f5ff 0%, #0066ff 25%, #6600ff 50%, #ff0066 75%, #ff6600 100%)',
          textGradient: 'linear-gradient(135deg, #00f5ff 0%, #0066ff 25%, #6600ff 50%, #ff0066 75%, #ff6600 100%)',
          logoGradient: 'conic-gradient(from 90deg, #00f5ff, #0066ff, #6600ff, #ff0066, #ff6600, #00f5ff)'
        };
      case 'white':
        return {
          primary: '#FFFFFF',
          secondary: alpha('#FFFFFF', 0.9),
          gradient: 'linear-gradient(135deg, #FFFFFF 0%, rgba(255,255,255,0.95) 100%)',
          textGradient: 'linear-gradient(135deg, #FFFFFF 0%, rgba(255,255,255,0.9) 100%)',
          logoGradient: 'linear-gradient(135deg, #FFFFFF 0%, rgba(255,255,255,0.8) 100%)'
        };
      case 'inherit':
        return {
          primary: 'inherit',
          secondary: 'inherit',
          gradient: 'none',
          textGradient: 'none',
          logoGradient: 'none'
        };
      default:
        return {
          primary: themeColor?.primary || theme.palette.primary.main,
          secondary: themeColor?.secondary || theme.palette.secondary.main,
          gradient: `conic-gradient(from 0deg,
            ${themeColor?.primary || '#22C55E'} 0deg,
            ${themeColor?.secondary || '#3B82F6'} 72deg,
            #A855F7 144deg,
            #F59E0B 216deg,
            #EF4444 288deg,
            ${themeColor?.primary || '#22C55E'} 360deg)`,
          textGradient: `linear-gradient(135deg,
            ${themeColor?.primary || '#22C55E'} 0%,
            ${themeColor?.secondary || '#3B82F6'} 25%,
            #A855F7 50%,
            #F59E0B 75%,
            #EF4444 100%)`,
          logoGradient: `radial-gradient(ellipse,
            ${themeColor?.primary || '#22C55E'} 0%,
            ${themeColor?.secondary || '#3B82F6'} 50%,
            #A855F7 100%)`
        };
    }
  };

  const colorStyle = getColorStyle();

  // AgriIntel Logo SVG Component
  const AgriIntelLogo = ({ size: logoSize }: { size: number }) => (
    <svg width={logoSize} height={logoSize * 0.8} viewBox="0 0 60 48" xmlns="http://www.w3.org/2000/svg" className="agri-logo">
      <defs>
        <linearGradient id={`agriGradient-${logoSize}`} x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" className="agri-logo-gradient-primary" />
          <stop offset="25%" className="agri-logo-gradient-secondary" />
          <stop offset="50%" className="agri-logo-gradient-purple" />
          <stop offset="75%" className="agri-logo-gradient-amber" />
          <stop offset="100%" className="agri-logo-gradient-red" />
        </linearGradient>

        <linearGradient id={`iconGradient-${logoSize}`} x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" className="agri-logo-icon-green" />
          <stop offset="50%" className="agri-logo-icon-teal" />
          <stop offset="100%" className="agri-logo-icon-teal-dark" />
        </linearGradient>
      </defs>

      {/* Farm Building */}
      <g transform="translate(5, 8)">
        <path d="M5 25 L15 15 L25 25 L25 35 L5 35 Z" fill={`url(#iconGradient-${logoSize})`} stroke="var(--agri-forest)" strokeWidth="1"/>
        <path d="M3 25 L15 13 L27 25" fill="none" stroke="var(--agri-forest)" strokeWidth="1.5" strokeLinecap="round"/>
        <rect x="12" y="28" width="6" height="7" fill="var(--agri-forest)"/>
        <rect x="7" y="22" width="4" height="4" fill="var(--agri-cream)"/>
        <rect x="19" y="22" width="4" height="4" fill="var(--agri-cream)"/>

        {/* Livestock */}
        <ellipse cx="35" cy="32" rx="8" ry="4" fill="var(--agri-accent-purple)" opacity="0.8"/>
        <ellipse cx="35" cy="30" rx="6" ry="3" fill="var(--agri-accent-purple)"/>
      </g>
    </svg>
  );

  // Animation styles
  const animationStyles = animated ? {
    '@keyframes agriShimmer': {
      '0%': { backgroundPosition: '-200% center' },
      '100%': { backgroundPosition: '200% center' }
    },
    animation: 'agriShimmer 3s ease-in-out infinite',
    backgroundSize: '200% 100%'
  } : {};

  // Brand text styles
  const getBrandTextStyle = () => {
    const baseStyle = {
      fontWeight: 'bold',
      letterSpacing: '-0.02em',
      fontFamily: '"Segoe UI", "Roboto", "Arial", sans-serif',
      ...animationStyles
    };

    switch (color) {
      case 'primary':
        return {
          ...baseStyle,
          background: colorStyle.gradient,
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text'
        };
      case 'white':
        return {
          ...baseStyle,
          color: '#FFFFFF',
          textShadow: `0 2px 4px ${alpha('#000000', 0.3)}`
        };
      case 'inherit':
        return {
          ...baseStyle,
          color: 'inherit'
        };
      default:
        return {
          ...baseStyle,
          color: colorStyle.primary
        };
    }
  };

  // Slogan text styles
  const getSloganTextStyle = () => {
    const baseStyle = {
      fontWeight: 500,
      fontFamily: '"Segoe UI", "Roboto", "Arial", sans-serif',
      letterSpacing: '0.5px'
    };

    switch (color) {
      case 'primary':
        return {
          ...baseStyle,
          background: `linear-gradient(135deg, ${alpha(colorStyle.primary, 0.8)}, ${alpha(colorStyle.secondary || '#3B82F6', 0.8)})`,
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text'
        };
      case 'white':
        return {
          ...baseStyle,
          color: alpha('#FFFFFF', 0.9),
          textShadow: `0 1px 2px ${alpha('#000000', 0.3)}`
        };
      case 'inherit':
        return {
          ...baseStyle,
          color: 'inherit'
        };
      default:
        return {
          ...baseStyle,
          color: theme.palette.text.secondary
        };
    }
  };

  const brandTextStyle = getBrandTextStyle();
  const sloganTextStyle = getSloganTextStyle();

  if (variant === 'logo-only') {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', ...sx }}>
        <AgriIntelLogo size={config.logoSize} />
      </Box>
    );
  }

  if (variant === 'compact') {
    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          flexDirection: orientation === 'vertical' ? 'column' : 'row',
          textAlign: orientation === 'vertical' ? 'center' : 'left',
          gap: config.spacing,
          ...sx
        }}
      >
        <AgriIntelLogo size={config.logoSize} />
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: orientation === 'vertical' ? 'center' : 'flex-start' }}>
          <Typography
            variant="h4"
            component="span"
            sx={{
              fontSize: config.fontSize,
              ...brandTextStyle
            }}
          >
            AgriIntel
          </Typography>
          {showSlogan && (
            <Typography
              variant="caption"
              component="span"
              sx={{
                fontSize: config.sloganSize,
                ...sloganTextStyle
              }}
            >
              Smart Farming, Smarter Decisions
            </Typography>
          )}
        </Box>
      </Box>
    );
  }

  // Full variant
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: orientation === 'vertical' ? 'column' : 'row',
        alignItems: orientation === 'vertical' ? 'center' : 'center',
        textAlign: orientation === 'vertical' ? 'center' : 'left',
        gap: config.spacing,
        ...sx
      }}
    >
      <AgriIntelLogo size={config.logoSize} />
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: orientation === 'vertical' ? 'center' : 'flex-start' }}>
        <Typography
          variant="h1"
          component="h1"
          sx={{
            fontSize: config.fontSize,
            lineHeight: 1.1,
            ...brandTextStyle
          }}
        >
          AgriIntel
        </Typography>
        {showSlogan && (
          <Typography
            variant="subtitle1"
            component="p"
            sx={{
              fontSize: config.sloganSize,
              lineHeight: 1.2,
              mt: 0.5,
              ...sloganTextStyle
            }}
          >
            Smart Farming, Smarter Decisions
          </Typography>
        )}
      </Box>
    </Box>
  );
};

export default AgriIntelBrand;
