import React from 'react';
import { Box, Typography, useTheme, alpha } from '@mui/material';
import './AgriIntelBrand.css';
import { useThemeContext } from '../../contexts/ThemeContext';
import '../../styles/components/agriintel-branding.css';

interface AgriIntelBrandProps {
  variant?: 'full' | 'compact' | 'logo-only' | 'consciousness' | 'quantum';
  size?: 'small' | 'medium' | 'large' | 'extra-large' | 'consciousness';
  showSlogan?: boolean;
  color?: 'primary' | 'secondary' | 'white' | 'inherit' | 'consciousness' | 'quantum';
  orientation?: 'horizontal' | 'vertical';
  animated?: boolean;
  consciousness?: boolean;
  quantum?: boolean;
  sx?: any;
}

const AgriIntelBrand: React.FC<AgriIntelBrandProps> = ({
  variant = 'consciousness',
  size = 'consciousness',
  showSlogan = true,
  color = 'consciousness',
  orientation = 'horizontal',
  animated = true,
  consciousness = true,
  quantum = true,
  sx = {}
}) => {
  const theme = useTheme();
  const { currentColor, availableColors } = useThemeContext();

  const themeColor = availableColors[currentColor];

  // 2029 Quantum Consciousness Size configurations
  const sizeConfig = {
    small: {
      logoSize: 40,
      fontSize: '1.5rem',
      sloganSize: '0.8rem',
      spacing: 1.2
    },
    medium: {
      logoSize: 56,
      fontSize: '2.2rem',
      sloganSize: '1rem',
      spacing: 1.8
    },
    large: {
      logoSize: 80,
      fontSize: '3.2rem',
      sloganSize: '1.3rem',
      spacing: 2.4
    },
    'extra-large': {
      logoSize: 112,
      fontSize: '4.5rem',
      sloganSize: '1.6rem',
      spacing: 3.2
    },
    consciousness: {
      logoSize: 96,
      fontSize: '3.8rem',
      sloganSize: '1.4rem',
      spacing: 2.8
    }
  };

  const config = sizeConfig[size];

  // 2029 Quantum Consciousness Color configurations
  const getColorStyle = () => {
    switch (color) {
      case 'consciousness':
        return {
          primary: '#00FFFF',
          secondary: '#FF00FF',
          gradient: 'conic-gradient(from 0deg at 50% 50%, #00ffff 0deg, #ff00ff 72deg, #ffff00 144deg, #00ff00 216deg, #ff0000 288deg, #00ffff 360deg)',
          textGradient: 'conic-gradient(from 45deg, #00ffff, #ff00ff, #ffff00, #00ff00, #ff0000, #00ffff)',
          logoGradient: 'radial-gradient(ellipse at center, #00f5ff 0%, #0066ff 25%, #6600ff 50%, #ff0066 75%, #ff6600 100%)'
        };
      case 'quantum':
        return {
          primary: '#00F5FF',
          secondary: '#FF0066',
          gradient: 'radial-gradient(ellipse at center, #00f5ff 0%, #0066ff 25%, #6600ff 50%, #ff0066 75%, #ff6600 100%)',
          textGradient: 'linear-gradient(135deg, #00f5ff 0%, #0066ff 25%, #6600ff 50%, #ff0066 75%, #ff6600 100%)',
          logoGradient: 'conic-gradient(from 90deg, #00f5ff, #0066ff, #6600ff, #ff0066, #ff6600, #00f5ff)'
        };
      case 'white':
        return {
          primary: '#FFFFFF',
          secondary: alpha('#FFFFFF', 0.9),
          gradient: 'linear-gradient(135deg, #FFFFFF 0%, rgba(255,255,255,0.95) 100%)',
          textGradient: 'linear-gradient(135deg, #FFFFFF 0%, rgba(255,255,255,0.9) 100%)',
          logoGradient: 'linear-gradient(135deg, #FFFFFF 0%, rgba(255,255,255,0.8) 100%)'
        };
      case 'inherit':
        return {
          primary: 'inherit',
          secondary: 'inherit',
          gradient: 'none',
          textGradient: 'none',
          logoGradient: 'none'
        };
      default:
        return {
          primary: themeColor?.primary || theme.palette.primary.main,
          secondary: themeColor?.secondary || theme.palette.secondary.main,
          gradient: `conic-gradient(from 0deg,
            ${themeColor?.primary || '#22C55E'} 0deg,
            ${themeColor?.secondary || '#3B82F6'} 72deg,
            #A855F7 144deg,
            #F59E0B 216deg,
            #EF4444 288deg,
            ${themeColor?.primary || '#22C55E'} 360deg)`,
          textGradient: `linear-gradient(135deg,
            ${themeColor?.primary || '#22C55E'} 0%,
            ${themeColor?.secondary || '#3B82F6'} 25%,
            #A855F7 50%,
            #F59E0B 75%,
            #EF4444 100%)`,
          logoGradient: `radial-gradient(ellipse,
            ${themeColor?.primary || '#22C55E'} 0%,
            ${themeColor?.secondary || '#3B82F6'} 50%,
            #A855F7 100%)`
        };
    }
  };

  const colorStyle = getColorStyle();

  // 2029 Quantum Consciousness AgriIntel Logo
  const AgriIntelLogo = ({ size: logoSize }: { size: number }) => (
    <svg
      width={logoSize}
      height={logoSize}
      viewBox="0 0 100 100"
      xmlns="http://www.w3.org/2000/svg"
      className={`agri-logo quantum-consciousness-logo ${consciousness ? 'consciousness-glow' : ''} ${animated ? 'consciousness-animated' : ''}`}
    >
      <defs>
        {/* Quantum Consciousness Gradients */}
        <radialGradient id={`consciousnessGradient-${logoSize}`} cx="50%" cy="50%" r="50%">
          <stop offset="0%" stopColor="#00ffff" stopOpacity="1" />
          <stop offset="25%" stopColor="#0066ff" stopOpacity="0.9" />
          <stop offset="50%" stopColor="#6600ff" stopOpacity="0.8" />
          <stop offset="75%" stopColor="#ff0066" stopOpacity="0.9" />
          <stop offset="100%" stopColor="#ff6600" stopOpacity="1" />
        </radialGradient>

        <linearGradient id={`quantumGradient-${logoSize}`} x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#00ffff" />
          <stop offset="20%" stopColor="#ff00ff" />
          <stop offset="40%" stopColor="#ffff00" />
          <stop offset="60%" stopColor="#00ff00" />
          <stop offset="80%" stopColor="#ff0000" />
          <stop offset="100%" stopColor="#00ffff" />
        </linearGradient>

        {/* Neural Network Pattern */}
        <pattern id={`neuralPattern-${logoSize}`} x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
          <circle cx="10" cy="10" r="1" fill="rgba(0, 255, 255, 0.3)" />
          <line x1="10" y1="10" x2="20" y2="10" stroke="rgba(0, 255, 255, 0.2)" strokeWidth="0.5" />
          <line x1="10" y1="10" x2="10" y2="20" stroke="rgba(255, 0, 255, 0.2)" strokeWidth="0.5" />
        </pattern>

        {/* Biomorphic Filter */}
        <filter id={`biomorphicGlow-${logoSize}`} x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
          <feMerge>
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
      </defs>

      {/* Quantum Consciousness Background */}
      <circle
        cx="50"
        cy="50"
        r="45"
        fill={`url(#consciousnessGradient-${logoSize})`}
        opacity="0.1"
        filter={`url(#biomorphicGlow-${logoSize})`}
      />

      {/* Neural Network Background */}
      <circle
        cx="50"
        cy="50"
        r="40"
        fill={`url(#neuralPattern-${logoSize})`}
        opacity="0.3"
      />

      {/* Central Quantum Core */}
      <circle
        cx="50"
        cy="50"
        r="25"
        fill={`url(#quantumGradient-${logoSize})`}
        opacity="0.8"
        filter={`url(#biomorphicGlow-${logoSize})`}
      />

      {/* Agricultural Consciousness Symbols */}
      <g transform="translate(50, 50)" opacity="0.9">
        {/* Quantum Leaf */}
        <path
          d="M-15,-10 Q-20,-15 -10,-20 Q0,-15 5,-10 Q0,-5 -10,0 Q-20,-5 -15,-10 Z"
          fill="rgba(0, 255, 0, 0.8)"
          filter={`url(#biomorphicGlow-${logoSize})`}
        />

        {/* Neural Livestock */}
        <ellipse
          cx="8"
          cy="5"
          rx="12"
          ry="6"
          fill="rgba(255, 255, 0, 0.7)"
          filter={`url(#biomorphicGlow-${logoSize})`}
        />

        {/* Consciousness Farm Structure */}
        <path
          d="M-8,10 L0,0 L8,10 L8,15 L-8,15 Z"
          fill="rgba(0, 255, 255, 0.6)"
          filter={`url(#biomorphicGlow-${logoSize})`}
        />

        {/* Quantum Data Points */}
        <circle cx="-20" cy="-5" r="2" fill="rgba(255, 0, 255, 0.9)" />
        <circle cx="20" cy="-5" r="2" fill="rgba(255, 0, 255, 0.9)" />
        <circle cx="0" cy="-25" r="2" fill="rgba(255, 0, 255, 0.9)" />
        <circle cx="0" cy="25" r="2" fill="rgba(255, 0, 255, 0.9)" />

        {/* Neural Connections */}
        <line x1="-20" y1="-5" x2="0" y2="0" stroke="rgba(0, 255, 255, 0.4)" strokeWidth="1" />
        <line x1="20" y1="-5" x2="0" y2="0" stroke="rgba(0, 255, 255, 0.4)" strokeWidth="1" />
        <line x1="0" y1="-25" x2="0" y2="0" stroke="rgba(0, 255, 255, 0.4)" strokeWidth="1" />
        <line x1="0" y1="25" x2="0" y2="0" stroke="rgba(0, 255, 255, 0.4)" strokeWidth="1" />
      </g>

      {/* Outer Quantum Ring */}
      <circle
        cx="50"
        cy="50"
        r="48"
        fill="none"
        stroke={`url(#quantumGradient-${logoSize})`}
        strokeWidth="2"
        opacity="0.6"
        strokeDasharray="5,5"
      />
    </svg>
  );

  // 2029 Quantum Consciousness Animation styles
  const animationStyles = animated ? {
    '@keyframes quantumConsciousness': {
      '0%': {
        backgroundPosition: '0% 50%',
        filter: 'hue-rotate(0deg) brightness(1)'
      },
      '25%': {
        backgroundPosition: '100% 25%',
        filter: 'hue-rotate(90deg) brightness(1.1)'
      },
      '50%': {
        backgroundPosition: '50% 100%',
        filter: 'hue-rotate(180deg) brightness(0.9)'
      },
      '75%': {
        backgroundPosition: '25% 0%',
        filter: 'hue-rotate(270deg) brightness(1.05)'
      },
      '100%': {
        backgroundPosition: '0% 50%',
        filter: 'hue-rotate(360deg) brightness(1)'
      }
    },
    animation: 'quantumConsciousness 8s ease-in-out infinite',
    backgroundSize: '400% 400%'
  } : {};

  // 2029 Quantum Brand text styles
  const getBrandTextStyle = () => {
    const baseStyle = {
      fontWeight: 800,
      letterSpacing: '-0.03em',
      fontFamily: 'var(--font-quantum)',
      fontVariationSettings: '"wght" 800',
      textShadow: consciousness ? '0 0 20px rgba(0, 255, 255, 0.3), 0 0 40px rgba(255, 0, 255, 0.2)' : 'none',
      ...animationStyles
    };

    switch (color) {
      case 'consciousness':
        return {
          ...baseStyle,
          background: colorStyle.textGradient,
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          filter: 'drop-shadow(0 0 10px rgba(0, 255, 255, 0.5))'
        };
      case 'quantum':
        return {
          ...baseStyle,
          background: colorStyle.textGradient,
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          filter: 'drop-shadow(0 0 8px rgba(0, 245, 255, 0.4))'
        };
      case 'white':
        return {
          ...baseStyle,
          color: '#FFFFFF',
          textShadow: `0 4px 8px ${alpha('#000000', 0.4)}, 0 0 20px rgba(255, 255, 255, 0.3)`
        };
      case 'inherit':
        return {
          ...baseStyle,
          color: 'inherit'
        };
      default:
        return {
          ...baseStyle,
          background: colorStyle.textGradient || colorStyle.gradient,
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text'
        };
    }
  };

  // 2029 Quantum Consciousness Slogan text styles
  const getSloganTextStyle = () => {
    const baseStyle = {
      fontWeight: 600,
      fontFamily: 'var(--font-quantum)',
      letterSpacing: '-0.01em',
      fontVariationSettings: '"wght" 600',
      textShadow: consciousness ? '0 0 10px rgba(0, 255, 255, 0.2)' : 'none'
    };

    switch (color) {
      case 'consciousness':
        return {
          ...baseStyle,
          background: `conic-gradient(from 45deg, ${alpha('#00ffff', 0.9)}, ${alpha('#ff00ff', 0.8)}, ${alpha('#ffff00', 0.9)})`,
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          filter: 'drop-shadow(0 0 5px rgba(0, 255, 255, 0.3))'
        };
      case 'quantum':
        return {
          ...baseStyle,
          background: `linear-gradient(135deg, ${alpha('#00f5ff', 0.9)}, ${alpha('#ff0066', 0.8)})`,
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          filter: 'drop-shadow(0 0 4px rgba(0, 245, 255, 0.2))'
        };
      case 'white':
        return {
          ...baseStyle,
          color: alpha('#FFFFFF', 0.95),
          textShadow: `0 2px 4px ${alpha('#000000', 0.4)}, 0 0 10px rgba(255, 255, 255, 0.2)`
        };
      case 'inherit':
        return {
          ...baseStyle,
          color: 'inherit'
        };
      default:
        return {
          ...baseStyle,
          background: colorStyle.textGradient || `linear-gradient(135deg, ${alpha(colorStyle.primary, 0.9)}, ${alpha(colorStyle.secondary || '#3B82F6', 0.8)})`,
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text'
        };
    }
  };

  // 2029 Quantum Consciousness Slogans
  const getSlogan = () => {
    if (consciousness || quantum) {
      return "Neural Agriculture • Quantum Intelligence • Conscious Farming";
    }
    return "Smart Farming, Smarter Decisions";
  };

  const brandTextStyle = getBrandTextStyle();
  const sloganTextStyle = getSloganTextStyle();

  if (variant === 'logo-only') {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', ...sx }}>
        <AgriIntelLogo size={config.logoSize} />
      </Box>
    );
  }

  if (variant === 'compact') {
    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          flexDirection: orientation === 'vertical' ? 'column' : 'row',
          textAlign: orientation === 'vertical' ? 'center' : 'left',
          gap: config.spacing,
          ...sx
        }}
      >
        <AgriIntelLogo size={config.logoSize} />
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: orientation === 'vertical' ? 'center' : 'flex-start' }}>
          <Typography
            variant="h4"
            component="span"
            sx={{
              fontSize: config.fontSize,
              ...brandTextStyle
            }}
          >
            AgriIntel
          </Typography>
          {showSlogan && (
            <Typography
              variant="caption"
              component="span"
              sx={{
                fontSize: config.sloganSize,
                ...sloganTextStyle
              }}
            >
              {getSlogan()}
            </Typography>
          )}
        </Box>
      </Box>
    );
  }

  // Full variant
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: orientation === 'vertical' ? 'column' : 'row',
        alignItems: orientation === 'vertical' ? 'center' : 'center',
        textAlign: orientation === 'vertical' ? 'center' : 'left',
        gap: config.spacing,
        ...sx
      }}
    >
      <AgriIntelLogo size={config.logoSize} />
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: orientation === 'vertical' ? 'center' : 'flex-start' }}>
        <Typography
          variant="h1"
          component="h1"
          sx={{
            fontSize: config.fontSize,
            lineHeight: 1.1,
            ...brandTextStyle
          }}
        >
          AgriIntel
        </Typography>
        {showSlogan && (
          <Typography
            variant="subtitle1"
            component="p"
            sx={{
              fontSize: config.sloganSize,
              lineHeight: 1.2,
              mt: 0.5,
              ...sloganTextStyle
            }}
          >
            {getSlogan()}
          </Typography>
        )}
      </Box>
    </Box>
  );
};

export default AgriIntelBrand;
