import React, { useState, useEffect } from 'react';
import styles from './AnimalManagement.module.css';
import axios from '../utils/axios';

interface Animal {
  _id: string;
  name: string;
  species: string;
  dob: string; // Date of Birth
  category: string;
}

const AnimalManagement: React.FC = () => {
  const [animals, setAnimals] = useState<Animal[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentAnimal, setCurrentAnimal] = useState<Animal | null>(null);
  const [newAnimal, setNewAnimal] = useState({ name: '', species: '', dob: '', category: '' });

  const fetchAnimals = async () => {
    try {
      const response = await axios.get('/animals');
      setAnimals(response.data.animals);
    } catch (error) {
      console.error('Error fetching animals:', error);
    }
  };

  useEffect(() => {
    fetchAnimals();
  }, []);

  const handleAddClick = () => {
    setCurrentAnimal(null);
    setNewAnimal({ name: '', species: '', dob: '', category: '' });
    setIsModalOpen(true);
  };

  const handleEditClick = (animal: Animal) => {
    setCurrentAnimal(animal);
    setNewAnimal(animal);
    setIsModalOpen(true);
  };

  const handleDeleteClick = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this animal?')) {
      try {
        await axios.delete(`/animals/${id}`);
        fetchAnimals();
      } catch (error) {
        console.error('Error deleting animal:', error);
      }
    }
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    if (name === 'dob') {
      setNewAnimal({ ...newAnimal, dob: new Date(value).toISOString() });
    } else {
      setNewAnimal({ ...newAnimal, [name]: value });
    }
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (currentAnimal) {
        await axios.put(`/animals/${currentAnimal._id}`, newAnimal);
      } else {
        await axios.post('/animals', newAnimal);
      }
      fetchAnimals();
      setIsModalOpen(false);
    } catch (error) {
      console.error('Error saving animal:', error);
    }
  };

  const getAgeInMonths = (dob: string) => {
    const birthDate = new Date(dob);
    const today = new Date();
    let months = (today.getFullYear() - birthDate.getFullYear()) * 12;
    months -= birthDate.getMonth();
    months += today.getMonth();
    return months <= 0 ? 0 : months;
  };

  const updateAnimalCategories = () => {
    // This is a premium feature. We can add a check here for subscription status.
    // For now, we'll assume the user is on a paid plan.

    const updatedAnimals = animals.map(animal => {
      const ageInMonths = getAgeInMonths(animal.dob);
      let newCategory = animal.category;

      if (animal.species === 'Cattle' && ageInMonths > 18 && animal.category === 'Calf') {
        newCategory = 'Heifer';
      }
      // Add more rules for other species or categories here
      // e.g., Sheep: Lamb -> Hogget -> Ewe/Ram
      // e.g., Goat: Kid -> Doeling/Buckling -> Doe/Buck

      return { ...animal, category: newCategory };
    });

    setAnimals(updatedAnimals);
    alert('Animal categories have been updated based on age.');
  };

  return (
    <div className={styles.pageContainer}>
      <aside className={styles.sidebar}>
        <h2 className={styles.sidebarTitle}>Animal Categories</h2>
        <ul className={styles.navList}>
          <li className={styles.navItem}><button className={styles.navLink}>Cattle</button></li>
          <li className={styles.navItem}><button className={styles.navLink}>Sheep</button></li>
          <li className={styles.navItem}><button className={styles.navLink}>Goats</button></li>
        </ul>
      </aside>
      <main className={styles.mainContent}>
        <h1 className={styles.mainTitle}>Animal Dashboard</h1>
        <button onClick={handleAddClick} className={`${styles.actionButton} ${styles.addButton}`}>
          Add New Animal
        </button>
        <button onClick={updateAnimalCategories} className={`${styles.actionButton} ${styles.updateButton}`}>
          Update Categories by Age
        </button>
        <div className={styles.tableContainer}>
          <table className={styles.table}>
            <thead>
              <tr className={styles.tableHeader}>
                <th className={styles.tableHeaderCell}>Name</th>
                <th className={styles.tableHeaderCell}>Species</th>
                <th className={styles.tableHeaderCell}>Category</th>
                <th className={styles.tableHeaderCell}>Age (Months)</th>
                <th className={styles.tableHeaderCell}>Actions</th>
              </tr>
            </thead>
            <tbody>
              {animals.map(animal => (
                <tr key={animal._id} className={styles.tableRow}>
                  <td className={styles.tableCell}>{animal.name}</td>
                  <td className={styles.tableCell}>{animal.species}</td>
                  <td className={styles.tableCell}>{animal.category}</td>
                  <td className={styles.tableCell}>{getAgeInMonths(animal.dob)}</td>
                  <td className={styles.actionCell}>
                    <button onClick={() => handleEditClick(animal)} className={styles.editButton}>Edit</button>
                    <button onClick={() => handleDeleteClick(animal._id)} className={styles.deleteButton}>Delete</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        {isModalOpen && (
          <div className={styles.modalOverlay}>
            <div className={styles.modalContent}>
              <h2>{currentAnimal ? 'Edit Animal' : 'Add Animal'}</h2>
              <form onSubmit={handleFormSubmit}>
                <div className={styles.formGroup}>
                  <label htmlFor="name">Name:</label>
                  <input id="name" type="text" name="name" value={newAnimal.name} onChange={handleFormChange} required className={styles.formInput} />
                </div>
                <div className={styles.formGroup}>
                  <label htmlFor="species">Species:</label>
                  <input id="species" type="text" name="species" value={newAnimal.species} onChange={handleFormChange} required className={styles.formInput} />
                </div>
                <div className={styles.formGroup}>
                  <label htmlFor="dob">Date of Birth:</label>
                  <input id="dob" type="date" name="dob" value={newAnimal.dob ? newAnimal.dob.split('T')[0] : ''} onChange={handleFormChange} required className={styles.formInput} />
                </div>
                <div className={styles.formGroup}>
                  <label htmlFor="category">Category:</label>
                  <input id="category" type="text" name="category" value={newAnimal.category} onChange={handleFormChange} required className={styles.formInput} />
                </div>
                <div className={styles.modalActions}>
                  <button type="button" onClick={handleModalClose} className={styles.cancelButton}>Cancel</button>
                  <button type="submit" className={styles.submitButton}>{currentAnimal ? 'Update' : 'Add'}</button>
                </div>
              </form>
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default AnimalManagement;