import React from 'react';
import UpgradePrompt from './UpgradePrompt';

const ComplianceUpgrade: React.FC = () => {
  const features = [
    'Regulatory compliance management',
    'Automated compliance reporting',
    'Document management system',
    'Audit trail and history',
    'Certification tracking',
    'Legal requirement updates',
    'Risk assessment tools',
    'Government submission assistance'
  ];

  return (
    <UpgradePrompt
      moduleName="Compliance Management"
      features={features}
      tier="Enterprise"
      price="R599/month"
    />
  );
};

export default ComplianceUpgrade;
