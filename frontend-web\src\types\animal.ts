export interface Animal {
  id: string; // MongoDB ObjectId or SQL primary key
  tagNumber: string; // Consistent tag format: [SPECIES_CODE]-[YEAR]-[SEQUENCE], e.g., CTL-2024-001
  name: string; // South African names (Sotho, Tswana, Afrikaans)
  type: string; // General type (e.g., Cattle, Sheep)
  species?: string; // Species (same as type, for backward compatibility)
  breed: string; // Specific breed
  gender: string; // Male or Female
  birthDate?: string | Date; // ISO date format or Date object
  weight?: number; // In kg
  status: string; // Active, Sold, Deceased, Quarantined, Breeding, Retired
  healthStatus: 'healthy' | 'sick' | 'injured' | 'pregnant';
  lastHealthCheck?: string | Date; // Last health check date
  location: string; // Farm location
  purchaseDate?: string | Date; // ISO date format or Date object
  purchasePrice?: number; // In ZAR (Rands)
  notes?: string; // Additional information
  parentId?: string; // Reference to parent animal ID (foreign key)
  parentTagNumber?: string; // Reference to parent animal tag (for easier querying)
  sireId?: string; // Father animal ID (foreign key)
  damId?: string; // Mother animal ID (foreign key)
  createdAt?: string | Date; // ISO date format or Date object
  updatedAt?: string | Date; // ISO date format or Date object
  species?: string; // Same as type, for backward compatibility
  imageUrl?: string; // Path to animal image
  rfidTag?: string; // RFID tag for tracking
  // Asset Management fields
  breedingCount?: number; // Number of times the animal has been bred
  retirementDate?: string | Date; // ISO date format or Date object - when the animal was retired
  retirementReason?: string; // Age, Breeding Limit, Health Issues, Performance, Other
  retirementNotes?: string; // Additional notes about retirement
  currentValue?: number; // Current estimated value in ZAR
  depreciationRate?: number; // Annual depreciation rate (%)
  maintenanceCosts?: number; // Total maintenance costs in ZAR
  revenueGenerated?: number; // Total revenue generated in ZAR
  roi?: number; // Return on investment (%)
}

export interface AnimalStats {
  totalAnimals: number;
  healthPercentage: number;
  pendingCheckups?: number;
  averageGrowthRate?: number;
  activeAnimals: number;
  inactiveAnimals: number;
  healthyAnimals: number;
  sickAnimals: number;
  injuredAnimals: number;
  pregnantAnimals: number;
  recentAdditions?: number;
  bySpecies: Record<string, number>;
  byStatus: Record<string, number>;
  byLocation?: Record<string, number>;
  byBreed: Record<string, number>;
  byHealth: {
    healthy: number;
    sick: number;
    injured: number;
    pregnant: number;
  };
  certifications?: any;
  inspections?: any;
  documents?: any;
  // Asset Management stats
  retiredAnimals?: number;
  nearingRetirement?: number;
  retirementByReason?: {
    age: number;
    breeding: number;
    health: number;
    other: number;
  };
  valueOfActiveAssets?: number;
  valueOfRetiredAssets?: number;
  totalAssetValue?: number;
  averageRoi?: number;
}

export interface Certification {
  id: string;
  title: string;
  type: string;
  status: string;
  category: string;
  issueDate: string;
  expiryDate: string;
  issuedBy: string;
  lastUpdated: string;
}

export interface Inspection {
  id: string;
  type: string;
  date: string;
  inspector: string;
  status: string;
  notes: string;
  nextInspection: string;
}

export interface Document {
  id: string;
  name: string;
  status: string;
  issueDate: string;
  expiryDate: string;
  issuedBy: string;
  issuer: string;
}


