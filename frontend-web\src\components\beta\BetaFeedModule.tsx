import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Grid,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
} from '@mui/material';
import {
  Add,
  Download,
  Restaurant,
  LocalDining,
  TrendingUp,
  Upgrade,
  PieChart,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { Pie<PERSON>hart as RechartsPie<PERSON>hart, Pie, Cell, ResponsiveContainer, Tooltip, Legend } from 'recharts';
import * as XLSX from 'xlsx';

interface FeedRecord {
  id: string;
  date: string;
  animalId: string;
  animalName: string;
  feedType: string;
  quantity: number;
  unit: string;
  cost: number;
  notes?: string;
}

interface BetaFeedModuleProps {
  userTier: 'beta' | 'professional' | 'enterprise';
}

const BetaFeedModule: React.FC<BetaFeedModuleProps> = ({ userTier }) => {
  const [feedRecords, setFeedRecords] = useState<FeedRecord[]>([]);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [newRecord, setNewRecord] = useState<Partial<FeedRecord>>({
    date: new Date().toISOString().split('T')[0],
    feedType: '',
    quantity: 0,
    unit: 'kg',
    cost: 0,
    animalId: '',
    animalName: '',
  });

  // Sample data for BETA demonstration
  useEffect(() => {
    const sampleData: FeedRecord[] = [
      {
        id: '1',
        date: '2024-01-15',
        animalId: 'C001',
        animalName: 'Bessie',
        feedType: 'Lucerne Hay',
        quantity: 25,
        unit: 'kg',
        cost: 350,
        notes: 'Morning feed'
      },
      {
        id: '2',
        date: '2024-01-15',
        animalId: 'C002',
        animalName: 'Daisy',
        feedType: 'Dairy Pellets',
        quantity: 15,
        unit: 'kg',
        cost: 180,
        notes: 'High protein feed'
      },
      {
        id: '3',
        date: '2024-01-14',
        animalId: 'C003',
        animalName: 'Molly',
        feedType: 'Maize Silage',
        quantity: 30,
        unit: 'kg',
        cost: 120,
      },
    ];
    setFeedRecords(sampleData);
  }, []);

  const handleAddRecord = () => {
    if (newRecord.animalName && newRecord.feedType && newRecord.quantity && newRecord.cost) {
      const record: FeedRecord = {
        id: Date.now().toString(),
        date: newRecord.date || new Date().toISOString().split('T')[0],
        animalId: newRecord.animalId || `A${Date.now()}`,
        animalName: newRecord.animalName,
        feedType: newRecord.feedType,
        quantity: Number(newRecord.quantity),
        unit: newRecord.unit || 'kg',
        cost: Number(newRecord.cost),
        notes: newRecord.notes,
      };
      setFeedRecords([...feedRecords, record]);
      setNewRecord({
        date: new Date().toISOString().split('T')[0],
        feedType: '',
        quantity: 0,
        unit: 'kg',
        cost: 0,
        animalId: '',
        animalName: '',
      });
      setShowAddDialog(false);
    }
  };

  const exportToExcel = () => {
    const worksheet = XLSX.utils.json_to_sheet(feedRecords);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Feed Records');
    XLSX.writeFile(workbook, 'AgriIntel_Feed_Report.xlsx');
  };

  const totalCost = feedRecords.reduce((sum, r) => sum + r.cost, 0);
  const totalQuantity = feedRecords.reduce((sum, r) => sum + r.quantity, 0);
  const averageCostPerKg = totalQuantity > 0 ? totalCost / totalQuantity : 0;

  // Feed type distribution for pie chart
  const feedTypeData = feedRecords.reduce((acc, record) => {
    const existing = acc.find(item => item.name === record.feedType);
    if (existing) {
      existing.value += record.cost;
    } else {
      acc.push({ name: record.feedType, value: record.cost });
    }
    return acc;
  }, [] as { name: string; value: number }[]);

  const COLORS = ['#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336'];

  return (
    <Box sx={{ p: 3 }}>
      {/* BETA Limitation Alert */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>BETA Feed Module:</strong> Basic feed recording with simple cost tracking and pie chart visualization. 
          Upgrade to Professional for advanced nutrition planning, automated scheduling, and inventory management.
        </Typography>
      </Alert>

      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" fontWeight="bold">
          Feed Management (BETA)
        </Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => setShowAddDialog(true)}
            sx={{ borderRadius: 2 }}
          >
            Add Feed Record
          </Button>
          <Button
            variant="outlined"
            startIcon={<Download />}
            onClick={exportToExcel}
            sx={{ borderRadius: 2 }}
          >
            Export Excel
          </Button>
        </Box>
      </Box>

      {/* Feed Summary Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={3}>
          <motion.div whileHover={{ y: -4 }}>
            <Card sx={{ borderRadius: 3, background: 'linear-gradient(135deg, #4CAF50, #66BB6A)' }}>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" color="white" fontWeight="bold">
                      R{totalCost.toLocaleString()}
                    </Typography>
                    <Typography variant="body2" color="rgba(255,255,255,0.8)">
                      Total Feed Cost
                    </Typography>
                  </Box>
                  <Restaurant sx={{ fontSize: 40, color: 'rgba(255,255,255,0.8)' }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={3}>
          <motion.div whileHover={{ y: -4 }}>
            <Card sx={{ borderRadius: 3, background: 'linear-gradient(135deg, #2196F3, #42A5F5)' }}>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" color="white" fontWeight="bold">
                      {totalQuantity}kg
                    </Typography>
                    <Typography variant="body2" color="rgba(255,255,255,0.8)">
                      Total Quantity
                    </Typography>
                  </Box>
                  <LocalDining sx={{ fontSize: 40, color: 'rgba(255,255,255,0.8)' }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={3}>
          <motion.div whileHover={{ y: -4 }}>
            <Card sx={{ borderRadius: 3, background: 'linear-gradient(135deg, #FF9800, #FFB74D)' }}>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" color="white" fontWeight="bold">
                      R{averageCostPerKg.toFixed(2)}
                    </Typography>
                    <Typography variant="body2" color="rgba(255,255,255,0.8)">
                      Avg Cost/kg
                    </Typography>
                  </Box>
                  <TrendingUp sx={{ fontSize: 40, color: 'rgba(255,255,255,0.8)' }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={3}>
          <motion.div whileHover={{ y: -4 }}>
            <Card sx={{ borderRadius: 3, background: 'linear-gradient(135deg, #9C27B0, #BA68C8)' }}>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" color="white" fontWeight="bold">
                      {feedRecords.length}
                    </Typography>
                    <Typography variant="body2" color="rgba(255,255,255,0.8)">
                      Feed Records
                    </Typography>
                  </Box>
                  <PieChart sx={{ fontSize: 40, color: 'rgba(255,255,255,0.8)' }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
      </Grid>

      {/* Charts and Upgrade Section */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={8}>
          <Card sx={{ borderRadius: 3, height: '400px' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" mb={2}>
                Feed Cost Distribution by Type
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <RechartsPieChart>
                  <Pie
                    data={feedTypeData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {feedTypeData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`R${value}`, 'Cost']} />
                  <Legend />
                </RechartsPieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ borderRadius: 3, height: '400px' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="between" mb={2}>
                <Typography variant="h6" fontWeight="bold">
                  Upgrade to Professional
                </Typography>
                <Upgrade sx={{ color: '#FF9800' }} />
              </Box>
              <Typography variant="body2" color="text.secondary" mb={2}>
                Unlock advanced feed management:
              </Typography>
              <Box component="ul" sx={{ pl: 2, mb: 2 }}>
                <Typography component="li" variant="body2" mb={1}>
                  Automated Feed Scheduling
                </Typography>
                <Typography component="li" variant="body2" mb={1}>
                  Nutrition Analysis & Planning
                </Typography>
                <Typography component="li" variant="body2" mb={1}>
                  Feed Inventory Management
                </Typography>
                <Typography component="li" variant="body2" mb={1}>
                  Cost Optimization Alerts
                </Typography>
                <Typography component="li" variant="body2" mb={1}>
                  Integration with Financial Module
                </Typography>
              </Box>
              <Button
                variant="contained"
                fullWidth
                sx={{
                  background: 'linear-gradient(135deg, #FF9800, #FFB74D)',
                  borderRadius: 2,
                  mt: 2,
                }}
              >
                Upgrade Now - R299/month
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Recent Feed Records Table */}
      <Card sx={{ borderRadius: 3 }}>
        <CardContent>
          <Typography variant="h6" fontWeight="bold" mb={2}>
            Recent Feed Records
          </Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Date</TableCell>
                  <TableCell>Animal</TableCell>
                  <TableCell>Feed Type</TableCell>
                  <TableCell>Quantity</TableCell>
                  <TableCell>Cost</TableCell>
                  <TableCell>Notes</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {feedRecords.slice(-10).reverse().map((record) => (
                  <TableRow key={record.id}>
                    <TableCell>{record.date}</TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {record.animalName}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {record.animalId}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip label={record.feedType} size="small" color="primary" />
                    </TableCell>
                    <TableCell>
                      {record.quantity} {record.unit}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold" color="success.main">
                        R{record.cost.toLocaleString()}
                      </Typography>
                    </TableCell>
                    <TableCell>{record.notes || '-'}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Add Feed Record Dialog */}
      <Dialog open={showAddDialog} onClose={() => setShowAddDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add Feed Record</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Date"
                type="date"
                value={newRecord.date}
                onChange={(e) => setNewRecord({ ...newRecord, date: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Animal Name"
                value={newRecord.animalName}
                onChange={(e) => setNewRecord({ ...newRecord, animalName: e.target.value })}
                placeholder="e.g., Bessie, Daisy"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Feed Type"
                value={newRecord.feedType}
                onChange={(e) => setNewRecord({ ...newRecord, feedType: e.target.value })}
                placeholder="e.g., Lucerne Hay, Dairy Pellets, Maize Silage"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Quantity"
                type="number"
                value={newRecord.quantity}
                onChange={(e) => setNewRecord({ ...newRecord, quantity: Number(e.target.value) })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Unit</InputLabel>
                <Select
                  value={newRecord.unit || 'kg'}
                  onChange={(e) => setNewRecord({ ...newRecord, unit: e.target.value })}
                  label="Unit"
                >
                  <MenuItem value="kg">kg</MenuItem>
                  <MenuItem value="bales">bales</MenuItem>
                  <MenuItem value="bags">bags</MenuItem>
                  <MenuItem value="tons">tons</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Cost (R)"
                type="number"
                value={newRecord.cost}
                onChange={(e) => setNewRecord({ ...newRecord, cost: Number(e.target.value) })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes (Optional)"
                value={newRecord.notes}
                onChange={(e) => setNewRecord({ ...newRecord, notes: e.target.value })}
                placeholder="Additional notes about this feed record"
                multiline
                rows={2}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowAddDialog(false)}>Cancel</Button>
          <Button variant="contained" onClick={handleAddRecord}>
            Add Record
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BetaFeedModule;
