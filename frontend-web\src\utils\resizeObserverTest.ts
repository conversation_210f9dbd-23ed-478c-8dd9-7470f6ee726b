/**
 * ResizeObserver Test Utility
 * This utility helps test if ResizeObserver loop errors are properly suppressed
 */

export const testResizeObserverFix = (): Promise<boolean> => {
  return new Promise((resolve) => {
    let errorCaught = false;
    
    // Store original error handler
    const originalOnError = window.onerror;
    const originalConsoleError = console.error;
    
    // Set up error detection
    window.onerror = (message) => {
      if (message && message.toString().includes('ResizeObserver loop')) {
        errorCaught = true;
      }
      return true;
    };
    
    console.error = (...args) => {
      if (args.some(arg => typeof arg === 'string' && arg.includes('ResizeObserver loop'))) {
        errorCaught = true;
      }
    };
    
    // Create a test scenario that would normally trigger ResizeObserver loop errors
    const testContainer = document.createElement('div');
    testContainer.style.width = '100px';
    testContainer.style.height = '100px';
    testContainer.style.position = 'absolute';
    testContainer.style.top = '-1000px';
    testContainer.style.left = '-1000px';
    document.body.appendChild(testContainer);
    
    // Create a ResizeObserver that would normally cause a loop
    const observer = new ResizeObserver((entries) => {
      // This would normally cause a loop by modifying the element being observed
      entries.forEach((entry) => {
        const target = entry.target as HTMLElement;
        if (target.style.width === '100px') {
          target.style.width = '101px';
        } else {
          target.style.width = '100px';
        }
      });
    });
    
    observer.observe(testContainer);
    
    // Wait for potential errors
    setTimeout(() => {
      // Clean up
      observer.disconnect();
      document.body.removeChild(testContainer);
      
      // Restore original handlers
      window.onerror = originalOnError;
      console.error = originalConsoleError;
      
      // Return true if no errors were caught (meaning they were suppressed)
      resolve(!errorCaught);
    }, 1000);
  });
};

export const logResizeObserverTestResult = async (): Promise<void> => {
  try {
    const isFixed = await testResizeObserverFix();
    if (isFixed) {
      console.log('✅ ResizeObserver loop errors are properly suppressed');
    } else {
      console.warn('⚠️ ResizeObserver loop errors are still occurring');
    }
  } catch (error) {
    console.error('❌ Error testing ResizeObserver fix:', error);
  }
};

// Auto-run test in development mode
if (process.env.NODE_ENV === 'development') {
  // Run test after a delay to ensure all fixes are applied
  setTimeout(() => {
    logResizeObserverTestResult();
  }, 2000);
}
