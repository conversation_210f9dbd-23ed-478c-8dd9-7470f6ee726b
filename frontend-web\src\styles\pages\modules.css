/**
 * Modules Page Styles - AgriIntel
 * Styling for livestock management modules
 */

/* Module Container */
.modules-container {
  min-height: 100vh;
  background: var(--color-background-primary);
  padding: var(--spacing-lg);
}

/* Module Grid */
.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

/* Module Card */
.module-card {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  transition: var(--transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.module-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border-color: var(--color-primary);
}

.module-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--agri-pro-gradient);
  opacity: 0;
  transition: var(--transition-normal);
}

.module-card:hover::before {
  opacity: 1;
}

/* Module Header */
.module-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.module-icon {
  width: 48px;
  height: 48px;
  color: var(--color-primary);
  background: rgba(46, 125, 50, 0.1);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
}

.module-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--color-text-primary);
  margin: 0;
}

/* Module Content */
.module-description {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-lg);
}

.module-features {
  list-style: none;
  padding: 0;
  margin: 0 0 var(--spacing-lg) 0;
}

.module-feature {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.module-feature-icon {
  width: 16px;
  height: 16px;
  color: var(--color-success);
}

/* Module Footer */
.module-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--glass-border);
}

.module-status {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
}

.module-status.available {
  background: rgba(34, 197, 94, 0.1);
  color: var(--color-success);
}

.module-status.beta {
  background: rgba(245, 124, 0, 0.1);
  color: var(--color-warning);
}

.module-status.premium {
  background: rgba(59, 130, 246, 0.1);
  color: var(--color-info);
}

.module-status.locked {
  background: rgba(107, 114, 128, 0.1);
  color: var(--color-text-secondary);
}

/* Module Actions */
.module-action {
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
}

.module-action.primary {
  background: var(--color-primary);
  color: var(--color-white);
}

.module-action.primary:hover {
  background: var(--color-primary-dark);
}

.module-action.secondary {
  background: transparent;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
}

.module-action.secondary:hover {
  background: var(--color-primary);
  color: var(--color-white);
}

/* Module Categories */
.module-categories {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
  flex-wrap: wrap;
}

.module-category {
  padding: var(--spacing-sm) var(--spacing-lg);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-full);
  background: var(--color-background-secondary);
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
}

.module-category:hover,
.module-category.active {
  background: var(--color-primary);
  color: var(--color-white);
  border-color: var(--color-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .modules-container {
    padding: var(--spacing-md);
  }
  
  .modules-grid {
    grid-template-columns: 1fr;
  }
  
  .module-categories {
    justify-content: center;
  }
  
  .module-footer {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: stretch;
  }
}

@media (max-width: 480px) {
  .module-header {
    flex-direction: column;
    text-align: center;
  }
  
  .module-categories {
    flex-direction: column;
  }
}
