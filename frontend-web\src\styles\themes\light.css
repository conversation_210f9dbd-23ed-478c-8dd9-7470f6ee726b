/**
 * Light Theme - AgriIntel
 * Light color scheme for the application
 */

:root[data-theme="light"] {
  /* Primary Colors */
  --color-primary: #2E7D32;
  --color-primary-dark: #1B5E20;
  --color-primary-light: #4CAF50;
  --color-primary-lighter: #81C784;

  /* Secondary Colors */
  --color-secondary: #1565C0;
  --color-secondary-dark: #0D47A1;
  --color-secondary-light: #2196F3;

  /* Accent Colors */
  --color-accent: #F57C00;
  --color-accent-dark: #E65100;
  --color-accent-light: #FF9800;

  /* Background Colors */
  --color-background-primary: #FFFFFF;
  --color-background-secondary: #F8F9FA;
  --color-background-tertiary: #F1F3F4;
  --color-background-hover: #F5F5F5;

  /* Text Colors */
  --color-text-primary: #212121;
  --color-text-secondary: #757575;
  --color-text-tertiary: #9E9E9E;
  --color-text-inverse: #FFFFFF;

  /* Border Colors */
  --color-border: #E0E0E0;
  --color-border-light: #F0F0F0;
  --color-border-dark: #BDBDBD;

  /* Status Colors */
  --color-success: #4CAF50;
  --color-warning: #FF9800;
  --color-error: #F44336;
  --color-info: #2196F3;

  /* Utility Colors */
  --color-white: #FFFFFF;
  --color-black: #000000;
  --color-focus: #2196F3;

  /* Glass Morphism */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-backdrop: blur(20px);
  --glass-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);

  /* Gradients */
  --agri-pro-gradient: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
  --agri-beta-gradient: linear-gradient(135deg, #F57C00 0%, #FF9800 100%);
  --agri-premium-gradient: linear-gradient(135deg, #1565C0 0%, #2196F3 100%);

  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);

  /* Card Backgrounds */
  --card-bg: #FFFFFF;
  --card-border: #E0E0E0;
  --card-shadow: var(--shadow-md);

  /* Navigation */
  --nav-bg: rgba(255, 255, 255, 0.95);
  --nav-border: rgba(0, 0, 0, 0.1);

  /* Sidebar */
  --sidebar-bg: #FFFFFF;
  --sidebar-border: #E0E0E0;
  --sidebar-item-hover: #F5F5F5;
  --sidebar-item-active: #E8F5E8;

  /* Form Elements */
  --input-bg: #FFFFFF;
  --input-border: #E0E0E0;
  --input-border-focus: #2E7D32;
  --input-placeholder: #9E9E9E;

  /* Buttons */
  --btn-primary-bg: var(--color-primary);
  --btn-primary-hover: var(--color-primary-dark);
  --btn-secondary-bg: var(--color-secondary);
  --btn-secondary-hover: var(--color-secondary-dark);

  /* Tables */
  --table-header-bg: #F8F9FA;
  --table-border: #E0E0E0;
  --table-row-hover: #F5F5F5;
  --table-row-selected: #E8F5E8;

  /* Charts */
  --chart-grid: #F0F0F0;
  --chart-text: #757575;
  --chart-primary: #2E7D32;
  --chart-secondary: #1565C0;
  --chart-accent: #F57C00;

  /* Scrollbar */
  --scrollbar-track: #F1F1F1;
  --scrollbar-thumb: #C1C1C1;
  --scrollbar-thumb-hover: #A8A8A8;
}

/* Light theme specific overrides */
.light-theme {
  background-color: var(--color-background-primary);
  color: var(--color-text-primary);
}

.light-theme .card {
  background-color: var(--card-bg);
  border-color: var(--card-border);
  box-shadow: var(--card-shadow);
}

.light-theme .nav {
  background-color: var(--nav-bg);
  border-color: var(--nav-border);
}

.light-theme .sidebar {
  background-color: var(--sidebar-bg);
  border-color: var(--sidebar-border);
}

.light-theme .btn-primary {
  background-color: var(--btn-primary-bg);
}

.light-theme .btn-primary:hover {
  background-color: var(--btn-primary-hover);
}

.light-theme .table {
  border-color: var(--table-border);
}

.light-theme .table thead {
  background-color: var(--table-header-bg);
}

.light-theme .table tbody tr:hover {
  background-color: var(--table-row-hover);
}

/* Scrollbar styling for light theme */
.light-theme ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.light-theme ::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  border-radius: 4px;
}

.light-theme ::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 4px;
}

.light-theme ::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}
