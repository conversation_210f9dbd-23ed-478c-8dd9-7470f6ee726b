/**
 * Enhanced Access Control System for AgriIntel
 * Provides strict separation between BETA and Live features
 */

import { User } from '../types/user';

export type AccessLevel = 'beta' | 'professional' | 'enterprise' | 'admin';
export type FeatureCategory = 'core' | 'premium' | 'enterprise' | 'admin';

export interface ModuleConfig {
  id: string;
  name: string;
  category: FeatureCategory;
  requiredLevel: AccessLevel;
  betaAccess: boolean;
  description: string;
  features: string[];
  limitations?: {
    beta?: string[];
    professional?: string[];
  };
}

export interface FeatureLimit {
  name: string;
  beta: number;
  professional: number;
  enterprise: number;
  admin: number;
}

// Module configurations with strict access control
export const moduleConfigurations: Record<string, ModuleConfig> = {
  dashboard: {
    id: 'dashboard',
    name: 'Dashboard',
    category: 'core',
    requiredLevel: 'beta',
    betaAccess: true,
    description: 'Main dashboard with overview statistics',
    features: ['overview', 'quick_stats', 'recent_activities']
  },
  
  animals: {
    id: 'animals',
    name: 'Animal Management',
    category: 'core',
    requiredLevel: 'beta',
    betaAccess: true,
    description: 'Basic animal record management',
    features: ['view_animals', 'add_animals', 'basic_search'],
    limitations: {
      beta: ['Max 50 animals', 'Basic search only', 'No bulk operations'],
      professional: ['Unlimited animals', 'Advanced search', 'Bulk operations']
    }
  },
  
  health: {
    id: 'health',
    name: 'Health Management',
    category: 'core',
    requiredLevel: 'beta',
    betaAccess: true,
    description: 'Basic health record tracking',
    features: ['view_health_records', 'add_health_records', 'vaccination_tracking'],
    limitations: {
      beta: ['Basic health records only', 'Manual entry only'],
      professional: ['Advanced health analytics', 'Automated alerts', 'Veterinary integration']
    }
  },
  
  resources: {
    id: 'resources',
    name: 'Resources',
    category: 'core',
    requiredLevel: 'beta',
    betaAccess: true,
    description: 'Educational resources and government information',
    features: ['educational_content', 'government_resources', 'auction_listings']
  },
  
  breeding: {
    id: 'breeding',
    name: 'Breeding Management',
    category: 'premium',
    requiredLevel: 'professional',
    betaAccess: false,
    description: 'Advanced breeding program management',
    features: ['breeding_programs', 'genetic_tracking', 'mating_schedules', 'pregnancy_monitoring']
  },
  
  financial: {
    id: 'financial',
    name: 'Financial Management',
    category: 'premium',
    requiredLevel: 'professional',
    betaAccess: false,
    description: 'Complete financial tracking and analysis',
    features: ['income_tracking', 'expense_management', 'profit_analysis', 'tax_preparation']
  },
  
  inventory: {
    id: 'inventory',
    name: 'Inventory Management',
    category: 'premium',
    requiredLevel: 'professional',
    betaAccess: false,
    description: 'Feed, supplies, and equipment management',
    features: ['inventory_tracking', 'automated_reordering', 'supplier_management', 'cost_optimization']
  },
  
  commercial: {
    id: 'commercial',
    name: 'Commercial Management',
    category: 'enterprise',
    requiredLevel: 'enterprise',
    betaAccess: false,
    description: 'Market analysis and sales management',
    features: ['market_analysis', 'sales_management', 'customer_relations', 'contract_management']
  },
  
  analytics: {
    id: 'analytics',
    name: 'Business Analytics',
    category: 'enterprise',
    requiredLevel: 'enterprise',
    betaAccess: false,
    description: 'Advanced analytics and business intelligence',
    features: ['predictive_analytics', 'custom_dashboards', 'data_visualization', 'ai_insights']
  },
  
  compliance: {
    id: 'compliance',
    name: 'Compliance Management',
    category: 'enterprise',
    requiredLevel: 'enterprise',
    betaAccess: false,
    description: 'Regulatory compliance and documentation',
    features: ['regulatory_tracking', 'audit_trails', 'document_management', 'compliance_reporting']
  }
};

// Feature limits by subscription tier
export const featureLimits: Record<string, FeatureLimit> = {
  maxAnimals: { name: 'Maximum Animals', beta: 50, professional: 1000, enterprise: 10000, admin: -1 },
  maxHealthRecords: { name: 'Health Records per Month', beta: 100, professional: 1000, enterprise: 10000, admin: -1 },
  maxReports: { name: 'Reports per Month', beta: 5, professional: 50, enterprise: 500, admin: -1 },
  maxUsers: { name: 'Team Members', beta: 1, professional: 5, enterprise: 25, admin: -1 },
  maxFarms: { name: 'Farm Locations', beta: 1, professional: 3, enterprise: 10, admin: -1 },
  apiCalls: { name: 'API Calls per Month', beta: 1000, professional: 10000, enterprise: 100000, admin: -1 }
};

/**
 * Get user's access level
 */
export const getUserAccessLevel = (user: User | null): AccessLevel => {
  if (!user) return 'beta';
  
  if (user.role === 'admin' || user.username === 'May Rakgama') return 'admin';
  if (user.username === 'Pro' || user.subscriptionTier === 'Professional') return 'professional';
  return 'beta';
};

/**
 * Check if user can access a module
 */
export const canAccessModule = (user: User | null, moduleId: string): boolean => {
  if (!user) return false;
  
  const module = moduleConfigurations[moduleId];
  if (!module) return false;
  
  const userLevel = getUserAccessLevel(user);
  const levelHierarchy: AccessLevel[] = ['beta', 'professional', 'enterprise', 'admin'];
  
  const userLevelIndex = levelHierarchy.indexOf(userLevel);
  const requiredLevelIndex = levelHierarchy.indexOf(module.requiredLevel);
  
  return userLevelIndex >= requiredLevelIndex;
};

/**
 * Check if user is in BETA mode
 */
export const isBetaUser = (user: User | null): boolean => {
  if (!user) return true;
  return getUserAccessLevel(user) === 'beta' || user.role === 'beta' || user.username === 'Demo';
};

/**
 * Get available modules for user
 */
export const getAvailableModules = (user: User | null): ModuleConfig[] => {
  if (!user) return [];
  
  return Object.values(moduleConfigurations).filter(module => 
    canAccessModule(user, module.id)
  );
};

/**
 * Get locked modules for user (for upgrade prompts)
 */
export const getLockedModules = (user: User | null): ModuleConfig[] => {
  if (!user) return Object.values(moduleConfigurations);
  
  return Object.values(moduleConfigurations).filter(module => 
    !canAccessModule(user, module.id)
  );
};

/**
 * Get feature limit for user
 */
export const getFeatureLimit = (user: User | null, featureName: string): number => {
  if (!user) return featureLimits[featureName]?.beta || 0;
  
  const userLevel = getUserAccessLevel(user);
  const limit = featureLimits[featureName];
  
  if (!limit) return 0;
  
  return limit[userLevel] || 0;
};

/**
 * Check if user has reached feature limit
 */
export const hasReachedLimit = (user: User | null, featureName: string, currentUsage: number): boolean => {
  const limit = getFeatureLimit(user, featureName);
  if (limit === -1) return false; // Unlimited
  return currentUsage >= limit;
};

/**
 * Get upgrade path for user
 */
export const getUpgradePath = (user: User | null): { current: AccessLevel; next: AccessLevel | null; price: string } => {
  const currentLevel = getUserAccessLevel(user);
  
  const upgradePaths = {
    beta: { next: 'professional' as AccessLevel, price: 'R299/month' },
    professional: { next: 'enterprise' as AccessLevel, price: 'R599/month' },
    enterprise: { next: null, price: '' },
    admin: { next: null, price: '' }
  };
  
  const path = upgradePaths[currentLevel];
  return {
    current: currentLevel,
    next: path.next,
    price: path.price
  };
};

/**
 * Get module restrictions for user
 */
export const getModuleRestrictions = (user: User | null, moduleId: string): string[] => {
  if (!user) return ['Login required'];
  
  const module = moduleConfigurations[moduleId];
  if (!module) return ['Module not found'];
  
  if (!canAccessModule(user, moduleId)) {
    const upgradePath = getUpgradePath(user);
    return [`Requires ${module.requiredLevel} subscription`, `Upgrade to ${upgradePath.next} for ${upgradePath.price}`];
  }
  
  const userLevel = getUserAccessLevel(user);
  return module.limitations?.[userLevel] || [];
};

/**
 * Validate route access
 */
export const validateRouteAccess = (user: User | null, route: string): { allowed: boolean; reason?: string; redirectTo?: string } => {
  if (!user) {
    return { 
      allowed: false, 
      reason: 'Authentication required', 
      redirectTo: route.includes('beta') ? '/beta-login' : '/login' 
    };
  }
  
  // Extract module from route
  const pathSegments = route.split('/').filter(Boolean);
  const moduleId = pathSegments[pathSegments.length - 1] || 'dashboard';
  
  // Check if it's a beta route
  const isBetaRoute = route.includes('beta');
  const userIsBeta = isBetaUser(user);
  
  // Enforce strict separation
  if (isBetaRoute && !userIsBeta) {
    return { 
      allowed: false, 
      reason: 'Beta route accessed by non-beta user', 
      redirectTo: '/dashboard' 
    };
  }
  
  if (!isBetaRoute && userIsBeta) {
    return { 
      allowed: false, 
      reason: 'Live route accessed by beta user', 
      redirectTo: '/beta-dashboard' 
    };
  }
  
  // Check module access
  if (!canAccessModule(user, moduleId)) {
    return { 
      allowed: false, 
      reason: `Access denied to ${moduleId} module`, 
      redirectTo: isBetaRoute ? '/beta-dashboard' : '/dashboard' 
    };
  }
  
  return { allowed: true };
};

export default {
  moduleConfigurations,
  featureLimits,
  getUserAccessLevel,
  canAccessModule,
  isBetaUser,
  getAvailableModules,
  getLockedModules,
  getFeatureLimit,
  hasReachedLimit,
  getUpgradePath,
  getModuleRestrictions,
  validateRouteAccess
};
