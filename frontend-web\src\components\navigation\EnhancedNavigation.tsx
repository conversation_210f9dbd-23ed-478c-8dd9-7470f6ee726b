/**
 * Enhanced Navigation Component
 * Handles routing conflicts and provides consistent navigation
 */

import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Breadcrumbs,
  Typography,
  Link,
  Alert,
  Snackbar,
  useTheme,
  alpha
} from '@mui/material';
import {
  NavigateNext,
  Warning,
  Info
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import {
  resolveRoute,
  getBreadcrumbs,
  isValidRoute,
  navigateWithGuards
} from '../../utils/routingManager';

interface NavigationState {
  hasConflict: boolean;
  conflictMessage?: string;
  suggestedPath?: string;
}

const EnhancedNavigation: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  const theme = useTheme();
  
  const [navigationState, setNavigationState] = React.useState<NavigationState>({
    hasConflict: false
  });

  // Check for routing conflicts on location change
  useEffect(() => {
    const currentPath = location.pathname;
    const resolution = resolveRoute(currentPath, user);
    
    if (resolution.shouldRedirect) {
      setNavigationState({
        hasConflict: true,
        conflictMessage: resolution.reason,
        suggestedPath: resolution.path
      });
      
      // Auto-redirect after a short delay
      const timer = setTimeout(() => {
        navigateWithGuards(navigate, resolution.path, user, { replace: true });
        setNavigationState({ hasConflict: false });
      }, 2000);
      
      return () => clearTimeout(timer);
    } else {
      setNavigationState({ hasConflict: false });
    }
  }, [location.pathname, user, navigate]);

  // Get breadcrumbs for current path
  const breadcrumbs = getBreadcrumbs(location.pathname, user);

  const handleBreadcrumbClick = (path: string) => {
    navigateWithGuards(navigate, path, user);
  };

  const handleConflictResolve = () => {
    if (navigationState.suggestedPath) {
      navigateWithGuards(navigate, navigationState.suggestedPath, user, { replace: true });
    }
    setNavigationState({ hasConflict: false });
  };

  const handleConflictDismiss = () => {
    setNavigationState({ hasConflict: false });
  };

  return (
    <>
      {/* Breadcrumb Navigation */}
      {breadcrumbs.length > 1 && (
        <Box
          sx={{
            py: 2,
            px: 3,
            background: alpha(theme.palette.background.paper, 0.8),
            backdropFilter: 'blur(8px)',
            borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`
          }}
        >
          <Breadcrumbs
            separator={<NavigateNext fontSize="small" />}
            aria-label="breadcrumb"
            sx={{
              '& .MuiBreadcrumbs-separator': {
                color: theme.palette.text.secondary
              }
            }}
          >
            {breadcrumbs.map((crumb, index) => {
              const isLast = index === breadcrumbs.length - 1;
              
              if (isLast) {
                return (
                  <Typography
                    key={crumb.path}
                    color="text.primary"
                    fontWeight="bold"
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}
                  >
                    {crumb.name}
                  </Typography>
                );
              }
              
              return (
                <Link
                  key={crumb.path}
                  color="inherit"
                  onClick={() => handleBreadcrumbClick(crumb.path)}
                  sx={{
                    cursor: 'pointer',
                    textDecoration: 'none',
                    '&:hover': {
                      textDecoration: 'underline',
                      color: theme.palette.primary.main
                    }
                  }}
                >
                  {crumb.name}
                </Link>
              );
            })}
          </Breadcrumbs>
        </Box>
      )}

      {/* Route Conflict Alert */}
      <Snackbar
        open={navigationState.hasConflict}
        autoHideDuration={6000}
        onClose={handleConflictDismiss}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          severity="warning"
          onClose={handleConflictDismiss}
          action={
            navigationState.suggestedPath && (
              <Link
                color="inherit"
                onClick={handleConflictResolve}
                sx={{
                  cursor: 'pointer',
                  textDecoration: 'underline',
                  fontWeight: 'bold'
                }}
              >
                Go to {navigationState.suggestedPath}
              </Link>
            )
          }
          sx={{
            background: alpha(theme.palette.warning.main, 0.9),
            color: 'white',
            '& .MuiAlert-icon': {
              color: 'white'
            }
          }}
        >
          <Typography variant="body2" fontWeight="bold">
            Navigation Conflict Detected
          </Typography>
          <Typography variant="body2">
            {navigationState.conflictMessage}
          </Typography>
        </Alert>
      </Snackbar>
    </>
  );
};

/**
 * Navigation Guard Hook
 * Provides navigation functions with built-in conflict resolution
 */
export const useNavigationGuard = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  const guardedNavigate = (path: string, options?: { replace?: boolean; state?: any }) => {
    navigateWithGuards(navigate, path, user, options);
  };

  const checkRoute = (path: string) => {
    return isValidRoute(path, user);
  };

  const resolveConflicts = (path: string) => {
    return resolveRoute(path, user);
  };

  return {
    navigate: guardedNavigate,
    checkRoute,
    resolveConflicts
  };
};

/**
 * Route Validator Component
 * Validates current route and shows warnings for invalid routes
 */
export const RouteValidator: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const location = useLocation();
  const { user } = useAuth();
  const theme = useTheme();
  
  const isValid = isValidRoute(location.pathname, user);
  const resolution = resolveRoute(location.pathname, user);

  if (!isValid && resolution.shouldRedirect) {
    return (
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: `linear-gradient(135deg, ${alpha(theme.palette.warning.main, 0.1)}, ${alpha(theme.palette.background.default, 0.9)})`,
          p: 3
        }}
      >
        <Alert
          severity="warning"
          icon={<Warning />}
          sx={{
            maxWidth: 600,
            background: alpha(theme.palette.background.paper, 0.9),
            backdropFilter: 'blur(20px)'
          }}
        >
          <Typography variant="h6" fontWeight="bold" gutterBottom>
            Route Validation Error
          </Typography>
          <Typography variant="body1" gutterBottom>
            {resolution.reason}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            You will be redirected to: {resolution.path}
          </Typography>
        </Alert>
      </Box>
    );
  }

  return (
    <>
      <EnhancedNavigation />
      {children}
    </>
  );
};

/**
 * Navigation Status Component
 * Shows current navigation status and any conflicts
 */
export const NavigationStatus: React.FC = () => {
  const location = useLocation();
  const { user } = useAuth();
  const theme = useTheme();
  
  const isValid = isValidRoute(location.pathname, user);
  const resolution = resolveRoute(location.pathname, user);

  return (
    <Box
      sx={{
        position: 'fixed',
        bottom: 16,
        right: 16,
        zIndex: 1000,
        background: alpha(theme.palette.background.paper, 0.9),
        backdropFilter: 'blur(8px)',
        border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
        borderRadius: 2,
        p: 2,
        minWidth: 200,
        maxWidth: 300
      }}
    >
      <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
        Navigation Status
      </Typography>
      
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
        {isValid ? (
          <Info color="success" />
        ) : (
          <Warning color="warning" />
        )}
        <Typography variant="body2">
          {isValid ? 'Route Valid' : 'Route Conflict'}
        </Typography>
      </Box>
      
      <Typography variant="caption" color="text.secondary">
        Current: {location.pathname}
      </Typography>
      
      {!isValid && (
        <Typography variant="caption" color="warning.main" display="block">
          Suggested: {resolution.path}
        </Typography>
      )}
    </Box>
  );
};

export default EnhancedNavigation;
