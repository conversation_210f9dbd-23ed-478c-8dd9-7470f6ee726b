import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface SubscriptionPlan {
  id: string;
  name: string;
  displayName: string;
  description: string;
  price: {
    monthly: number;
    yearly: number;
    currency: string;
  };
  features: {
    maxAnimals: number;
    maxUsers: number;
    historicalDataMonths: number;
    maxReports: number;
    maxAlerts: number;
    aiAgentMode: boolean;
    advancedAnalytics: boolean;
    financialIntelligence: boolean;
    apiAccess: boolean;
    prioritySupport: boolean;
    customReports: boolean;
    dataExport: {
      formats: string[];
    };
    governmentIntegration: boolean;
    weatherIntegration: boolean;
    mobileApp: boolean;
  };
}

interface UserSubscription {
  id: string;
  userId: string;
  plan: SubscriptionPlan;
  status: 'active' | 'cancelled' | 'expired' | 'trial' | 'suspended';
  startDate: Date;
  endDate: Date;
  trialEndDate?: Date;
  billingCycle: 'monthly' | 'yearly';
  usage: {
    currentAnimals: number;
    currentUsers: number;
    monthlyAlerts: number;
    monthlyReports: number;
    lastResetDate: Date;
  };
  conversionTracking?: {
    betaStartDate?: Date;
    conversionDate?: Date;
    conversionSource?: string;
    interactionHistory: Array<{
      action: string;
      timestamp: Date;
      metadata?: any;
    }>;
  };
}

interface SubscriptionContextType {
  subscription: UserSubscription | null;
  plans: SubscriptionPlan[];
  isLoading: boolean;
  error: string | null;

  // Feature access methods
  canAccessFeature: (feature: string, currentUsage?: any) => Promise<{
    allowed: boolean;
    reason?: string;
    upgradeRequired?: boolean;
  }>;
  checkFeatureAccess: (feature: string) => boolean; // Synchronous version for quick checks

  // Usage tracking
  trackFeatureUsage: (feature: string, action: string, metadata?: any) => Promise<void>;
  updateUsage: (usageType: string, increment?: number) => Promise<void>;

  // Subscription management
  upgradeSubscription: (planName: string, billingCycle?: 'monthly' | 'yearly') => Promise<void>;
  cancelSubscription: (reason?: string) => Promise<void>;
  reactivateSubscription: () => Promise<void>;

  // Conversion tracking
  getConversionMetrics: () => Promise<any>;

  // Utility methods
  refreshSubscription: () => Promise<void>;
  isTrialExpiring: () => boolean;
  getDaysLeft: () => number;
  getUsagePercentage: (usageType: string) => number;
}

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined);

export const useSubscription = () => {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
};

interface SubscriptionProviderProps {
  children: ReactNode;
}

export const SubscriptionProvider: React.FC<SubscriptionProviderProps> = ({ children }) => {
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    initializeSubscription();
  }, []);

  const initializeSubscription = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Fetch user subscription and available plans
      const [subscriptionResponse, plansResponse] = await Promise.all([
        fetch('/api/subscription/current'),
        fetch('/api/subscription/plans')
      ]);

      if (subscriptionResponse.ok) {
        const subscriptionData = await subscriptionResponse.json();
        setSubscription(subscriptionData);
      }

      if (plansResponse.ok) {
        const plansData = await plansResponse.json();
        setPlans(plansData);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load subscription data');
      console.error('Error initializing subscription:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const canAccessFeature = async (feature: string, currentUsage?: any) => {
    try {
      const response = await fetch('/api/subscription/check-access', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ feature, currentUsage })
      });

      if (!response.ok) {
        throw new Error('Failed to check feature access');
      }

      return await response.json();
    } catch (err) {
      console.error('Error checking feature access:', err);
      return { allowed: false, reason: 'Error checking permissions' };
    }
  };

  // Synchronous version for quick UI checks
  const checkFeatureAccess = (feature: string): boolean => {
    // Get current user from localStorage to avoid circular dependency
    const userStr = localStorage.getItem('user');
    const user = userStr ? JSON.parse(userStr) : null;

    if (!user) return false;

    // Admin users have access to everything
    if (user.role === 'admin' || user.username === 'May Rakgama') {
      return true;
    }

    // Beta users have limited access - ONLY 5 BASIC MODULES
    if (user.role === 'beta' || user.username === 'Demo') {
      const betaFeatures = [
        'dashboard',
        'animals',
        'animals_basic',
        'animals_view',
        'animals_create',
        'health',
        'health_basic',
        'health_view',
        'health_create',
        'feeding',
        'feeding_basic',
        'feeding_view',
        'feeding_create',
        'financial',
        'financial_basic',
        'financial_view',
        'financial_create',
        'resources'
      ];
      return betaFeatures.includes(feature);
    }

    // Professional users have access to most features
    if (user.username === 'Pro' || user.subscriptionTier === 'Professional') {
      const professionalFeatures = [
        'dashboard',
        'animals_full',
        'health_full',
        'breeding_full',
        'feeding_full',
        'financial_full',
        'inventory_full',
        'reports_advanced',
        'resources',
        'settings'
      ];
      return professionalFeatures.includes(feature);
    }

    // Enterprise users have access to all features
    if (user.role === 'enterprise') {
      return true;
    }

    return false;
  };

  const trackFeatureUsage = async (feature: string, action: string, metadata?: any) => {
    try {
      await fetch('/api/subscription/track-usage', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ feature, action, metadata })
      });
    } catch (err) {
      console.error('Error tracking feature usage:', err);
    }
  };

  const updateUsage = async (usageType: string, increment: number = 1) => {
    try {
      const response = await fetch('/api/subscription/update-usage', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ usageType, increment })
      });

      if (response.ok) {
        const updatedSubscription = await response.json();
        setSubscription(updatedSubscription);
      }
    } catch (err) {
      console.error('Error updating usage:', err);
    }
  };

  const upgradeSubscription = async (planName: string, billingCycle: 'monthly' | 'yearly' = 'monthly') => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/subscription/upgrade', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ planName, billingCycle })
      });

      if (!response.ok) {
        throw new Error('Failed to upgrade subscription');
      }

      const updatedSubscription = await response.json();
      setSubscription(updatedSubscription);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to upgrade subscription');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const cancelSubscription = async (reason?: string) => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/subscription/cancel', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ reason })
      });

      if (!response.ok) {
        throw new Error('Failed to cancel subscription');
      }

      const updatedSubscription = await response.json();
      setSubscription(updatedSubscription);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to cancel subscription');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const reactivateSubscription = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/subscription/reactivate', {
        method: 'POST'
      });

      if (!response.ok) {
        throw new Error('Failed to reactivate subscription');
      }

      const updatedSubscription = await response.json();
      setSubscription(updatedSubscription);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to reactivate subscription');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const getConversionMetrics = async () => {
    try {
      const response = await fetch('/api/subscription/conversion-metrics');
      if (!response.ok) {
        throw new Error('Failed to fetch conversion metrics');
      }
      return await response.json();
    } catch (err) {
      console.error('Error fetching conversion metrics:', err);
      return null;
    }
  };

  const refreshSubscription = async () => {
    await initializeSubscription();
  };

  const isTrialExpiring = (): boolean => {
    if (!subscription || !subscription.trialEndDate) return false;
    const daysLeft = getDaysLeft();
    return daysLeft <= 7 && daysLeft > 0;
  };

  const getDaysLeft = (): number => {
    if (!subscription) return 0;
    const endDate = subscription.trialEndDate || subscription.endDate;
    const now = new Date();
    const end = new Date(endDate);
    const diffTime = end.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const getUsagePercentage = (usageType: string): number => {
    if (!subscription) return 0;
    
    const { usage, plan } = subscription;
    const { features } = plan;
    
    switch (usageType) {
      case 'animals':
        return features.maxAnimals === -1 ? 0 : (usage.currentAnimals / features.maxAnimals) * 100;
      case 'reports':
        return features.maxReports === -1 ? 0 : (usage.monthlyReports / features.maxReports) * 100;
      case 'alerts':
        return features.maxAlerts === -1 ? 0 : (usage.monthlyAlerts / features.maxAlerts) * 100;
      default:
        return 0;
    }
  };

  const value: SubscriptionContextType = {
    subscription,
    plans,
    isLoading,
    error,
    canAccessFeature,
    checkFeatureAccess,
    trackFeatureUsage,
    updateUsage,
    upgradeSubscription,
    cancelSubscription,
    reactivateSubscription,
    getConversionMetrics,
    refreshSubscription,
    isTrialExpiring,
    getDaysLeft,
    getUsagePercentage
  };

  return (
    <SubscriptionContext.Provider value={value}>
      {children}
    </SubscriptionContext.Provider>
  );
};
