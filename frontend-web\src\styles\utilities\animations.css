/**
 * Animation Utilities - AgriIntel
 * Animation and transition utility classes
 */

/* Transition Utilities */
.transition-none { transition: none !important; }
.transition-all { transition: all var(--transition-normal) !important; }
.transition-colors { transition: color var(--transition-normal), background-color var(--transition-normal), border-color var(--transition-normal) !important; }
.transition-opacity { transition: opacity var(--transition-normal) !important; }
.transition-shadow { transition: box-shadow var(--transition-normal) !important; }
.transition-transform { transition: transform var(--transition-normal) !important; }

/* Transition Duration */
.duration-75 { transition-duration: 75ms !important; }
.duration-100 { transition-duration: 100ms !important; }
.duration-150 { transition-duration: 150ms !important; }
.duration-200 { transition-duration: 200ms !important; }
.duration-300 { transition-duration: 300ms !important; }
.duration-500 { transition-duration: 500ms !important; }
.duration-700 { transition-duration: 700ms !important; }
.duration-1000 { transition-duration: 1000ms !important; }

/* Transition Timing Function */
.ease-linear { transition-timing-function: linear !important; }
.ease-in { transition-timing-function: cubic-bezier(0.4, 0, 1, 1) !important; }
.ease-out { transition-timing-function: cubic-bezier(0, 0, 0.2, 1) !important; }
.ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important; }

/* Transform Utilities */
.transform { transform: var(--transform) !important; }
.transform-none { transform: none !important; }
.transform-gpu { transform: translate3d(0, 0, 0) !important; }

/* Scale */
.scale-0 { transform: scale(0) !important; }
.scale-50 { transform: scale(0.5) !important; }
.scale-75 { transform: scale(0.75) !important; }
.scale-90 { transform: scale(0.9) !important; }
.scale-95 { transform: scale(0.95) !important; }
.scale-100 { transform: scale(1) !important; }
.scale-105 { transform: scale(1.05) !important; }
.scale-110 { transform: scale(1.1) !important; }
.scale-125 { transform: scale(1.25) !important; }
.scale-150 { transform: scale(1.5) !important; }

/* Rotate */
.rotate-0 { transform: rotate(0deg) !important; }
.rotate-1 { transform: rotate(1deg) !important; }
.rotate-2 { transform: rotate(2deg) !important; }
.rotate-3 { transform: rotate(3deg) !important; }
.rotate-6 { transform: rotate(6deg) !important; }
.rotate-12 { transform: rotate(12deg) !important; }
.rotate-45 { transform: rotate(45deg) !important; }
.rotate-90 { transform: rotate(90deg) !important; }
.rotate-180 { transform: rotate(180deg) !important; }
.-rotate-1 { transform: rotate(-1deg) !important; }
.-rotate-2 { transform: rotate(-2deg) !important; }
.-rotate-3 { transform: rotate(-3deg) !important; }
.-rotate-6 { transform: rotate(-6deg) !important; }
.-rotate-12 { transform: rotate(-12deg) !important; }
.-rotate-45 { transform: rotate(-45deg) !important; }
.-rotate-90 { transform: rotate(-90deg) !important; }
.-rotate-180 { transform: rotate(-180deg) !important; }

/* Translate */
.translate-x-0 { transform: translateX(0) !important; }
.translate-x-1 { transform: translateX(0.25rem) !important; }
.translate-x-2 { transform: translateX(0.5rem) !important; }
.translate-x-3 { transform: translateX(0.75rem) !important; }
.translate-x-4 { transform: translateX(1rem) !important; }
.translate-x-5 { transform: translateX(1.25rem) !important; }
.translate-x-6 { transform: translateX(1.5rem) !important; }
.translate-x-8 { transform: translateX(2rem) !important; }
.translate-x-10 { transform: translateX(2.5rem) !important; }
.translate-x-12 { transform: translateX(3rem) !important; }
.translate-x-16 { transform: translateX(4rem) !important; }
.translate-x-20 { transform: translateX(5rem) !important; }
.translate-x-24 { transform: translateX(6rem) !important; }
.translate-x-32 { transform: translateX(8rem) !important; }
.translate-x-40 { transform: translateX(10rem) !important; }
.translate-x-48 { transform: translateX(12rem) !important; }
.translate-x-56 { transform: translateX(14rem) !important; }
.translate-x-64 { transform: translateX(16rem) !important; }
.translate-x-full { transform: translateX(100%) !important; }
.-translate-x-1 { transform: translateX(-0.25rem) !important; }
.-translate-x-2 { transform: translateX(-0.5rem) !important; }
.-translate-x-3 { transform: translateX(-0.75rem) !important; }
.-translate-x-4 { transform: translateX(-1rem) !important; }
.-translate-x-5 { transform: translateX(-1.25rem) !important; }
.-translate-x-6 { transform: translateX(-1.5rem) !important; }
.-translate-x-8 { transform: translateX(-2rem) !important; }
.-translate-x-10 { transform: translateX(-2.5rem) !important; }
.-translate-x-12 { transform: translateX(-3rem) !important; }
.-translate-x-16 { transform: translateX(-4rem) !important; }
.-translate-x-20 { transform: translateX(-5rem) !important; }
.-translate-x-24 { transform: translateX(-6rem) !important; }
.-translate-x-32 { transform: translateX(-8rem) !important; }
.-translate-x-40 { transform: translateX(-10rem) !important; }
.-translate-x-48 { transform: translateX(-12rem) !important; }
.-translate-x-56 { transform: translateX(-14rem) !important; }
.-translate-x-64 { transform: translateX(-16rem) !important; }
.-translate-x-full { transform: translateX(-100%) !important; }

.translate-y-0 { transform: translateY(0) !important; }
.translate-y-1 { transform: translateY(0.25rem) !important; }
.translate-y-2 { transform: translateY(0.5rem) !important; }
.translate-y-3 { transform: translateY(0.75rem) !important; }
.translate-y-4 { transform: translateY(1rem) !important; }
.translate-y-5 { transform: translateY(1.25rem) !important; }
.translate-y-6 { transform: translateY(1.5rem) !important; }
.translate-y-8 { transform: translateY(2rem) !important; }
.translate-y-10 { transform: translateY(2.5rem) !important; }
.translate-y-12 { transform: translateY(3rem) !important; }
.translate-y-16 { transform: translateY(4rem) !important; }
.translate-y-20 { transform: translateY(5rem) !important; }
.translate-y-24 { transform: translateY(6rem) !important; }
.translate-y-32 { transform: translateY(8rem) !important; }
.translate-y-40 { transform: translateY(10rem) !important; }
.translate-y-48 { transform: translateY(12rem) !important; }
.translate-y-56 { transform: translateY(14rem) !important; }
.translate-y-64 { transform: translateY(16rem) !important; }
.translate-y-full { transform: translateY(100%) !important; }
.-translate-y-1 { transform: translateY(-0.25rem) !important; }
.-translate-y-2 { transform: translateY(-0.5rem) !important; }
.-translate-y-3 { transform: translateY(-0.75rem) !important; }
.-translate-y-4 { transform: translateY(-1rem) !important; }
.-translate-y-5 { transform: translateY(-1.25rem) !important; }
.-translate-y-6 { transform: translateY(-1.5rem) !important; }
.-translate-y-8 { transform: translateY(-2rem) !important; }
.-translate-y-10 { transform: translateY(-2.5rem) !important; }
.-translate-y-12 { transform: translateY(-3rem) !important; }
.-translate-y-16 { transform: translateY(-4rem) !important; }
.-translate-y-20 { transform: translateY(-5rem) !important; }
.-translate-y-24 { transform: translateY(-6rem) !important; }
.-translate-y-32 { transform: translateY(-8rem) !important; }
.-translate-y-40 { transform: translateY(-10rem) !important; }
.-translate-y-48 { transform: translateY(-12rem) !important; }
.-translate-y-56 { transform: translateY(-14rem) !important; }
.-translate-y-64 { transform: translateY(-16rem) !important; }
.-translate-y-full { transform: translateY(-100%) !important; }

/* Hover Animations */
.hover-scale-105:hover { transform: scale(1.05) !important; }
.hover-scale-110:hover { transform: scale(1.1) !important; }
.hover-scale-125:hover { transform: scale(1.25) !important; }
.hover-scale-95:hover { transform: scale(0.95) !important; }

.hover-translate-y-1:hover { transform: translateY(-0.25rem) !important; }
.hover-translate-y-2:hover { transform: translateY(-0.5rem) !important; }
.hover-translate-y-3:hover { transform: translateY(-0.75rem) !important; }

/* Keyframe Animations */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes zoomIn {
  from {
    transform: scale(0);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
  20%, 40%, 60%, 80% { transform: translateX(10px); }
}

/* Animation Classes */
.animate-spin { animation: spin 1s linear infinite; }
.animate-ping { animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite; }
.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.animate-bounce { animation: bounce 1s infinite; }

.animate-fade-in { animation: fadeIn 0.5s ease-in-out; }
.animate-fade-out { animation: fadeOut 0.5s ease-in-out; }
.animate-slide-in-up { animation: slideInUp 0.5s ease-out; }
.animate-slide-in-down { animation: slideInDown 0.5s ease-out; }
.animate-slide-in-left { animation: slideInLeft 0.5s ease-out; }
.animate-slide-in-right { animation: slideInRight 0.5s ease-out; }
.animate-zoom-in { animation: zoomIn 0.3s ease-out; }
.animate-shake { animation: shake 0.5s ease-in-out; }

/* Animation Delays */
.animate-delay-75 { animation-delay: 75ms; }
.animate-delay-100 { animation-delay: 100ms; }
.animate-delay-150 { animation-delay: 150ms; }
.animate-delay-200 { animation-delay: 200ms; }
.animate-delay-300 { animation-delay: 300ms; }
.animate-delay-500 { animation-delay: 500ms; }
.animate-delay-700 { animation-delay: 700ms; }
.animate-delay-1000 { animation-delay: 1000ms; }

/* Animation Fill Mode */
.animate-fill-none { animation-fill-mode: none; }
.animate-fill-forwards { animation-fill-mode: forwards; }
.animate-fill-backwards { animation-fill-mode: backwards; }
.animate-fill-both { animation-fill-mode: both; }

/* Animation Iteration Count */
.animate-once { animation-iteration-count: 1; }
.animate-twice { animation-iteration-count: 2; }
.animate-infinite { animation-iteration-count: infinite; }

/* Animation Play State */
.animate-paused { animation-play-state: paused; }
.animate-running { animation-play-state: running; }
