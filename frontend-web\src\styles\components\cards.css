/**
 * Card Component Styles
 * Professional card designs for AgriIntel platform
 */

/* ===== BASE CARD STYLES ===== */

.agri-card {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--glass-shadow);
  padding: var(--spacing-lg);
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.agri-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--agri-primary);
}

.agri-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--agri-pro-gradient);
  opacity: 0;
  transition: var(--transition-normal);
}

.agri-card:hover::before {
  opacity: 1;
}

/* ===== CARD VARIANTS ===== */

.agri-card-primary {
  border-color: var(--agri-primary);
  background: linear-gradient(135deg, 
    rgba(21, 101, 192, 0.05) 0%, 
    rgba(255, 255, 255, 0.1) 100%);
}

.agri-card-secondary {
  border-color: var(--agri-secondary);
  background: linear-gradient(135deg, 
    rgba(46, 125, 50, 0.05) 0%, 
    rgba(255, 255, 255, 0.1) 100%);
}

.agri-card-accent {
  border-color: var(--agri-accent);
  background: linear-gradient(135deg, 
    rgba(245, 124, 0, 0.05) 0%, 
    rgba(255, 255, 255, 0.1) 100%);
}

.agri-card-beta {
  border-color: var(--agri-beta-primary);
  background: linear-gradient(135deg, 
    rgba(255, 183, 77, 0.05) 0%, 
    rgba(255, 255, 255, 0.1) 100%);
}

.agri-card-pro {
  border-color: var(--agri-pro-primary);
  background: linear-gradient(135deg, 
    rgba(102, 187, 106, 0.05) 0%, 
    rgba(255, 255, 255, 0.1) 100%);
}

/* ===== CARD SIZES ===== */

.agri-card-sm {
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
}

.agri-card-md {
  padding: var(--spacing-lg);
  border-radius: var(--radius-xl);
}

.agri-card-lg {
  padding: var(--spacing-xl);
  border-radius: var(--radius-2xl);
}

/* ===== CARD HEADER ===== */

.agri-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
}

.agri-card-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.agri-card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: var(--spacing-xs) 0 0 0;
}

.agri-card-icon {
  width: 24px;
  height: 24px;
  color: var(--agri-primary);
}

.agri-card-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* ===== CARD CONTENT ===== */

.agri-card-content {
  color: var(--color-text-primary);
  line-height: var(--line-height-relaxed);
}

.agri-card-content p {
  margin-bottom: var(--spacing-md);
}

.agri-card-content p:last-child {
  margin-bottom: 0;
}

/* ===== CARD FOOTER ===== */

.agri-card-footer {
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.agri-card-footer-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.agri-card-footer-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* ===== INTERACTIVE CARDS ===== */

.agri-card-interactive {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.agri-card-interactive:hover {
  transform: translateY(-6px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.agri-card-interactive:active {
  transform: translateY(-2px);
}

/* ===== STAT CARDS ===== */

.agri-stat-card {
  text-align: center;
  padding: var(--spacing-xl);
}

.agri-stat-value {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--agri-primary);
  margin-bottom: var(--spacing-sm);
  display: block;
}

.agri-stat-label {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.agri-stat-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-sm);
  font-size: var(--font-size-sm);
}

.agri-stat-trend.positive {
  color: var(--color-success);
}

.agri-stat-trend.negative {
  color: var(--color-error);
}

/* ===== FEATURE CARDS ===== */

.agri-feature-card {
  text-align: center;
  padding: var(--spacing-xl);
}

.agri-feature-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--spacing-lg);
  padding: var(--spacing-lg);
  background: var(--agri-pro-gradient);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
}

.agri-feature-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
}

.agri-feature-description {
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
}

/* ===== PRICING CARDS ===== */

.agri-pricing-card {
  text-align: center;
  position: relative;
  overflow: hidden;
}

.agri-pricing-card.featured {
  border-color: var(--agri-primary);
  transform: scale(1.05);
  z-index: 1;
}

.agri-pricing-card.featured::before {
  opacity: 1;
}

.agri-pricing-badge {
  position: absolute;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  background: var(--agri-accent);
  color: var(--color-white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
}

.agri-pricing-plan {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-sm);
}

.agri-pricing-price {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--agri-primary);
  margin-bottom: var(--spacing-xs);
}

.agri-pricing-period {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-lg);
}

.agri-pricing-features {
  list-style: none;
  padding: 0;
  margin: var(--spacing-lg) 0;
}

.agri-pricing-features li {
  padding: var(--spacing-sm) 0;
  color: var(--color-text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

.agri-pricing-features li::before {
  content: '✓';
  color: var(--color-success);
  font-weight: var(--font-weight-bold);
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
  .agri-card {
    padding: var(--spacing-md);
  }
  
  .agri-card-lg {
    padding: var(--spacing-lg);
  }
  
  .agri-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
  
  .agri-card-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
  
  .agri-pricing-card.featured {
    transform: none;
  }
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  .agri-card:hover,
  .agri-card-interactive:hover {
    transform: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .agri-card {
    background: var(--color-background-primary);
    border: 2px solid var(--color-text-primary);
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
  }
  
  .agri-card::before {
    display: none;
  }
}
